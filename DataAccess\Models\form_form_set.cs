﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace DataAccess.Models;

/// <summary>
/// 表单集
/// </summary>
public partial class form_form_set
{
    /// <summary>
    /// 表单集ID
    /// </summary>
    public Guid id { get; set; }

    /// <summary>
    /// 表单集名称
    /// </summary>
    public string name { get; set; }

    /// <summary>
    /// 表单集描述
    /// </summary>
    public string description { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime created_at { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime updated_at { get; set; }

    /// <summary>
    /// 项目ID
    /// </summary>
    public Guid project_id { get; set; }

    /// <summary>
    /// 项目名称
    /// </summary>
    public string project_name { get; set; }

    /// <summary>
    /// 所属病区ID
    /// </summary>
    public Guid ward_id { get; set; }

    /// <summary>
    /// 所属病区名称
    /// </summary>
    public string ward_name { get; set; }

    /// <summary>
    /// 所属科室ID
    /// </summary>
    public Guid department_id { get; set; }

    /// <summary>
    /// 所属科室名称
    /// </summary>
    public string department_name { get; set; }

    /// <summary>
    /// 所属医院ID
    /// </summary>
    public Guid hospital_id { get; set; }

    /// <summary>
    /// 所属医院名称
    /// </summary>
    public string hospital_name { get; set; }

    /// <summary>
    /// 表单集类型
    /// </summary>
    public string type { get; set; }

    public virtual sys_department department { get; set; }

    public virtual ICollection<form_card> form_card { get; set; } = new List<form_card>();

    public virtual ICollection<form_form> form_form { get; set; } = new List<form_form>();

    public virtual ICollection<form_linker_rule> form_linker_rule { get; set; } = new List<form_linker_rule>();

    public virtual ICollection<form_question> form_question { get; set; } = new List<form_question>();

    public virtual sys_hospital hospital { get; set; }

    public virtual form_project project { get; set; }

    public virtual sys_ward ward { get; set; }
}