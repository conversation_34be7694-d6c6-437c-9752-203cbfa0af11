CREATE TABLE "Form"."Sys_DataPermission" (
    "Id" uuid NOT NULL,
    "RoleId" uuid NOT NULL,
    "RoleName" text NOT NULL,
    "PermissionType" text NOT NULL,
    "HospitalId" uuid NOT NULL,
    "HospitalName" text NOT NULL,
    "DepartmentId" uuid NOT NULL,
    "DepartmentName" text NOT NULL,
    "WardId" uuid NOT NULL,
    "WardName" text NOT NULL,
    "ProjectId" uuid NOT NULL,
    "ProjectName" text NOT NULL,
    "FormSetId" uuid NOT NULL,
    "FormSetName" text NOT NULL,
    "FormId" uuid NOT NULL,
    "FormName" text NOT NULL,
    "CardId" uuid NOT NULL,
    "CardName" text NOT NULL,
    "CreatedAt" timestamp(6) NOT NULL DEFAULT now(),
    "UpdatedAt" timestamp(6) NOT NULL DEFAULT now(),
    CONSTRAINT "PK_Sys_DataPermission" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_Sys_DataPermission_RoleId" FOREIGN KEY ("RoleId") REFERENCES "Form"."Sys_Role" ("Id"),
    CONSTRAINT "FK_Sys_DataPermission_HospitalId" FOREIGN KEY ("HospitalId") REFERENCES "Form"."Sys_Hospital" ("Id"),
    CONSTRAINT "FK_Sys_DataPermission_DepartmentId" FOREIGN KEY ("DepartmentId") REFERENCES "Form"."Sys_Department" ("Id"),
    CONSTRAINT "FK_Sys_DataPermission_WardId" FOREIGN KEY ("WardId") REFERENCES "Form"."Sys_Ward" ("Id"),
    CONSTRAINT "FK_Sys_DataPermission_ProjectId" FOREIGN KEY ("ProjectId") REFERENCES "Form"."Sys_Project" ("Id"),
    CONSTRAINT "FK_Sys_DataPermission_FormSetId" FOREIGN KEY ("FormSetId") REFERENCES "Form"."Sys_FormSet" ("Id"),
    CONSTRAINT "FK_Sys_DataPermission_FormId" FOREIGN KEY ("FormId") REFERENCES "Form"."Sys_Form" ("Id"),
    CONSTRAINT "FK_Sys_DataPermission_CardId" FOREIGN KEY ("CardId") REFERENCES "Form"."Sys_Card" ("Id")
)

COMMENT ON TABLE "Form"."Sys_DataPermission" IS '数据权限';
COMMENT ON COLUMN "Form"."Sys_DataPermission"."RoleId" IS '角色ID';
COMMENT ON COLUMN "Form"."Sys_DataPermission"."RoleName" IS '角色名称';
COMMENT ON COLUMN "Form"."Sys_DataPermission"."PermissionType" IS '权限类型';
COMMENT ON COLUMN "Form"."Sys_DataPermission"."HospitalId" IS '医院ID';
COMMENT ON COLUMN "Form"."Sys_DataPermission"."HospitalName" IS '医院名称';
COMMENT ON COLUMN "Form"."Sys_DataPermission"."DepartmentId" IS '科室ID';
COMMENT ON COLUMN "Form"."Sys_DataPermission"."DepartmentName" IS '科室名称';
COMMENT ON COLUMN "Form"."Sys_DataPermission"."WardId" IS '病区ID';
COMMENT ON COLUMN "Form"."Sys_DataPermission"."WardName" IS '病区名称';
COMMENT ON COLUMN "Form"."Sys_DataPermission"."ProjectId" IS '项目ID';
COMMENT ON COLUMN "Form"."Sys_DataPermission"."ProjectName" IS '项目名称';
COMMENT ON COLUMN "Form"."Sys_DataPermission"."FormSetId" IS '表单集ID';
COMMENT ON COLUMN "Form"."Sys_DataPermission"."FormSetName" IS '表单集名称';
COMMENT ON COLUMN "Form"."Sys_DataPermission"."FormId" IS '表单ID';
COMMENT ON COLUMN "Form"."Sys_DataPermission"."FormName" IS '表单名称';
COMMENT ON COLUMN "Form"."Sys_DataPermission"."CardId" IS '卡片ID';
COMMENT ON COLUMN "Form"."Sys_DataPermission"."CardName" IS '卡片名称';
COMMENT ON COLUMN "Form"."Sys_DataPermission"."CreatedAt" IS '创建时间';
COMMENT ON COLUMN "Form"."Sys_DataPermission"."UpdatedAt" IS '更新时间';
