﻿@inherits Cmp_Base
@inject ILogger<Cmp_Question_Selector> inj_logger

@if (_formset != null)
{
    <MudPaper>
        <div class="d-flex flex-wrap gap-2 mb-2">
            @{
                var formList = _formset.form_form.OrderBy(f 
                    => f.sort_index).ToList();
            }
            @foreach (var form in formList)
            {
                <MudButton Variant="@(form == _selectedForm ? Variant.Filled : Variant.Outlined)"
                           Color="MudBlazor.Color.Primary" Size="MudBlazor.Size.Small"
                           OnClick="@(() => SelectForm(form))">
                    @form.name
                </MudButton>
            }
        </div>

        @if (_selectedForm != null)
        {
            <div class="d-flex flex-wrap gap-2 mb-2">
                @{
                    var cardList = _selectedForm.form_card.OrderBy(c => c.name).ToList();
                }
                @foreach (var card in cardList)
                {
                    <MudButton Variant="@(card == _selectedCard ? Variant.Filled : Variant.Outlined)"
                               Color="MudBlazor.Color.Secondary" Size="MudBlazor.Size.Small"
                               OnClick="@(() => SelectCard(card))">
                        @card.name
                    </MudButton>
                }
            </div>

            <div class="d-flex justify-content-end mb-2">
                <MudButton Variant="Variant.Filled" Color="MudBlazor.Color.Success" Size="MudBlazor.Size.Small"
                           OnClick="@AddSelectedCard" Disabled="@(_selectedCard == null)">
                    添加卡片
                </MudButton>
            </div>
        }

        @if (_selectedCard != null)
        {
            <div class="d-flex flex-wrap gap-2 mb-2">
                @{
                    var questionList = _selectedCard.form_question.OrderBy(q => q.display_name).ToList();
                }
                @foreach (var question in questionList)
                {
                    <MudButton Variant="@(_selectedQuestionList.Contains(question) ? Variant.Filled : Variant.Outlined)"
                               Color="MudBlazor.Color.Tertiary" Size="MudBlazor.Size.Small"
                               OnClick="@(e => ToggleQuestionSelection(question))">
                        @question.display_name
                    </MudButton>
                }
            </div>

            <div class="d-flex justify-content-end mb-2">
                <MudButton Variant="Variant.Filled" Color="MudBlazor.Color.Success" Size="MudBlazor.Size.Small"
                           OnClick="@AddSelectedQuestions" Disabled="@(_selectedQuestionList.Count == 0)">
                    添加问题
                </MudButton>
            </div>
        }
    </MudPaper>
}


@code {
    [Parameter]
    public required form_form_set _formset { get; set; }

    [Parameter]
    public EventCallback<(List<Guid>, string)> AddQuestionEvent { get; set; }

    [Parameter]
    public EventCallback<(List<Guid>, string)> AddCardEvent { get; set; }

    [Parameter]
    public string ObjType { get; set; } = "publish"; // 默认为publish，可设置为subscribe

    private form_form? _selectedForm;
    private form_card? _selectedCard;
    private List<form_question> _selectedQuestionList = new();

    protected override async Task OnPageInitializedAsync()
    {

    }

    // 选择表单时清除卡片和问题的选择
    private void SelectForm(form_form form)
    {
        if (_selectedForm == form)
            return;

        _selectedForm = form;
        _selectedCard = null;
        _selectedQuestionList.Clear();
    }

    // 选择卡片时清除之前的问题选择
    private void SelectCard(form_card card)
    {
        if (_selectedCard == card)
            return;

        _selectedCard = card;
        _selectedQuestionList.Clear();
    }

    private void ToggleQuestionSelection(form_question question)
    {
        if (_selectedQuestionList.Contains(question))
        {
            _selectedQuestionList.Remove(question);
        }
        else
        {
            _selectedQuestionList.Add(question);
        }
    }

    // 添加选中的问题
    private async Task AddSelectedQuestions()
    {
        if (_selectedQuestionList.Count > 0)
        {
            List<Guid> questionIds = _selectedQuestionList.Select(q => q.id).ToList();
            await AddQuestionEvent.InvokeAsync((questionIds, ObjType));

            // 清空已选择的问题列表
            _selectedQuestionList.Clear();
        }
    }

    // 添加选中的卡片
    private async Task AddSelectedCard()
    {
        if (_selectedCard != null)
        {
            List<Guid> cardIds = new List<Guid> { _selectedCard.id };
            await AddCardEvent.InvokeAsync((cardIds, ObjType));
        }
    }
}