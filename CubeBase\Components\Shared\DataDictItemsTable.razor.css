.data-dict-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
    table-layout: fixed;
}
.data-dict-table th {
    background-color: #47887f;
    color: white;
    padding: 10px;
    text-align: left;
    position: sticky;
    top: 0;
    z-index: 1;
}
.data-dict-table td {
    border-bottom: 1px solid #e0e0e0;
    vertical-align: top;
}
.data-dict-table tr:hover {
    background-color: rgba(0, 0, 0, 0.04);
}
.data-dict-table .id-column {
    width: 120px;
}
.data-dict-table .name-column {
    font-family: Consolas, "Courier New", monospace;
    width: 360px;
    white-space: normal;
    word-wrap: break-word;
}
.data-dict-table .attr-column {
    width: 500px;
    word-break: break-word;
}
.data-dict-table .type-column {
    width: 100px;
    font-weight: 500;
}
.data-dict-table .match-result-column {
    width: 399px;
    word-break: break-word;
}
/* 匹配结果样式 */
.match-name-line {
    margin-bottom: 2px;
    font-weight: 500;
    color: #2e7d32;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
}

.suggest-name-line {
    margin-bottom: 2px;
    font-weight: 500;
    color: #0277bd; /* 蓝色 */
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
}

.require-name-line {
    margin-bottom: 2px;
    font-weight: bold;
    color: #d32f2f; /* 红色 */
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
}

.match-type-line {
    margin-bottom: 2px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.match-type {
    font-weight: bold;
    color: #1976d2;
    font-size: 13px;
}

.suggest-type {
    font-weight: bold;
    color: #0277bd; /* 蓝色 */
    font-size: 13px;
}

.require-type {
    font-weight: bold;
    color: #d32f2f; /* 红色 */
    font-size: 13px;
}

.node-id {
    color: #555;
    font-family: Consolas, "Courier New", monospace;
    font-size: 12px;
}

.data-dict-container {
    max-height: calc(100vh - 400px);
    overflow: auto;
    box-shadow: inset 0 -5px 5px -5px rgba(0,0,0,0.1);
    border-radius: 4px;
}

::deep .attr-name {
    font-weight: bold;
}

/* LLM分析结果样式 */
.llm-match {
    background-color: #e8f5e9;
    padding: 4px 8px;
    border-radius: 4px;
    border-left: 3px solid #2e7d32;
    margin-bottom: 4px;
}

.llm-potential-match {
    background-color: #fff8e1;
    padding: 4px 8px;
    border-radius: 4px;
    border-left: 3px solid #ff8f00;
    margin-bottom: 4px;
}

.llm-no-match {
    background-color: #ffebee; /* 红色背景 */
    padding: 4px 8px;
    border-radius: 4px;
    border-left: 3px solid #d32f2f;
    cursor: pointer;
    margin-bottom: 4px;
}

.llm-no-match .no-match-text {
    color: #d32f2f; /* 红色文本 */
    font-weight: bold;
}

.llm-error {
    background-color: #ffebee;
    padding: 4px 8px;
    border-radius: 4px;
    border-left: 3px solid #c62828;
    margin-bottom: 4px;
}

.llm-pending {
    background-color: #e3f2fd;
    padding: 4px 8px;
    border-radius: 4px;
    border-left: 3px solid #1976d2;
    margin-bottom: 4px;
    font-style: italic;
    color: #555;
}

.match-text, .potential-match-text, .no-match-text, .error-text {
    display: block;
    font-weight: 500;
}

.match-path {
    margin-top: 2px;
    color: #555;
    font-size: 0.85em;
    white-space: normal;
    word-break: break-word;
}

.match-type {
    display: block;
    font-size: 12px;
    color: #555;
}

/* 原生checkbox样式 */
.native-checkbox {
    width: 18px;
    height: 18px;
    cursor: pointer;
    accent-color: #1976d2; /* MudBlazor主色调 */
    border-radius: 2px;
    margin: 0;
    padding: 0;
    position: relative;
    vertical-align: middle;
}

/* 确保checkbox在容器中垂直居中 */
.llm-no-match .d-flex {
    min-height: 24px;
}

/* 移除旧的custom-checkbox样式 */
.custom-checkbox {
    width: 12px;
    height: 12px;
}

.suggestion-info {
    display: flex;
    flex-direction: column;
    margin-top: 2px;
}

.suggestion-info small {
    margin-bottom: 2px;
}