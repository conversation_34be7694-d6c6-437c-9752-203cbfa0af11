﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace DataAccess.Models;

public partial class form_card
{
    /// <summary>
    /// 题卡ID
    /// </summary>
    public Guid id { get; set; }

    /// <summary>
    /// 父级题卡ID
    /// </summary>
    public Guid? parent_id { get; set; }

    /// <summary>
    /// 题卡类型
    /// </summary>
    public string type { get; set; }

    /// <summary>
    /// 题卡名称
    /// </summary>
    public string name { get; set; }

    /// <summary>
    /// 题卡描述
    /// </summary>
    public string description { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime created_at { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime updated_at { get; set; }

    /// <summary>
    /// 表单ID
    /// </summary>
    public Guid form_id { get; set; }

    /// <summary>
    /// 表单名称
    /// </summary>
    public string form_name { get; set; }

    /// <summary>
    /// 表单集ID
    /// </summary>
    public Guid form_set_id { get; set; }

    /// <summary>
    /// 表单集名称
    /// </summary>
    public string form_set_name { get; set; }

    /// <summary>
    /// 项目ID
    /// </summary>
    public Guid project_id { get; set; }

    /// <summary>
    /// 项目名称
    /// </summary>
    public string project_name { get; set; }

    /// <summary>
    /// 所属病区ID
    /// </summary>
    public Guid ward_id { get; set; }

    /// <summary>
    /// 所属病区名称
    /// </summary>
    public string ward_name { get; set; }

    /// <summary>
    /// 所属科室ID
    /// </summary>
    public Guid department_id { get; set; }

    /// <summary>
    /// 所属科室名称
    /// </summary>
    public string department_name { get; set; }

    /// <summary>
    /// 所属医院ID
    /// </summary>
    public Guid hospital_id { get; set; }

    /// <summary>
    /// 所属医院名称
    /// </summary>
    public string hospital_name { get; set; }

    public Guid? pre_uid { get; set; }

    public Guid? next_uid { get; set; }

    /// <summary>
    /// 是否默认隐藏
    /// </summary>
    public bool is_hidden { get; set; }

    public virtual ICollection<form_card> Inverseparent { get; set; } = new List<form_card>();

    public virtual sys_department department { get; set; }

    public virtual form_form form { get; set; }

    public virtual ICollection<form_question> form_question { get; set; } = new List<form_question>();

    public virtual form_form_set form_set { get; set; }

    public virtual sys_hospital hospital { get; set; }

    public virtual form_card parent { get; set; }

    public virtual form_project project { get; set; }

    public virtual sys_ward ward { get; set; }
}