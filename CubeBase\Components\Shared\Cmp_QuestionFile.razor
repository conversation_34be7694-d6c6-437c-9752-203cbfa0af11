﻿@inherits Cmp_Base
@inject ILogger<Cmp_QuestionFile> inj_logger
@inject IMedDictService inj_medDictService
@inject IDbContextFactory<CubeContext> ContextFactory
@inject ISnackbar inj_snackbar


    <div>
    @if (Question.is_show_label)
    {
        @if (Question.is_tip_label)
        {
            <MudTooltip class="bio-question-tooltip" Placement="Placement.Right">
                <ChildContent>
                    <MudIcon Icon="@Icons.Material.Filled.QuestionMark" Color="Color.Warning" Size="Size.Small" Style="border:2px solid orange; border-radius:50%;"></MudIcon>
                </ChildContent>
                <TooltipContent>
                    <div class="bio-tooltip-content"> @((MarkupString)Question.label_text) </div>
                </TooltipContent>
            </MudTooltip>
        }
        else
        {
            <MudHtmlViewer Html="@Question.label_text" />
        }
    }
    @if (IsAdmin)
    {
        <div class="d-flex align-items-center">
            <div style="font-size: 1rem; color: #ad6200; font-weight: bold;">@_columnPathAndName</div>
            <div> - </div>
            <MudButton Class="ma-1 pa-2" Variant="Variant.Outlined" Color="Color.Info" Size="Size.Small" OnClick="() => _showMoveTools = !_showMoveTools">修改字典绑定</MudButton>
        </div>
        @if (_showMoveTools)
        {
            <Cascader TValue="string" Items="@MedDictItems" OnSelectedItemChanged="@OnSelectedItemChangedHandler" ParentSelectable="false" style="width: 600px;padding: 2px;" />
            <MudButton Class="ma-1 pa-2" Variant="Variant.Filled" Color="Color.Primary" OnClick="OnDoMergeColumn" Style="width:80px"> Move </MudButton>
        }
    }
    <div class="d-flex align-items-center">
        @if (Question.prefix_text != null && Question.prefix_text != "")
        {
            <span class="mr-2">@Question.prefix_text</span>
        }
        <div>
            <MudButton Variant="Variant.Outlined" StartIcon="@Icons.Material.Filled.Upload">上传文件</MudButton>
        </div>

        @if (Question.suffix_text != null && Question.suffix_text != "")
        {
            <span class="ml-2"> @Question.suffix_text</span>
        }
    </div>
</div>
@code {
    [Parameter]
    public required CubeContext _context { get; set; }
    [Parameter]
    public required form_question Question { get; set; }
    [CascadingParameter(Name = "MedDictItems")]
    public List<CascaderItem> MedDictItems { get; set; }
    [CascadingParameter(Name = "admin")]
    public bool IsAdmin { get; set; }
    [CascadingParameter(Name = "TablePathCache")]
    public Dictionary<Guid, string> TablePathCache { get; set; }
    [Parameter]
    public EventCallback OnSuccess { get; set; }

    private string _columnPathAndName = "正在加载...";
    private string _mergeToID;
    private Guid? _lastQuestionId;
    private bool _showMoveTools = false;

    private string _style
    {
        get
        {
            var width = Question.text_width == null ? 100 : Question.text_width;
            return $"width: {width}%;";
        }
    }

    protected override async Task OnParametersSetAsync()
    {
        // 只有当Question真正变化时才重新加载路径
        if (_lastQuestionId != Question?.id)
        {
            _lastQuestionId = Question?.id;
            if (IsAdmin)
            {
                await LoadColumnPathAsync();
            }
        }
    }

    private async Task LoadColumnPathAsync()
    {
        try
        {
            // 优先使用FormSetManager传递的路径缓存
            if (TablePathCache != null && TablePathCache.TryGetValue(Question.table_definition_id, out var cachedPath))
            {
                _columnPathAndName = $"{cachedPath} - {Question.column_definition.display_name}";
                inj_logger?.LogWarning("[性能优化] 使用缓存路径 - TableID: {TableId}", Question.table_definition_id);
            }
            else
            {
                // 回退到原有的数据库查询方式
                await using var context = await ContextFactory.CreateDbContextAsync();
                _columnPathAndName = $"{await inj_medDictService.GetColumnNodePathAsync(context, Question.table_definition_id)} - {Question.column_definition.display_name}";
                inj_logger?.LogWarning("[性能调试] 缓存未命中，使用数据库查询 - TableID: {TableId}", Question.table_definition_id);
            }
        }
        catch (Exception ex)
        {
            inj_logger?.LogError(ex, "获取列节点路径时发生错误，表定义ID: {TableDefinitionId}", Question?.table_definition_id);
            _columnPathAndName = "路径加载失败";
        }
    }

    private Task OnSelectedItemChangedHandler(CascaderItem[] items)
    {
        if (items != null && items.Length > 0 && Guid.TryParse(items[^1].Value, out _))
        {
            _mergeToID = items[^1].Value;
        }
        return Task.CompletedTask;
    }

    private async Task OnDoMergeColumn()
    {
        if (string.IsNullOrEmpty(_mergeToID))
        {
            inj_snackbar.Add("请先选择目标列", Severity.Warning);
            return;
        }
        
        if (!Guid.TryParse(_mergeToID, out var destColumnId))
        {
            inj_snackbar.Add("目标列ID格式不正确", Severity.Error);
            return;
        }
        
        try
        {
            await using var context = await ContextFactory.CreateDbContextAsync();

            var columnDefinition = await context.column_definition.FindAsync(destColumnId);
            if (columnDefinition == null)
            {
                inj_snackbar.Add("目标列不存在", Severity.Error);
                return;
            }

            Question.column_definition_id = destColumnId;
            Question.column_name = columnDefinition.unique_name;
            Question.table_definition_id = columnDefinition.table_id;
            await _context.SaveChangesAsync();
            await OnSuccess.InvokeAsync();
            inj_snackbar.Add("列移动成功", Severity.Success);

            // 更新后也优先使用缓存
            if (TablePathCache != null && TablePathCache.TryGetValue(columnDefinition.table_id, out var updatedCachedPath))
            {
                _columnPathAndName = $"{updatedCachedPath} - {columnDefinition.display_name}";
            }
            else
            {
                _columnPathAndName = $"{await inj_medDictService.GetColumnNodePathAsync(context, columnDefinition.table_id)} - {columnDefinition.display_name}";
            }
            StateHasChanged();
        }
        catch (Exception ex)
        {
            inj_logger.LogError(ex, "移动列时发生错误");
            inj_snackbar.Add("移动列失败: " + ex.Message, Severity.Error);
        }
    }
}