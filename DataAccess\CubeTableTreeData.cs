﻿using DataAccess.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DataAccess.Models
{
    public class CubeTableTreeData
    {
        #region 属性
        public Guid ID { get; set; }
        public string UniqueName { get; set; }
        public string DisplayName { get; set; }
        public string NodeType { get; set; }
        public Guid ParentId { get; set; }
        public string NoteCn { get; set; }
        public string NoteEn { get; set; }
        public string DataType { get; set; }
        public List<string> OptionSet { get; set; }
        #endregion

        public CubeTableTreeData()
        {
            // 如果需要，初始化默认值
        }

        public CubeTableTreeData(table_definition table)
        {
            ID = table.id;
            UniqueName = table.unique_name;
            DisplayName = table.display_name;
            NodeType = table.node_type;
            ParentId = table.parent_node_id;
            NoteCn = table.note_cn;
            NoteEn = table.note_en;
        }

        public CubeTableTreeData(column_definition column)
        {
            ID = column.id;
            UniqueName = column.unique_name;
            DisplayName = column.display_name;
            DataType = column.data_type;
            ParentId = column.table_id;
            NodeType = "Column";
            NoteCn = column.note_cn;
            NoteEn = column.note_en;
            OptionSet = column.option_set;
        }
    }
}
