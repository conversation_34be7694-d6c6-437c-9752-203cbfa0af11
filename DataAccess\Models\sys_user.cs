﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace DataAccess.Models;

/// <summary>
/// 用户
/// </summary>
public partial class sys_user
{
    /// <summary>
    /// 用户ID
    /// </summary>
    public Guid id { get; set; }

    /// <summary>
    /// 用户登录账号
    /// </summary>
    public string account { get; set; }

    /// <summary>
    /// 用户登录密码
    /// </summary>
    public string password { get; set; }

    /// <summary>
    /// 显示名称
    /// </summary>
    public string display_name { get; set; }

    /// <summary>
    /// 向后授权
    /// </summary>
    public bool can_grant_to_other { get; set; }

    /// <summary>
    /// 是否有效
    /// </summary>
    public bool is_valid { get; set; }

    /// <summary>
    /// 头像文件名
    /// </summary>
    public string avatar_filename { get; set; }

    /// <summary>
    /// 最后登录时间
    /// </summary>
    public DateTime? last_login_time { get; set; }

    /// <summary>
    /// 最后登录IP
    /// </summary>
    public string last_login_ip { get; set; }

    /// <summary>
    /// 微信ID
    /// </summary>
    public string weixin_id { get; set; }

    /// <summary>
    /// 微信消息
    /// </summary>
    public string weixin_message { get; set; }

    public virtual ICollection<sys_role> role { get; set; } = new List<sys_role>();
}