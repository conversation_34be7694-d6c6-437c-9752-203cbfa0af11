using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Bio.AI;
using Bio.AI.Models;
using DataAccess.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace BusinessLogic.Cube;

/// <summary>
/// 数据字典LLM分析服务实现类
/// </summary>
public class DataDictLLMService : IDataDictLLMService
{
    private readonly IMedDictService _medDictService;
    private readonly IDbContextFactory<CubeContext> _contextFactory;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="medDictService">医学数据字典服务</param>
    /// <param name="contextFactory">数据库上下文工厂</param>
    public DataDictLLMService(IMedDictService medDictService, IDbContextFactory<CubeContext> contextFactory)
    {
        _medDictService = medDictService ?? throw new ArgumentNullException(nameof(medDictService));
        _contextFactory = contextFactory ?? throw new ArgumentNullException(nameof(contextFactory));
    }

    /// <summary>
    /// 使用LLM分析表单字段是否在数据字典中已存在
    /// </summary>
    /// <param name="itemsToAnalyze">需要分析的表单字段列表</param>
    /// <param name="logger">日志记录器</param>
    /// <returns>分析后的表单字段列表</returns>
    public async Task<List<IDataDictLLMService.ParsedDataItem>> RunLlmFieldAnalysisAsync(
        List<IDataDictLLMService.ParsedDataItem> itemsToAnalyze,
        List<IDataDictLLMService.ParsedDataItem> allItems,
        ILogger logger)
    {
        if (itemsToAnalyze == null || !itemsToAnalyze.Any())
        {
            logger.LogWarning("没有找到需要进行LLM分析的表单字段");
            return itemsToAnalyze;
        }

        // 按CategoryId分组
        var groupedItems = itemsToAnalyze.GroupBy(item => item.CategoryId).ToList();
        logger.LogInformation($"按CategoryId分组后，共有{groupedItems.Count}个分组需要分析");

        // 创建并发LLM分析任务
        var tasks = new List<Task>();

        // 修改为每300ms触发一个任务
        for (int i = 0; i < groupedItems.Count; i++)
        {
            var group = groupedItems[i];
            var categoryId = group.Key;
            var itemsInGroup = group.ToList();

            // 获取该分类下的数据字典
            logger.LogInformation($"开始获取分类 {categoryId} 的数据字典");

            // 等待300ms再启动下一个任务，以避免同时发起过多请求
            if (i > 0)
            {
                await Task.Delay(200);
            }

            // 创建一个新的任务来处理这个分组
            var task = Task.Run(async () =>
            {
                try
                {
                    // 解析GUID
                    if (Guid.TryParse(categoryId, out Guid categoryGuid))
                    {
                        using CubeContext _context = await _contextFactory.CreateDbContextAsync();
                        // 获取分类下的数据字典
                        string existingCategoryData = await _medDictService.ExportSubtreeDataDictionaryAsync(_context, categoryGuid);
                        logger.LogInformation($"获取到分类 {categoryId} 的数据字典，长度: {existingCategoryData.Length}");

                        if (string.IsNullOrWhiteSpace(existingCategoryData))
                        {
                            logger.LogWarning($"分类 {categoryId} 没有返回有效的数据字典内容");
                            // 更新组内所有项目的LLM分析结果为错误
                            foreach (var item in itemsInGroup)
                            {
                                item.LlmAnalysis = new IDataDictLLMService.LlmSemanticAnalysisResult
                                {
                                    IsProcessed = true,
                                    ErrorMessage = $"无法获取分类 {categoryId} 的数据字典内容"
                                };
                            }
                            return;
                        }

                        // 准备LLM分析-构建系统提示词
                        string systemPrompt = @"你是医学数据字典专家，你的核心任务是分析新表单字段是否在现有数据字典中已存在，并严格按照以下标准和格式输出结果。

分析标准：
1.  **语义匹配**：字段名称语义相似或相同。
    *   **上下文至关重要**：务必结合新字段的“所属分组”、“所属表单”、“所属目录”以及数据字典条目的“根节点 > ...”路径提供的医学语境来判断语义。
    *   **具体化优先**：如果新字段名称较为通用（如“诊断”），而其上下文（如“入院”）与数据字典中某个更具体的字段（如“入院诊断”）的限定词一致，则应视为语义匹配。
    *   **同义词/近义词**：考虑医学术语中的常见同义词和近义词。

2.  **类型匹配**：考虑以下类型等价关系，并在匹配对象之间给出具体例子：
    *   长文本 与 文本 --> 匹配 (例如：新字段“病程记录”[长文本] vs 现有字段“病情描述”[文本])
    *   单选 与 选择 --> 匹配 (例如：新字段“性别”[单选] vs 现有字段“患者性别”[选择])
    *   多选 与 选择 --> 匹配 (例如：新字段“过敏史”[多选] vs 现有字段“药物过敏”[选择])
    *   小数 与 数值 --> 匹配 (例如：新字段“体温”[小数] vs 现有字段“检查结果值”[数值])
    *   整数 与 数值 --> 匹配 (例如：新字段“年龄”[整数] vs 现有字段“住院天数”[数值])
    *   图片 与 文件 --> 匹配 (例如：新字段“X光片”[图片] vs 现有字段“影像资料”[文件])
    *   视频 与 文件 --> 匹配 (例如：新字段“手术录像”[视频] vs 现有字段“多媒体附件”[文件])

3.  **名称匹配但类型不同时**：标记为潜在匹配 (语义匹配true, 类型匹配false)。必须提供匹配到的字段ID，唯一名称、显示名称和类型。同时，根据新字段的类型，提供建议的英文字段名和规范化的中文字段名（通常是在原中文名后补充类型信息，如“（文本）”或调整以反映新类型）。

4.  **类型匹配但名称不匹配时**：应视为不匹配 (语义匹配false, 类型匹配true)。必须输出匹配到的字段ID，唯一名称、显示名称和类型，必须提供建议的英文字段名称和规范化的中文字段名称。

5.  **完全不匹配时** (名称和类型都不匹配，或语义上判断不相关)：应视为不匹配 (语义匹配false, 类型匹配false)。必须提供建议的英文字段名称和规范化的中文字段名称。

6.  **完全匹配时** (语义和类型均匹配)：建议的英文字段名和中文字段名留空。

返回CSV格式结果，每行一个字段分析，使用以下列顺序（用逗号分隔）：
1. 原始表单字段ID
2. 语义环境下字段名称是否匹配(true/false)
3. 类型是否匹配(true/false)
4. 匹配到的字段ID(无则留空，但必须保留逗号)
5. 匹配到的字段唯一名称(无则留空，但必须保留逗号)
6. 匹配到的字段显示名称(无则留空，但必须保留逗号)
7. 匹配到的字段类型(无则留空，但必须保留逗号)
8. 建议的英文字段名，不允许有空格(仅当完全匹配时留空，其他情况都提供，但必须保留逗号)
9. 建议的中文字段名(仅当完全匹配时留空，其他情况都提供，但必须保留逗号)
10. 分析理由(不允许出现ba简要说明匹配原因或不匹配原因，需清晰体现判断逻辑)

**特别注意CSV格式中空字段的处理：**
*   当第8和第9字段（建议的英文字段名和建议的中文字段名）因“完全匹配”而留空时，CSV中这两个字段将表现为两个连续的空值。这意味着第7个字段（匹配到的字段类型）之后，会紧跟**三个逗号**，然后才是第10个字段（分析理由）。例如： `...,匹配到的字段类型,,,分析理由...`。

对于建议的英文字段名，请遵循以下规则：
- 全部使用小写字母，使用下划线分隔单词
- 不允许出现空格
- 不允许长度大于50个字符，可以使用缩写 (例如：bp 代表 blood pressure, hr 代表 heart rate)
- 必须符合PostgreSQL数据库字段命名规范
- 字段名应符合当前医学分类的语境（参考数据字典第一行的分类路径和新字段的上下文信息）
- 字段名必须是唯一的，不能与现有数据字典中的任何字段唯一名称重复。
- 同一批次分析中生成的所有建议英文字段名也必须互不重复（如果需要区分，可添加数字后缀如 `_v2` 或更具体的描述词）。

对于建议的中文字段名，请遵循以下规则：
- 使用规范的医学术语
- 名称应简洁明了，通常不超过20个汉字
- 避免使用缩写和非标准表达
- 保持与医学分类语境的一致性
- 如原始名称已经规范，可以保留；如有不规范之处（如口语化、不标准术语），应予以修正。
- 当为“名称匹配但类型不同”的情况建议中文名时，如果原中文名已很规范，可在其后补充类型提示，如“入院诊断（文本）”。

**重要提示：**
*   **数据字典上下文**：数据字典第一行（如 “根节点 > 病史 > 现病史 > 神经系统评估”）提供了关键的医学语境。请务必利用此信息以及新表单字段的“所属分组”、“所属表单”、“所属目录”来辅助语义判断。
*   **唯一性检查**：在生成建议的英文字段名时，务必仔细检查数据字典中所有现有的唯一名称，确保不重复。同时，本次生成的所有建议英文名之间也不能重复。
*   **逗号占位符**：即使某些列没有值，也必须保留逗号作为占位符，确保每行都有10个字段（9个逗号）。
*   **针对完全匹配的逗号处理（再次强调）**：当一个新字段与数据字典条目完全匹配时（语义匹配true，类型匹配true），第8和第9字段（建议的英文名和中文名）应为空。此时，CSV的相应部分应为 `...,<匹配到的字段类型>,,,<分析理由>...`。
    *   **错误示例（避免此种输出）**：`...,文本,,,,新字段名称...` (这里在“文本”和“新字段名称”之间有4个逗号，导致了11个字段)。
    *   **正确示例（务必遵循此格式）**：`...,文本,,,新字段名称...` (这里在“文本”和“新字段名称”之间有3个逗号，正确表示第8和第9字段为空，总共10个字段)。
*   **单行记录**：每一个分析记录必须单独占一行，并以换行符结束。

示例（请注意分析理由的细化）：
(1-1-1-1),true,true,00001111-aaaa-bbbb-ccc0-00163e08a3b0,book_title,图书标题,文本,,,字段名称“图书标题”与现有字段完全匹配且类型一致。
(1-1-1-1),true,false,00002222-dddd-eeee-fff0-00163e08a3b1,publish_date,出版日期,日期,publish_year_text,出版年份（文本）,字段名称“出版信息”语义匹配，但新字段类型为“文本”，现有字段类型为“日期”，建议新字段，并提供现有匹配字段ID。
(1-1-1-1),false,true,,,,,author_country,作者国籍,新字段名称“作者所属国家”与现有字段名称在语义上不直接匹配，但类型“选择”匹配，建议新字段。
(1-1-1-1),false,false,,,,,book_isbn_code,图书ISBN码,新字段名称“ISBN编码”和类型“文本”均未在现有字典中找到直接语义或类型匹配的字段，建议新字段。
(1-1-1-1),true,true,00003333-gggg-hhhh-iii0-00163e08a3b2,book_description,图书描述,长文本,,,新字段名称“内容简介”在“图书详情”上下文中与现有字段“图书描述”语义匹配，类型“长文本”一致。

注意，非常重要：
对于如“(1-1-1-1),true,false”这样的结果，即使某些字段没有值，也必须保留逗号作为占位符，确保每行都有10个字段（9个逗号）。
即使某些字段没有值，也必须保留逗号作为占位符，确保每行都有10个字段（9个逗号）。
每一个分析记录必须单独占一行，并以换行符结束。不要将多个记录合并到单行中。
仅返回纯CSV格式，不要添加任何标题行或其他文本。";

                        // 直接构建文本格式的提示词
                        var newFieldsText = new StringBuilder();
                        foreach (var item in itemsInGroup)
                        {
                            var (groupName, formName, directoryName) = GetParentNames(item.Id, allItems, logger);
                            newFieldsText.AppendLine($"ID: {item.Id}, 所属分组：{groupName}, 所属表单：{formName}, 所属目录：{directoryName}, 名称: {CleanDisplayName(item.DisplayName)}, 类型: {item.ItemType}");
                        }

                        string userPrompt = @"分析新表单字段是否在数据字典中已存在：

【新表单字段】
" + newFieldsText.ToString() + @"

【已有数据字典条目】
" + existingCategoryData;

                        // 调用LLM进行分析
                        logger.LogInformation($"开始针对分类 {categoryId} 调用LLM分析，包含 {itemsInGroup.Count} 个字段");
                        var llmResult = await BioAI.ThinkAsync(
                            prompt: userPrompt,
                            model: LLModel.OpenRouter.Gemini_25_Flash_05_20,
                            systemPrompt: systemPrompt
                        );
                        string answer = llmResult.Answer;

                        // 记录返回结果
                        logger.LogInformation($"LLM返回了分类 {categoryId} 的分析结果，长度: {answer.Length}");

                        if (string.IsNullOrWhiteSpace(answer))
                        {
                            logger.LogWarning($"LLM返回的分类 {categoryId} 分析结果为空");
                            // 更新组内所有项目的LLM分析结果为错误
                            foreach (var item in itemsInGroup)
                            {
                                item.LlmAnalysis = new IDataDictLLMService.LlmSemanticAnalysisResult
                                {
                                    IsProcessed = true,
                                    ErrorMessage = "LLM返回的分析结果为空"
                                };
                            }
                            return;
                        }

                        try
                        {
                            // 清理LLM返回的结果，移除可能的Markdown代码块标记
                            string cleanedAnswer = answer;

                            // 检查是否以```开头，表示是一个Markdown代码块
                            if (cleanedAnswer.StartsWith("```"))
                            {
                                // 找到第一个换行符，跳过第一行（可能包含```csv等）
                                int firstNewline = cleanedAnswer.IndexOf('\n');
                                if (firstNewline > 0)
                                {
                                    cleanedAnswer = cleanedAnswer.Substring(firstNewline + 1);
                                }

                                // 寻找结尾的```并移除
                                int endMarkdown = cleanedAnswer.LastIndexOf("```");
                                if (endMarkdown > 0)
                                {
                                    cleanedAnswer = cleanedAnswer.Substring(0, endMarkdown).Trim();
                                }
                            }

                            logger.LogInformation($"清理后的CSV结果: {cleanedAnswer.Substring(0, Math.Min(100, cleanedAnswer.Length))}...");

                            // 尝试解析CSV结果
                            var results = ParseCsvResults(cleanedAnswer, logger);

                            if (results == null || !results.Any())
                            {
                                logger.LogWarning($"无法解析LLM返回的分类 {categoryId} 分析结果为有效的CSV格式");
                                throw new Exception("无法解析LLM返回的分析结果为有效的CSV格式");
                            }

                            logger.LogInformation($"成功解析LLM返回的分类 {categoryId} 分析结果，共 {results.Count} 项");

                            // 更新每个字段的分析结果
                            foreach (var result in results)
                            {
                                // 查找对应的ParsedDataItem
                                var item = itemsInGroup.FirstOrDefault(i => i.Id == result.parsed_data_item_id);
                                if (item != null)
                                {
                                    // 创建分析结果
                                    item.LlmAnalysis = new IDataDictLLMService.LlmSemanticAnalysisResult
                                    {
                                        IsProcessed = true,
                                        // 直接使用LLM的匹配结果
                                        MatchFound = result.match_found,
                                        // 直接使用LLM的潜在匹配结果
                                        PotentialMatchTypeMismatch = result.potential_match_type_mismatch,
                                        // 添加匹配到的字段ID
                                        MatchedExistingItemId = result.matched_existing_item_id,
                                        MatchedExistingItemUniqueName = result.matched_existing_item_unique_name,
                                        MatchedExistingItemDisplayName = result.matched_existing_item_display_name,
                                        MatchedExistingItemType = result.matched_existing_item_type,
                                        Reasoning = result.reasoning,
                                        ExistingCategoryDataAsText = existingCategoryData,
                                        // 添加建议的英文字段名
                                        SuggestedEnglishName = result.suggested_english_name,
                                        // 添加建议的中文字段名
                                        SuggestedChineseName = result.suggested_chinese_name
                                    };

                                    logger.LogInformation($"更新字段 {item.DisplayName} 的LLM分析结果: 匹配={result.match_found}, 类型不匹配={result.potential_match_type_mismatch}, 建议英文名={result.suggested_english_name}, 建议中文名={result.suggested_chinese_name}");
                                }
                                else
                                {
                                    logger.LogWarning($"无法找到ID为 {result.parsed_data_item_id} 的表单字段");
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            logger.LogError(ex, $"解析LLM返回的分类 {categoryId} 分析结果时发生错误");

                            // 更新组内所有项目的LLM分析结果为错误
                            foreach (var item in itemsInGroup)
                            {
                                item.LlmAnalysis = new IDataDictLLMService.LlmSemanticAnalysisResult
                                {
                                    IsProcessed = true,
                                    ErrorMessage = $"解析LLM分析结果时发生错误: {ex.Message}",
                                    ExistingCategoryDataAsText = existingCategoryData
                                };
                            }
                        }
                    }
                    else
                    {
                        logger.LogError($"无效的分类ID格式: {categoryId}");
                        // 更新组内所有项目的LLM分析结果为错误
                        foreach (var item in itemsInGroup)
                        {
                            item.LlmAnalysis = new IDataDictLLMService.LlmSemanticAnalysisResult
                            {
                                IsProcessed = true,
                                ErrorMessage = $"无效的分类ID格式: {categoryId}"
                            };
                        }
                    }
                }
                catch (Exception ex)
                {
                    logger.LogError(ex, $"处理分类 {categoryId} 时发生错误");
                    // 更新组内所有项目的LLM分析结果为错误
                    foreach (var item in itemsInGroup)
                    {
                        item.LlmAnalysis = new IDataDictLLMService.LlmSemanticAnalysisResult
                        {
                            IsProcessed = true,
                            ErrorMessage = $"处理过程中发生错误: {ex.Message}"
                        };
                    }
                }
            });

            tasks.Add(task);
        }

        // 等待所有任务完成
        await Task.WhenAll(tasks);

        logger.LogInformation("所有LLM分析任务已完成");

        return itemsToAnalyze;
    }

    /// <summary>
    /// 清理显示名称，移除空格和连接符号
    /// </summary>
    /// <param name="displayName">显示名称</param>
    /// <returns>清理后的显示名称</returns>
    public string CleanDisplayName(string displayName)
    {
        if (string.IsNullOrEmpty(displayName))
            return string.Empty;

        // 替换掉常见的树形结构连接符号（如"──"、"│"、"└──"等）
        string cleaned = displayName
            .Replace("─", "")
            .Replace("│", "")
            .Replace("└", "")
            .Replace("├", "")
            .Trim();

        // 移除连续空格
        while (cleaned.Contains("  "))
        {
            cleaned = cleaned.Replace("  ", " ");
        }

        return cleaned;
    }

    /// <summary>
    /// 解析CSV格式的LLM分析结果
    /// </summary>
    /// <param name="csvText">CSV格式文本</param>
    /// <param name="logger">日志记录器</param>
    /// <returns>解析后的LLM分析结果列表</returns>
    public List<IDataDictLLMService.LlmSingleFieldAnalysisResponse> ParseCsvResults(string csvText, ILogger logger)
    {
        var results = new List<IDataDictLLMService.LlmSingleFieldAnalysisResponse>();
        if (string.IsNullOrWhiteSpace(csvText))
            return results;

        // 按行分割
        var lines = csvText.Split(new[] { "\r\n", "\r", "\n" }, StringSplitOptions.RemoveEmptyEntries);

        foreach (var line in lines)
        {
            if (string.IsNullOrWhiteSpace(line))
                continue;

            try
            {
                // 使用简单的逗号分割
                var parts = line.Split(',');

                // 修改：允许9或10个字段（增加了matched_existing_item_id）
                if (parts.Length < 9 || parts.Length > 10)
                {
                    logger.LogWarning($"CSV行格式不正确，需要9或10个字段: {line}");
                    continue;
                }

                // 解析CSV字段
                bool semanticMatch = bool.TryParse(parts[1].Trim(), out bool sm) && sm;
                bool typeMatch = bool.TryParse(parts[2].Trim(), out bool tm) && tm;

                // 根据语义匹配和类型匹配的组合来确定最终状态
                bool finalMatchFound = semanticMatch && typeMatch; // 完全匹配：语义和类型都匹配
                bool potentialMatch = semanticMatch && !typeMatch; // 潜在匹配：语义匹配但类型不匹配

                // 记录解析结果用于调试
                logger.LogDebug($"解析CSV行 {parts[0]}: 语义匹配={semanticMatch}, 类型匹配={typeMatch}, 最终匹配={finalMatchFound}, 潜在匹配={potentialMatch}");

                // 创建分析结果对象
                var result = new IDataDictLLMService.LlmSingleFieldAnalysisResponse
                {
                    // 第1个字段：原始表单字段ID
                    parsed_data_item_id = parts[0].Trim(),
                    // 第2个字段：语义环境下字段名称是否匹配
                    // 第3个字段：类型是否匹配
                    // 组合逻辑：只有语义和类型都匹配才算完全匹配
                    match_found = finalMatchFound,
                    // 潜在匹配：语义匹配但类型不匹配
                    potential_match_type_mismatch = potentialMatch,
                    // 第4个字段：匹配到的字段ID
                    matched_existing_item_id = !string.IsNullOrWhiteSpace(parts[3]) ? parts[3].Trim() : null,
                    // 第5个字段：匹配到的字段唯一名称
                    matched_existing_item_unique_name = !string.IsNullOrWhiteSpace(parts[4]) ? parts[4].Trim() : null,
                    // 第6个字段：匹配到的字段显示名称
                    matched_existing_item_display_name = !string.IsNullOrWhiteSpace(parts[5]) ? parts[5].Trim() : null,
                    // 第7个字段：匹配到的字段类型
                    matched_existing_item_type = !string.IsNullOrWhiteSpace(parts[6]) ? parts[6].Trim() : null,
                    // 第8个字段：建议的英文字段名 (如果存在)
                    suggested_english_name = !string.IsNullOrWhiteSpace(parts[7]) ? parts[7].Trim() : string.Empty,
                    // 第9个字段：建议的中文字段名 (如果存在)
                    suggested_chinese_name = parts.Length > 8 && !string.IsNullOrWhiteSpace(parts[8]) ? parts[8].Trim() : string.Empty,
                    // 第10个字段或第9个字段：分析理由
                    reasoning = parts.Length > 9 ?
                        !string.IsNullOrWhiteSpace(parts[9]) ? parts[9].Trim() : "未提供分析理由" :
                        !string.IsNullOrWhiteSpace(parts[8]) ? parts[8].Trim() : "未提供分析理由",
                };

                results.Add(result);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, $"解析CSV行时发生错误: {line}");
            }
        }

        return results;
    }

    // 添加辅助方法：从字段 ID 中提取父级 ID
    private List<string> GetParentIds(string id)
    {
        // 移除括号，分割 ID
        string cleanId = id.Trim('(', ')');
        string[] parts = cleanId.Split('-', StringSplitOptions.RemoveEmptyEntries);

        List<string> parentIds = new List<string>();

        // 添加分组 ID
        if (parts.Length > 0)
            parentIds.Add($"({parts[0].Trim()})");

        // 添加表单 ID
        if (parts.Length > 1)
            parentIds.Add($"({parts[0].Trim()}-{parts[1].Trim()})");

        // 添加目录 ID
        if (parts.Length > 2)
            parentIds.Add($"({parts[0].Trim()}-{parts[1].Trim()}-{parts[2].Trim()})");

        return parentIds;
    }

    // 添加辅助方法：查找父级项目并返回清理后的名称
    private (string GroupName, string FormName, string DirectoryName) GetParentNames(
        string id,
        List<IDataDictLLMService.ParsedDataItem> allItems,
        ILogger logger)
    {
        List<string> parentIds = GetParentIds(id);

        // 查找分组名称
        var groupItem = allItems.FirstOrDefault(item => item.Id == parentIds[0]);
        if (groupItem == null)
        {
            logger.LogError($"找不到字段 {id} 的分组项目 {parentIds[0]}");
            throw new Exception($"找不到字段 {id} 的分组项目 {parentIds[0]}");
        }
        string groupName = CleanDisplayName(groupItem.DisplayName);

        // 查找表单名称
        var formItem = allItems.FirstOrDefault(item => item.Id == parentIds[1]);
        if (formItem == null)
        {
            logger.LogError($"找不到字段 {id} 的表单项目 {parentIds[1]}");
            throw new Exception($"找不到字段 {id} 的表单项目 {parentIds[1]}");
        }
        string formName = CleanDisplayName(formItem.DisplayName);

        // 查找目录名称
        var directoryItem = allItems.FirstOrDefault(item => item.Id == parentIds[2]);
        if (directoryItem == null)
        {
            logger.LogError($"找不到字段 {id} 的目录项目 {parentIds[2]}");
            throw new Exception($"找不到字段 {id} 的目录项目 {parentIds[2]}");
        }
        string directoryName = CleanDisplayName(directoryItem.DisplayName);

        return (groupName, formName, directoryName);
    }
}
