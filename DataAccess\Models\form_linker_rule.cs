﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace DataAccess.Models;

/// <summary>
/// 表单问题联动规则表
/// </summary>
public partial class form_linker_rule
{
    /// <summary>
    /// ID
    /// </summary>
    public Guid id { get; set; }

    /// <summary>
    /// 表单集ID
    /// </summary>
    public Guid formset_id { get; set; }

    public string rule_name { get; set; }

    /// <summary>
    /// 规则类型
    /// </summary>
    public string rule_type { get; set; }

    /// <summary>
    /// 发布者对象类型
    /// </summary>
    public string publish_obj_calss { get; set; }

    /// <summary>
    /// 发布者对象ID
    /// </summary>
    public List<Guid> publish_obj_id { get; set; }

    /// <summary>
    /// 订阅者对象类型
    /// </summary>
    public string subscribe_obj_calss { get; set; }

    /// <summary>
    /// 订阅者对象ID
    /// </summary>
    public List<Guid> subscribe_obj_id { get; set; }

    /// <summary>
    /// 执行函数名称
    /// </summary>
    public string action_function { get; set; }

    /// <summary>
    /// 触发条件
    /// </summary>
    public string trigger_condition { get; set; }

    /// <summary>
    /// 优先级
    /// </summary>
    public int? priority { get; set; }

    /// <summary>
    /// 参数
    /// </summary>
    public string parameters { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime created_at { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime updated_at { get; set; }

    /// <summary>
    /// 是否是复合类型
    /// </summary>
    public bool? is_composite { get; set; }

    /// <summary>
    /// 依赖字段ID
    /// </summary>
    public List<Guid> dependent_field_ids { get; set; }

    public virtual form_form_set formset { get; set; }
}