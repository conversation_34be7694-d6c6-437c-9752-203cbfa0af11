﻿@inherits Cmp_Base
@inject ILogger<Cmp_QuestionBoolean> inj_logger
@inject IMedDictService inj_medDictService
@inject IDbContextFactory<CubeContext> ContextFactory

<div style="font-size: 1rem; color: #ad6200; font-weight: bold;">@_columnPathAndName</div>
<h3>Cmp_Question_Boolean</h3>

@code {
    [Parameter]
    public required CubeContext _context { get; set; }
    [Parameter]
    public required form_question Question { get; set; }
    [CascadingParameter(Name = "TablePathCache")]
    public Dictionary<Guid, string> TablePathCache { get; set; }

    private string _columnPathAndName = "正在加载...";
    private Guid? _lastQuestionId;

    protected override async Task OnParametersSetAsync()
    {
        // 只有当Question真正变化时才重新加载路径
        if (_lastQuestionId != Question?.id)
        {
            _lastQuestionId = Question?.id;
            await LoadColumnPathAsync();
        }
    }

    private async Task LoadColumnPathAsync()
    {
        try
        {
            // 优先使用FormSetManager传递的路径缓存
            if (TablePathCache != null && TablePathCache.TryGetValue(Question.table_definition_id, out var cachedPath))
            {
                _columnPathAndName = $"{cachedPath} - {Question.column_definition.display_name}";
                inj_logger?.LogWarning("[性能优化] 使用缓存路径 - TableID: {TableId}", Question.table_definition_id);
            }
            else
            {
                // 回退到原有的数据库查询方式
                await using var context = await ContextFactory.CreateDbContextAsync();
                _columnPathAndName = $"{await inj_medDictService.GetColumnNodePathAsync(context, Question.table_definition_id)} - {Question.column_definition.display_name}";
                inj_logger?.LogWarning("[性能调试] 缓存未命中，使用数据库查询 - TableID: {TableId}", Question.table_definition_id);
            }
        }
        catch (Exception ex)
        {
            inj_logger?.LogError(ex, "获取列节点路径时发生错误，表定义ID: {TableDefinitionId}", Question?.table_definition_id);
            _columnPathAndName = "路径加载失败";
        }
    }
}
