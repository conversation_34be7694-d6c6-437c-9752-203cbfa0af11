﻿@inject NavigationManager NavigationManager
@inject ILogger<RedirectToSignIn> _logger
@inject AuthenticationStateProvider AuthenticationStateProvider

@code {
    protected override async Task OnInitializedAsync()
    {
        var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        var user = authState.User;
        _logger.LogWarning("RedirectToSignIn: 初次检查 - 用户认证状态 IsAuthenticated={IsAuthenticated}, UserName={UserName}", user.Identity?.IsAuthenticated, user.Identity?.Name);

        if (user.Identity?.IsAuthenticated ?? false)
        {
            _logger.LogWarning("RedirectToSignIn: 初次检查成功，用户已认证。导航到 /Home。");
            NavigationManager.NavigateTo("/Home"); //  如果您的应用有 returnUrl 逻辑，可以替换为该逻辑
            return;
        }

        _logger.LogWarning("RedirectToSignIn: 初次检查失败。等待2秒后重新检查认证状态。");
        await Task.Delay(2000); // 等待2秒

        authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        user = authState.User;
        _logger.LogWarning("RedirectToSignIn: 再次检查 - 用户认证状态 IsAuthenticated={IsAuthenticated}, UserName={UserName}", user.Identity?.IsAuthenticated, user.Identity?.Name);

        if (user.Identity?.IsAuthenticated ?? false)
        {
            _logger.LogWarning("RedirectToSignIn: 再次检查成功，用户已认证。导航到 /Home。");
            NavigationManager.NavigateTo("/Home"); //  如果您的应用有 returnUrl 逻辑，可以替换为该逻辑
        }
        else
        {
            _logger.LogWarning("RedirectToSignIn: 再次检查失败，用户仍未认证。触发重定向到 /Login。");
            NavigationManager.NavigateTo("Login");
        }
    }
}