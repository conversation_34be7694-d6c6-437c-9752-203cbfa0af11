﻿using DataAccess.Models;
using AutoMapper;

namespace CubeBase
{
    public class MappingProfile : Profile
    {
        public MappingProfile()
        {
            CreateMap<column_definition, column_definition>().ReverseMap();
            CreateMap<sys_user, sys_user>().ReverseMap();
            CreateMap<sys_role, sys_role>().ReverseMap();
            CreateMap<sys_permission, sys_permission>().ReverseMap();

            CreateMap<sys_hospital, sys_hospital>().ReverseMap();
            CreateMap<sys_region, sys_region>().ReverseMap();
            CreateMap<sys_department, sys_department>().ReverseMap();
            CreateMap<sys_ward, sys_ward>().ReverseMap();

            CreateMap<form_project, form_project>().ReverseMap();
            CreateMap<form_form, form_form>().ReverseMap();
        }
    }
}
