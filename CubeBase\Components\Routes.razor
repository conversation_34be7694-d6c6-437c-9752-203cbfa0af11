﻿<BootstrapBlazorRoot>
    <CascadingAuthenticationState>
        <Router AppAssembly="typeof(Program).Assembly">
            <Found Context="routeData">
                <AuthorizeRouteView RouteData="routeData" DefaultLayout="typeof(Layout.MainLayout)">
                    <NotAuthorized>
                        <RedirectToSignIn />
                    </NotAuthorized>
                </AuthorizeRouteView>
                <FocusOnNavigate RouteData="routeData" Selector="h1" />
            </Found>
        </Router>
    </CascadingAuthenticationState>
</BootstrapBlazorRoot>