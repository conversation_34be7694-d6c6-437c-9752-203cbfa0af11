﻿namespace Infrastructure
{
    public class SequentialGuidGenerator
    {
        public static Guid NewGuid()
        {
            byte[] guidBytes = Guid.NewGuid().ToByteArray();

            // 获取当前UTC时间的Ticks
            long timestamp = DateTime.UtcNow.Ticks;
            byte[] timestampBytes = BitConverter.GetBytes(timestamp);

            // 如果系统是小端序，则需要反转字节顺序
            if (BitConverter.IsLittleEndian)
            {
                Array.Reverse(timestampBytes);
            }

            // 将时间戳的后6个字节复制到GUID的最后6个字节
            Array.Copy(timestampBytes, timestampBytes.Length - 6, guidBytes, guidBytes.Length - 6, 6);

            return new Guid(guidBytes);
        }

    }
}
