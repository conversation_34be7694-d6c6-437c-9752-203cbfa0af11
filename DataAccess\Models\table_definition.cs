﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace DataAccess.Models;

public partial class table_definition
{
    /// <summary>
    /// ID
    /// </summary>
    public Guid id { get; set; }

    /// <summary>
    /// 表的唯一名称
    /// </summary>
    public string unique_name { get; set; }

    /// <summary>
    /// 表的显示名称
    /// </summary>
    public string display_name { get; set; }

    /// <summary>
    /// 是否有效
    /// </summary>
    public bool is_valid { get; set; }

    /// <summary>
    /// 节点类型（Visual，Table）
    /// </summary>
    public string node_type { get; set; }

    /// <summary>
    /// 父节点ID
    /// </summary>
    public Guid parent_node_id { get; set; }

    /// <summary>
    /// 历史版本号数组
    /// </summary>
    public List<int> history_ver_array { get; set; }

    /// <summary>
    /// 版本发布后是否发生改变
    /// </summary>
    public bool is_changed { get; set; }

    /// <summary>
    /// 排序索引
    /// </summary>
    public int sorted_index { get; set; }

    /// <summary>
    /// 中文释义
    /// </summary>
    public string note_cn { get; set; }

    /// <summary>
    /// 英文释义
    /// </summary>
    public string note_en { get; set; }

    public DateTime? update_time { get; set; }

    public virtual ICollection<column_definition> column_definition { get; set; } = new List<column_definition>();

    public virtual ICollection<form_question> form_question { get; set; } = new List<form_question>();
}