create table "public"."unique_patient" (
    "id" uuid not null,
    "name" text not null,
    "birthday" date,
    "gender" text,
    "sid_type" text[],
    "sid_number" text[],
    "sid_address" text[],
    "sid_verification" text[],
    "phone_type" text[],
    "phone_number" text[],
    "weixin_id" text[],
    "weixin_phone_number" text[],
    "address_type" text[],
    "address_content" text[],
    "medical_type" text,
    "medical_card" text,
    "old_name" text[],    
    CONSTRAINT "unique_patient_pkey" PRIMARY KEY ("id")
);
ALTER TABLE "public"."unique_patient" OWNER TO "postgres";

comment on table "public"."unique_patient" is '唯一患者表';
comment on column "public"."unique_patient"."id" is '主键';
comment on column "public"."unique_patient"."name" is '姓名';
comment on column "public"."unique_patient"."birthday" is '出生日期';
comment on column "public"."unique_patient"."gender" is '性别';
comment on column "public"."unique_patient"."sid_type" is '身份证类型';
comment on column "public"."unique_patient"."sid_number" is '身份证号码';
comment on column "public"."unique_patient"."sid_address" is '身份证地址';
comment on column "public"."unique_patient"."sid_verification" is '身份证验证';
comment on column "public"."unique_patient"."phone_type" is '联系电话类型';
comment on column "public"."unique_patient"."phone_number" is '联系电话号码';
comment on column "public"."unique_patient"."weixin_id" is '微信号';
comment on column "public"."unique_patient"."weixin_phone_number" is '微信号绑定电话';
comment on column "public"."unique_patient"."address_type" is '地址类型';
comment on column "public"."unique_patient"."address_content" is '地址内容';
comment on column "public"."unique_patient"."medical_type" is '医保类型';
comment on column "public"."unique_patient"."medical_card" is '医保卡号';
comment on column "public"."unique_patient"."old_name" is '曾用名';


create table "public"."patient" (
    "id" uuid not null,
    "unique_id" uuid not null,
    "hospital_id" uuid,
    "medical_record_number" text,
    "inpatient_number" text,
    "name" text,
    "age" date,
    "gender" text,
    "sid_type" text,
    "sid_number" text,
    "phone_type" text,
    "phone_number" text,
    "weixin_id" text,
    "weixin_phone_number" text,
    "contact_name" text[],
    "contact_phone" text[],
    "contact_relation" text[],
    "address_type" text[],
    "address_content" text[],
    "email" text[],
    "nation" text,
    "native_place" text,
    "marital_status" text,
    "education" text,
    "occupation" text,
    "create_time" timestamp default current_timestamp,
    CONSTRAINT "patient_pkey" PRIMARY KEY ("Id")
);
ALTER TABLE "public"."patient" OWNER TO "postgres";

comment on table "public"."patient" is '患者表';
comment on column "public"."patient"."id" is '主键';
comment on column "public"."patient"."unique_id" is '唯一患者id';
comment on column "public"."patient"."hospital_id" is '医院id';
comment on column "public"."patient"."medical_record_number" is '病案号';
comment on column "public"."patient"."inpatient_number" is '住院号';
comment on column "public"."patient"."name" is '姓名';
comment on column "public"."patient"."age" is '年龄';
comment on column "public"."patient"."gender" is '性别';
comment on column "public"."patient"."sid_type" is '证件类型';
comment on column "public"."patient"."sid_number" is '证件号码';
comment on column "public"."patient"."phone_type" is '联系电话类型';
comment on column "public"."patient"."phone_number" is '联系电话号码';
comment on column "public"."patient"."weixin_id" is '微信号';
comment on column "public"."patient"."weixin_phone_number" is '微信号绑定电话';
comment on column "public"."patient"."contact_name" is '联系人';
comment on column "public"."patient"."contact_phone" is '联系人电话';
comment on column "public"."patient"."contact_relation" is '联系人关系';
comment on column "public"."patient"."address_type" is '地址类型';
comment on column "public"."patient"."address_content" is '地址内容';
comment on column "public"."patient"."email" is '邮箱';
comment on column "public"."patient"."nation" is '民族';
comment on column "public"."patient"."native_place" is '籍贯';
comment on column "public"."patient"."marital_status" is '婚姻状况';
comment on column "public"."patient"."education" is '教育程度';
comment on column "public"."patient"."occupation" is '职业';

create table "System"."Sys_License" (
    "id" uuid not null,
    "code" text not null,
    "name" text not null,
    "describe" text,
    "type" text,
    "is_valid" boolean default true,    
    "valid_start_time" timestamp,
    "valid_end_time" timestamp,
    "create_time" timestamp default current_timestamp,
    CONSTRAINT "Sys_License_pkey" PRIMARY KEY ("Id")
);
ALTER TABLE "System"."Sys_License" OWNER TO "postgres";

comment on table "System"."Sys_License" is '许可证表'; 
comment on column "System"."Sys_License"."id" is '主键';
comment on column "System"."Sys_License"."code" is '许可证代码';
comment on column "System"."Sys_License"."name" is '许可证名称';
comment on column "System"."Sys_License"."describe" is '许可证描述';
comment on column "System"."Sys_License"."type" is '许可证类型';
comment on column "System"."Sys_License"."is_valid" is '是否有效';
comment on column "System"."Sys_License"."valid_start_time" is '有效开始时间';
comment on column "System"."Sys_License"."valid_end_time" is '有效结束时间';
comment on column "System"."Sys_License"."create_time" is '创建时间';

CREATE TABLE "System"."Sys_Map_Role_License" (
  "Sys_RoleId" uuid NOT NULL,
  "Sys_LicenseId" uuid NOT NULL,
  CONSTRAINT "Map_Role_License_pkey" PRIMARY KEY ("Sys_RoleId", "Sys_LicenseId"),
  CONSTRAINT "Map_Role_License_LicenseId_fkey" FOREIGN KEY ("Sys_LicenseId") REFERENCES "System"."Sys_License" ("Id") ON DELETE NO ACTION ON UPDATE NO ACTION,
  CONSTRAINT "Map_Role_License_RoleId_fkey" FOREIGN KEY ("Sys_RoleId") REFERENCES "System"."Sys_Role" ("Id") ON DELETE NO ACTION ON UPDATE NO ACTION
);

ALTER TABLE "System"."Sys_Map_Role_License" 
  OWNER TO "postgres";

COMMENT ON COLUMN "System"."Sys_Map_Role_License"."Sys_RoleId" IS '角色ID';
COMMENT ON COLUMN "System"."Sys_Map_Role_License"."Sys_LicenseId" IS '许可证ID';
COMMENT ON TABLE "System"."Sys_Map_Role_License" IS '角色和许可证映射表';