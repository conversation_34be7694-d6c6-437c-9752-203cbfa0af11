﻿@page "/Cube"
@layout BlankLayout
@inherits BasePage
@inject ILogger<CubeManager> inj_logger

<MudGrid>
    <!-- 左侧树视图区域 -->
    <MudItem xs="3">
        <MudField Label="列表区域" Variant="Variant.Outlined">
            <MudButton Color="Color.Primary" Variant="Variant.Filled" OnClick="AddNewTable" Disabled="@(_selectedTableEntity==null || _selectedTableEntity.node_type=="Table")">增加新节点</MudButton>
            <Button Size="BSize.Small" Text="1" OnClick="@(() => SetTreeViewLevel(1))" />
            <Button Size="BSize.Small" Text="2" OnClick="@(() => SetTreeViewLevel(2))" />
            <Button Size="BSize.Small" Text="3" OnClick="@(() => SetTreeViewLevel(3))" />
            <Button Size="BSize.Small" Text="4" OnClick="@(() => SetTreeViewLevel(4))" />
            <Button Size="BSize.Small" Text="5" OnClick="@(() => SetTreeViewLevel(5))" />
            <Button Size="BSize.Small" Text="6" OnClick="@(() => SetTreeViewLevel(6))" />
            <Button Size="BSize.Small" Text="7" OnClick="@(() => SetTreeViewLevel(7))" />
            <Button Size="BSize.Small" Text="8" OnClick="@(() => SetTreeViewLevel(8))" />
            <Button Size="BSize.Small" Text="9" OnClick="@(() => SetTreeViewLevel(9))" />
            <MudPaper Class="pa-0" Elevation="0" Style="overflow-y: auto;height:calc(100vh - 90px);">
                <TreeView @ref="_pageElements_TreeView" TItem="table_definition" Items="@_treeItemsList" OnTreeItemClick="OnTreeViewItemClick" ShowIcon="true" Style="height:100%;"
                          OnExpandNodeAsync="OnExpandNodeAsync" />
            </MudPaper>
        </MudField>
    </MudItem>

    <!-- 中间内容区域 -->
    <MudItem xs="5" Style="height: 100vh; display: flex; flex-direction: column;">
        <MudPaper Class="pa-2" Elevation="0" Style="flex: 1; overflow-y: auto; height: 0;">
            @if (_selectedTableEntity != null)
            {
                <MudText Typo="Typo.h5" Class="pa-2">编辑 - @GetParentNodeChainPathString(_selectedTableEntity, false)</MudText>
                <MudForm Model="_selectedTableEntity">
                    <MudTextField Label="主键" @bind-Value="_selectedTableEntity.id" Margin="Margin.Dense" Variant="Variant.Outlined" />
                    <span>DEBUG ID: @_selectedTableEntity?.id</span>
                    <div class="d-flex">
                        <div class="flex-grow-1">
                            <MudTextField Label="显示名称" @bind-Value="_selectedTableEntity.display_name" Required Variant="Variant.Outlined" Margin="Margin.Dense" />
                        </div>
                        <div>
                            <MudButton Class="ma-1 mt-3" OnClick="@GetEnglishTableName" Color="Color.Primary" Variant="Variant.Filled" Size="MudBlazor.Size.Small"> 翻 译 </MudButton>
                            <span hidden="@(!_isVisible)"><Spinner Color="BColor.Success" style="padding-top:16px;" /></span>
                        </div>
                        <div class="flex-grow-1">
                            <MudTextField Label="唯一编码" @bind-Value="_selectedTableEntity.unique_name" Required Variant="Variant.Outlined" T="string"
                                          Counter="60" Validation="@(new Func<string, IEnumerable<string>>(MaxCharacters))" Immediate="true" Margin="Margin.Dense" />
                        </div>
                    </div>

                    <div class="d-flex">
                        <div class="ma-1 mt-3">
                            <RadioList @bind-Value="@_selectedTableEntity.node_type" Items="@_nodeTypeItems" IsButton="true" Color="BColor.Success"></RadioList>
                        </div>
                        <div class="flex-grow-1">
                            <MudSelect T="Guid" Label="父节点" @bind-Value="_selectedTableEntity.parent_node_id" Variant="Variant.Outlined" Dense>
                                @foreach (var tableDef in _contextTableList.Where(td => td.is_valid && td.node_type == "Visual").OrderBy(td => td.sorted_index))
                                {
                                    if (_selectedTableEntity != null && (tableDef.id == _selectedTableEntity.id || _currentDescendantIds.Contains(tableDef.id)))
                                        continue; // 排除当前节点及其后代节点
                                    <MudSelectItem Value="@tableDef.id"><i class="bi bi-door-open"></i> @tableDef.display_name</MudSelectItem>
                                }
                            </MudSelect>
                        </div>
                        <div>
                            <MudButton Class="ma-2 pa-2" Color="Color.Primary" Variant="Variant.Filled" OnClick="@(() => ChangeTableDefSortIndex(_selectedTableEntity.id, "up"))"> UP </MudButton>
                            <MudButton Class="ma-2 pa-2" Color="Color.Primary" Variant="Variant.Filled" OnClick="@(() => ChangeTableDefSortIndex(_selectedTableEntity.id, "down"))"> Down </MudButton>
                            <MudButton Class="ma-2 pa-2" Color="Color.Primary" Variant="Variant.Filled" OnClick="@(() => { _isBatchCreate = true; _isEditingColumn = false; StateHasChanged(); })"> 批量处理 </MudButton>
                        </div>
                    </div>
                    @*
                        <MudTextField Label="中文释义" @bind-Value="@_selectedTableEntity.note_cn" Margin="Margin.Dense" Variant="Variant.Outlined" AutoGrow MaxLines="4" />
                        <MudTextField Label="英文释义" @bind-Value="@_selectedTableEntity.note_en" Margin="Margin.Dense" Variant="Variant.Outlined" AutoGrow MaxLines="4" />
                        *@
                    <div class="d-flex">
                        <MudButton OnClick="OnTableSubmit" Class="ma-1 pa-2" Variant="Variant.Filled" Color="Color.Primary"> Submit </MudButton>
                        <MudButton OnClick="(async ()=>await OnTableDeleteAsync(_selectedTableEntity))" Class="ma-1 pa-2" Variant="Variant.Filled" Color="Color.Primary"> Delete </MudButton>
                    </div>
                </MudForm>

                @if (_selectedTableEntity.node_type == "Table")
                {
                    <Divider />
                    <h4>@_selectedTableEntity.display_name</h4>
                    <Table @ref="_pageElements_ColumnTable" TItem="column_definition" OnQueryAsync="OnColumnQueryAsync" 
                           ClickToSelect="true" OnClickRowCallback="@OnTableRowClick"
                           IsStriped HeaderStyle="TableHeaderStyle.Light" ShowExtendEditButton IsBordered 
                           ShowLineNo="true" LineNoText="序号" style="height: auto;">
                        <TableColumns>
                            <TableColumn @bind-Field="@context.unique_name" Text="唯一编码" Sortable="true" />
                            <TableColumn @bind-Field="@context.display_name" Text="默认显示名称" Sortable="true" />
                            <TableColumn @bind-Field="@context.data_type" Text="数据类型" Sortable="true" />
                            <TableColumn @bind-Field="@context.option_set" Text="选项内容" Sortable="true">
                                <Template Context="v">
                                    @if (v.Value != null && v.Value.Count > 2)
                                    {
                                        var displayCount = 2;
                                        var displayedItems = v.Value.Take(displayCount).ToList();
                                        var remainingCount = v.Value.Count - displayCount;
                                        <div style="display: flex; align-items: center;">
                                            <span>@string.Join(", ", displayedItems)</span>
                                            @if (remainingCount > 0)
                                            {
                                                <Tooltip Title="@string.Join(", ", v.Value)" CustomClass="is-valid">
                                                    <i class="bi bi-three-dots" />
                                                </Tooltip>
                                            }
                                        </div>
                                    }
                                    else
                                    {
                                        <span>@(v.Value != null ? string.Join(", ", v.Value) : "")</span>
                                    }
                                </Template>
                            </TableColumn>
                            <TableColumn @bind-Field="@context.id" Text="关联问题数" Sortable="true">
                                <Template Context="v">
                                    <span>@_columnQuestionCounts.GetValueOrDefault(v.Value, 0)</span>
                                </Template>
                            </TableColumn>
                            <TableColumn @bind-Field="@context.id" Text="删除">
                                <Template Context="value">
                                    <PopConfirmButton Placement="BPlacement.Top" Color="BColor.Danger" ConfirmIcon="fa-solid fa-triangle-exclamation text-danger" Size="BSize.ExtraSmall"
                                                      ConfirmButtonColor="BColor.Danger" Text=" 删除 " Content="确定删除数据吗？" IsAsync="true" OnConfirm="OnAsyncConfirm" />
                                </Template>
                            </TableColumn>
                        </TableColumns>
                    </Table>
                    <MudButton Class="ma-2 pa-2" Color="Color.Primary" Variant="Variant.Filled" OnClick="AddNewColumn"> ADD </MudButton>
                }
            }
        </MudPaper>
    </MudItem>

    <!-- 右侧内容区域 -->
    <MudItem xs="4" Class="right-panel">
        <MudPaper Class="right-panel-content mr-2" Elevation="0">
            @if (_isEditingColumn)
            {
                <Cmp_CubeColumnEditor ColumnId="@_selectedColumnIdForEdit" TableId="@_selectedTableEntity.id" OnSuccess="HandleColumnEditSuccess" OnDataChanged="HandleColumnDataChanged" />
            }
            @if (_isBatchCreate)
            {
                <Cmp_CubBatchCreate TableId="@_selectedTableEntity.id" OnSuccess="HandleColumnEditSuccess" />
            }
        </MudPaper>
    </MudItem>
</MudGrid>

@code {
    // 添加表格引用变量
    private Table<column_definition>? _pageElements_ColumnTable { get; set; }
    
    // 其他现有变量保持不变
    private bool _isVisible = false;
    private bool _isEditingColumn = false; // 控制是否显示列编辑组件
    private bool _isBatchCreate = false; // 控制是否显示批量处理组件
    private Guid? _selectedColumnIdForEdit = null; // 当前选中编辑的列ID
    private HashSet<Guid> _currentDescendantIds = new(); // Field to store descendant IDs

    // 新增字典，用于跟踪每个节点的展开状态
    private Dictionary<Guid, bool> _nodeExpansionStates { get; set; } = new();

    /// 树视图的根节点集合
    private List<TreeViewItem<table_definition>> _treeItemsList { get; set; } = new();

    /// 所有表定义的列表、列定义的列表
    private List<table_definition> _contextTableList { get; set; } = new();
    private Dictionary<Guid, table_definition> _tableDictionary { get; set; } = new();

    // 存储表和列关联的问题数量字典
    private Dictionary<Guid, int> _tableQuestionCounts { get; set; } = new();
    private Dictionary<Guid, int> _columnQuestionCounts { get; set; } = new();

    /// 当前选中的Table和Column对象
    private table_definition _selectedTableEntity { get; set; } = new();
    private column_definition _selectedColumnEntity { get; set; } = new();
    private column_definition? _tempColumnEntity { get; set; }
    private string _optionsSet
    {
        get
        {
            if (_tempColumnEntity == null) throw new ArgumentNullException(nameof(_tempColumnEntity));
            if (_tempColumnEntity.option_set.Count > 0)
            {
                return string.Join("\n", _tempColumnEntity.option_set);
            }
            return string.Empty;
        }
        set
        {
            if (_tempColumnEntity == null) throw new ArgumentNullException(nameof(_tempColumnEntity));
            // 将输入字符串拆分为列表，并移除空行
            value = value.Replace("###", "\n");
            List<string> optionsList = value.Split(new string[] { "\n" }, StringSplitOptions.RemoveEmptyEntries)
                                            .Select(option => option.Trim())  // 去除每行的首尾空格
                                            .Where(option => !string.IsNullOrWhiteSpace(option))  // 移除空行
                                            .ToList();

            // 去除重复项
            optionsList = optionsList.Distinct().ToList();

            _tempColumnEntity.option_set = optionsList;
        }
    }

    private TreeView<table_definition>? _pageElements_TreeView { get; set; } = new();

    private List<string> _autoCompleteItems { get; set; } = new();

    private IEnumerable<SelectedItem>? _nodeTypeItems = new SelectedItem[]
    {
        new ("Visual", "概念节点"),
        new ("Table", "实体节点")
    };

    private IEnumerable<SelectedItem>? _dataTypeItems = new SelectedItem[]
    {
        new ("选择", "选择"),
        new ("数值", "数值"),
        new ("文本", "文本"),
        new ("日期", "日期"),
        new ("布尔", "布尔"),
        new ("文件", "文件")
    };

    //=========================================================================

    // 设置TreeView展开层级的方法
    private void SetTreeViewLevel(int targetLevel)
    {
        if (_pageElements_TreeView == null) throw new ArgumentNullException(nameof(_pageElements_TreeView));
        foreach (var item in _pageElements_TreeView.Items)
        {
            SetExpansionState(item, currentDepth: 0, targetLevel);
        }

        StateHasChanged();
    }

    // 递归设置展开状态
    private void SetExpansionState(TreeViewItem<table_definition> item, int currentDepth, int targetLevel)
    {
        if (currentDepth < targetLevel)
        {
            item.IsExpand = true;
            item.ExpandIcon = "bi bi-door-open";
            _nodeExpansionStates[item.Value.id] = true; // 更新字典
        }
        else
        {
            item.IsExpand = false;
            item.ExpandIcon = "bi bi-door-closed-fill";
            _nodeExpansionStates[item.Value.id] = false; // 更新字典
        }

        if (item.Items != null && item.Items.Count > 0)
        {
            foreach (var child in item.Items)
            {
                SetExpansionState(child, currentDepth + 1, targetLevel);
            }
        }
    }

    private async Task OnAsyncConfirm()
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            // 确保实体被正确跟踪
            var columnToDelete = await context.column_definition
                .FirstOrDefaultAsync(c => c.id == _selectedColumnEntity.id);

            if (columnToDelete != null)
            {
                // 假删
                columnToDelete.is_valid = false;
                context.column_definition.Update(columnToDelete);
                await context.SaveChangesAsync();
                await transaction.CommitAsync();

                // 清除选中的列和编辑状态
                _selectedColumnIdForEdit = null;
                _isEditingColumn = false;

                // 刷新视图
                await RefreshTree();
                
                // 刷新列表格
                if (_pageElements_ColumnTable != null)
                {
                    await _pageElements_ColumnTable.QueryAsync();
                }
            }
        }
        catch (Exception ex)
        {
            await transaction.RollbackAsync();
            inj_logger.LogError(ex, "删除字段信息时出错:" + ex.Message);
            inj_snackbar.Add("删除失败", Severity.Error);
        }
    }

    private void AutoCompleteParentTableID(string value)
    {
        if (_tempColumnEntity == null) throw new ArgumentNullException(nameof(_tempColumnEntity));
        _autoCompleteItems = _contextTableList.Where(td => td.display_name.Contains(value) && td.node_type == "Table").OrderBy(td => td.display_name).Select(td => td.display_name).ToList();
        //当只有一个匹配项时，自动计算出对应的TableId
        if (_autoCompleteItems.Count == 1)
        {
            _tempColumnEntity.table_id = _contextTableList.Where(td => td.display_name == _autoCompleteItems[0]).First().id;
        }
    }

    protected override async Task OnPageInitializedAsync()
    {
        try
        {
            await RefreshTree();
        }
        catch (Exception ex)
        {
            inj_logger.LogError(ex, "初始化 Cube 页面时出错:" + ex.Message);
        }
    }

    private async Task RefreshTree()
    {
        var expandedStatesBackup = new Dictionary<Guid, bool>(_nodeExpansionStates); // 备份当前展开状态
        Guid? selectedTableId = _selectedTableEntity?.id; // 保存当前选中的表ID

        // 加载表和列定义，但不级联加载 form_questions
        _contextTableList = await inj_medDictService.GetValidTableTreeAsync(context);

        // 单独查询表关联的问题数量
        _tableQuestionCounts = await context.form_question
            .Where(q => q.table_definition_id != null)
            .GroupBy(q => q.table_definition_id)
            .Select(g => new { TableId = g.Key, Count = g.Count() })
            .ToDictionaryAsync(x => x.TableId, x => x.Count);

        // 单独查询列关联的问题数量
        _columnQuestionCounts = await context.form_question
            .Where(q => q.column_definition_id != null)
            .GroupBy(q => q.column_definition_id)
            .Select(g => new { ColumnId = g.Key, Count = g.Count() })
            .ToDictionaryAsync(x => x.ColumnId, x => x.Count);

        _tableDictionary = _contextTableList.ToDictionary(td => td.id, td => td);
        LoadAllTreeNodes();

        // 如果之前有选中的表，更新选中的表实体为最新数据
        if (selectedTableId.HasValue)
        {
            var refreshedTable = _contextTableList.FirstOrDefault(t => t.id == selectedTableId.Value);
            if (refreshedTable != null)
            {
                _selectedTableEntity = refreshedTable;
            }
        }

        _nodeExpansionStates = expandedStatesBackup; // 恢复展开状态

        // Update descendant IDs if a node is selected
        if (_selectedTableEntity != null && _selectedTableEntity.id != Guid.Empty)
        {
            // Use service to get descendant IDs (excluding self)
            _currentDescendantIds = await inj_medDictService.GetAllDescendantIdsAsync(context, _selectedTableEntity.id);
        }
        else
        {
            _currentDescendantIds.Clear();
        }

        StateHasChanged();
    }

    #region TreeView 相关操作
    private void LoadAllTreeNodes()
    {
        _treeItemsList.Clear();
        var rootNodes = _contextTableList.Where(td => td.parent_node_id == Guid.Empty).ToList();
        foreach (var rootNode in rootNodes)
        {
            _treeItemsList.Add(LoadTreeNodes(rootNode));
        }

        SetAllItemIcons(_treeItemsList);
    }

    private async Task OnTreeViewItemClick(TreeViewItem<table_definition> item)
    {
        if (item.Value.unique_name != "Root")
        {
            _selectedTableEntity = item.Value;

            // 清除列编辑状态
            _isEditingColumn = false;
            _selectedColumnIdForEdit = null;

            inj_logger.LogWarning($"Selected new table. ID: {_selectedTableEntity?.id}, Name: {_selectedTableEntity?.display_name}");
            
            // 刷新列表格
            if (_pageElements_ColumnTable != null && _selectedTableEntity.node_type == "Table")
            {
                await _pageElements_ColumnTable.QueryAsync();
            }
            
            await InvokeAsync(StateHasChanged);
        }
    }

    private TreeViewItem<table_definition> LoadTreeNodes(table_definition node)
    {
        var childrenNodes = _contextTableList.Where(td => td.parent_node_id == node.id).ToList();
        TreeViewItem<table_definition> treeItem = new(node);
        // 使用字典中的问题数量
        var questionCount = _tableQuestionCounts.GetValueOrDefault(node.id, 0);
        treeItem.Text = $"{node.display_name}" + (node.node_type == "Table" ? $" ({node.column_definition.Count}) [{questionCount}]" : "");

        // 根据字典设置展开状态
        if (_nodeExpansionStates.TryGetValue(node.id, out bool isExpanded))
        {
            treeItem.IsExpand = isExpanded;
            treeItem.ExpandIcon = isExpanded ? "bi bi-door-open" : "bi bi-door-closed-fill";
        }
        else
        {
            // 默认状态
            treeItem.IsExpand = false;
            treeItem.ExpandIcon = "bi bi-door-closed-fill";
        }

        foreach (var childrenNode in childrenNodes)
        {
            treeItem.Items.Add(LoadTreeNodes(childrenNode));
        }
        return treeItem;
    }

    private void AddTreeNode(table_definition newNode)
    {
        var parent = _treeItemsList.FirstOrDefault(t => t.Value.id == newNode.parent_node_id);
        if (parent != null)
        {
            parent.Items.Add(LoadTreeNodes(newNode));
        }
        else
        {
            _treeItemsList.Add(LoadTreeNodes(newNode));
        }
    }

    private void SetAllItemIcons(IEnumerable<TreeViewItem<table_definition>> items)
    {
        ApplyToAllTreeItems(item =>
        {
            item.Icon = item.Value.node_type == "Table" ? "bi bi-table" : "bi bi-door-closed-fill";
            item.ExpandIcon = item.IsExpand ? "bi bi-door-open" : "bi bi-door-closed-fill";
        }, items);
    }

    private void ApplyToAllTreeItems(Action<TreeViewItem<table_definition>> action, IEnumerable<TreeViewItem<table_definition>> items)
    {
        foreach (var item in items)
        {
            action(item);
            if (item.Items != null && item.Items.Any())
            {
                ApplyToAllTreeItems(action, item.Items);
            }
        }
    }

    // 新增方法，用于处理节点展开
    private async Task<IEnumerable<TreeViewItem<table_definition>>> OnExpandNodeAsync(TreeViewItem<table_definition> node)
    {
        inj_logger.LogDebug($"节点 {(node.IsExpand ? "展开" : "折叠")}：{node.Value.display_name}");
        _nodeExpansionStates[node.Value.id] = node.IsExpand;

        return await Task.FromResult(node.Items);
    }
    #endregion

    #region Table 相关操作
    private void AddNewTable()
    {
        var currID = _selectedTableEntity?.id ?? Guid.Empty;
        _selectedTableEntity = new();
        //_selectedTableEntity.Id = SequentialGuidGenerator.NewGuid();
        _selectedTableEntity.parent_node_id = currID;
        _selectedTableEntity.is_valid = true;
        // sorted_index will be calculated by the service
        StateHasChanged();
    }

    private Task OnTableRowClick(column_definition item)
    {
        _selectedColumnEntity = item;

        // 设置编辑列的ID和状态
        _selectedColumnIdForEdit = item.id;
        _isEditingColumn = true;
        _isBatchCreate = false;

        StateHasChanged();
        return Task.CompletedTask;
    }

    // Table表单提交事件处理函数
    private async Task OnTableSubmit()
    {
        if (_selectedTableEntity != null)
        {
            // Keep this UI-specific validation or validation that service doesn't handle
            _selectedTableEntity.unique_name = _selectedTableEntity.unique_name.ToLower();
            // 检查表名是否与所有列名重复 (Keep this validation here as requested)
            var allColumnNames = _selectedTableEntity.column_definition?.Select(c => c.unique_name) ?? Enumerable.Empty<string>();
            if (allColumnNames.Any(columnName => string.Equals(columnName, _selectedTableEntity.unique_name, StringComparison.OrdinalIgnoreCase)))
            {
                inj_logger.LogError("保存表定义时出错:表的唯一编码不能与任何列名重复");
                inj_snackbar.Add("保存表定义时出错:表的唯一编码不能与任何列名重复", Severity.Error);
                return;
            }
            // Basic length validation can stay here or be removed if service handles it robustly
            if (_selectedTableEntity.unique_name?.Length > 60)
            {
                inj_snackbar.Add("唯一编码长度不能超过60个字符", Severity.Error);
                return;
            }

            using var transaction = await context.Database.BeginTransactionAsync();
            try
            {
                bool isNew = _contextTableList.All(td => td.id != _selectedTableEntity.id);

                if (isNew)
                {
                    // Use service to add
                    await inj_medDictService.AddTableDefinitionAsync(context, _selectedTableEntity);
                    inj_snackbar.Add($"表 [{_selectedTableEntity.display_name}] 添加成功！", Severity.Success);
                }
                else
                {
                    // Use service to update
                    await inj_medDictService.UpdateTableDefinitionAsync(context, _selectedTableEntity);
                    inj_snackbar.Add($"表 [{_selectedTableEntity.display_name}] 信息更新成功！", Severity.Success);
                }

                await context.SaveChangesAsync(); // Save changes after service call
                await transaction.CommitAsync();
                await RefreshTree();
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                // Log the detailed exception from the service or context
                inj_logger.LogError(ex, "保存表定义时出错: {ErrorMessage}", ex.Message);
                // Provide a user-friendly message, potentially using ex.Message if it's safe/informative
                inj_snackbar.Add($"保存失败: {ex.Message}", Severity.Error);
                // No need to call RefreshTree here usually, as the operation failed.
            }
        }
    }

    private async Task ChangeTableDefSortIndex(Guid tableId, string direction)
    {
        inj_logger.LogInformation("开始调整排序: TableId={TableId}, Direction={Direction}", tableId, direction);

        using var transaction = await context.Database.BeginTransactionAsync(); // Keep transaction
        try
        {
            // Call service to handle sorting logic
            await inj_medDictService.ChangeTableSortOrderAsync(context, tableId, direction);

            // Save changes after successful service call
            await context.SaveChangesAsync();
            await transaction.CommitAsync(); // Commit transaction
            inj_logger.LogInformation("排序调整成功: TableId={TableId}", tableId);

            // Refresh UI after successful operation
            await RefreshTree();
        }
        catch (DbUpdateConcurrencyException ex) // Keep concurrency handling
        {
            await transaction.RollbackAsync();
            var entryDetails = ex.Entries.Select(e => $"Entity: {e.Metadata.Name}, State: {e.State}, ID: {e.Property("id")?.CurrentValue}");
            inj_logger.LogError(ex, "调整排序时发生并发冲突: TableId={TableId}, Direction={Direction}. Conflicting Entries: {EntryDetails}", tableId, direction, string.Join("; ", entryDetails));
            inj_snackbar.Add("保存排序时出现冲突，数据已被其他操作修改，已自动刷新，请重试。", Severity.Warning);
            await RefreshTree(); // Refresh to get latest data
        }
        catch (InvalidOperationException ex) // Catch specific exceptions from the service if needed
        {
            await transaction.RollbackAsync();
            inj_logger.LogWarning(ex, "调整排序操作无效: TableId={TableId}, Direction={Direction}. Reason: {Reason}", tableId, direction, ex.Message);
            inj_snackbar.Add($"排序操作无效: {ex.Message}", Severity.Warning);
            // No RefreshTree needed typically, state didn't change as intended
        }
        catch (Exception ex)
        {
            await transaction.RollbackAsync();
            inj_logger.LogError(ex, "调整排序时发生未知错误: TableId={TableId}, Direction={Direction}", tableId, direction);
            inj_snackbar.Add($"调整排序时发生未知错误: {ex.Message}", Severity.Error);
            // No RefreshTree needed typically
        }
    }

    private async Task OnTableDeleteAsync(table_definition table)
    {
        // Keep confirmation dialog
        var confirm = await inj_dialogService.ShowMessageBox(
            $"确定要删除表/节点【{table.display_name}】及其所有后代和关联字段吗？", // Updated confirmation message
            "此操作不可逆！", // Added warning
            yesText: "确定删除",
            cancelText: "取消",
            options: new DialogOptions { MaxWidth = MaxWidth.ExtraSmall } // Example option
        );

        if (confirm == true)
        {
            using var transaction = await context.Database.BeginTransactionAsync(); // Keep transaction
            try
            {
                // Call service to handle deletion logic
                await inj_medDictService.DeleteTableDefinitionAsync(context, table.id);

                // Save changes after successful service call
                await context.SaveChangesAsync();
                await transaction.CommitAsync(); // Commit transaction

                // Clear selection and refresh UI after successful deletion
                _selectedTableEntity = null;
                _isEditingColumn = false;
                _selectedColumnIdForEdit = null;
                inj_snackbar.Add($"节点 [{table.display_name}] 已成功删除。", Severity.Success); // Success message
                await RefreshTree();
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                inj_logger.LogError(ex, "删除表定义时出错: {ErrorMessage}", ex.Message);
                inj_snackbar.Add($"删除失败: {ex.Message}", Severity.Error);
                // No RefreshTree needed typically, state didn't change as intended
            }
        }
    }

    private async Task GetEnglishTableName()
    {
        var systemPrompt = "你是一个精通中英双语的临床医学专家，擅长将中文医学术语翻译成适合作为数据库表名的英文单词。";

        var q = $@"
目标：这是一个从中文到英文的翻译任务，旨在将用于表示临床医学概念的中文名称转换为适合作为数据库表名称的英文单词。

任务背景：我正在设计一个表达临床医学概念及其逻辑关系的树状结构。树的每个节点都有一个中文名称，现在需要将这些中文名称翻译成英文，以便将来直接作为数据库的表名称使用。

任务描述：请将中文词汇'{_selectedTableEntity.display_name}'翻译成一个适合作为数据库表名称的英文单词。

注意，翻译出的英文单词应当符合以下要求：
必须符合临床医学专业要求；
必须是一个英文单词，或者由下划线连接的多个英文单词，不能是短语或句子；
只允许包含小写英文字符和下划线；
不允许超过60个字符；不允许有空格；
不允许有数字；
应尽量与其它同级节点和父节点保持一致的命名风格；
容易被程序员理解和识别；
直接给出翻译结果，无需任何分析或解释。

背景信息：
当前节点的中文名称：{_selectedTableEntity.display_name}
节点层级关系：{GetParentNodeChainPathString(_selectedTableEntity)}
同级节点：{GetSiblingInfoString(_selectedTableEntity)}";
        inj_logger.LogDebug("LLM Query: " + q);

        _isVisible = true;
        // 使用Bio.AI替代原有的LLM服务
        var result = await BioAI.ThinkAsync(
            prompt: q,
            model: LLModel.OpenRouter.Gemini_25_Flash_05_20,
            systemPrompt: systemPrompt
        );
        _selectedTableEntity.unique_name = result.Answer;
        _isVisible = false;
    }

    //获得一个TableDefinition的从当前节点上溯到根节点，经过的所有级联节点的UniqueName和DisplayName
    private string GetParentNodeChainPathString(table_definition node, bool hasEN = true)
    {
        var currentNode = node;
        string parentNodeChainPathString = $"{currentNode.display_name}";
        while (currentNode?.parent_node_id != null && _contextTableList.Where(td => td.id == currentNode.parent_node_id).Count() > 0)
        {
            currentNode = _contextTableList.Where(td => td.id == currentNode.parent_node_id).First();
            if (hasEN)
                parentNodeChainPathString = $"{currentNode.unique_name}({currentNode.display_name}) -> " + parentNodeChainPathString;
            else
                parentNodeChainPathString = $"{currentNode.display_name} -> " + parentNodeChainPathString;
        }
        return parentNodeChainPathString;
    }

    //获得一个TableDefinition的的同级节点（拥有相同的父节点）的信息
    private string GetSiblingInfoString(table_definition node)
    {
        var siblingInfo = _contextTableList
            .Where(td => td.parent_node_id == node.parent_node_id && td.id != node.id)
            .Select(td => $"{td.unique_name}({td.display_name})")
            .ToList();
        return string.Join("; ", siblingInfo);
    }


    // 验证方法
    private IEnumerable<string> MaxCharacters(string ch)
    {
        if (!string.IsNullOrEmpty(ch) && 60 < ch?.Length)
            yield return "不要超过60个字符";
    }
    #endregion

    #region Column 相关操作
    private void AddNewColumn()
    {
        if (_selectedTableEntity == null || _selectedTableEntity.node_type != "Table") return;

        // 设置为添加模式
        _selectedColumnIdForEdit = null;
        _isEditingColumn = true;
        _isBatchCreate = false;

        StateHasChanged();
    }

    // 处理列编辑组件操作成功后的事件
    private async Task HandleColumnEditSuccess()
    {
        // 清除编辑状态
        _isEditingColumn = false;
        _selectedColumnIdForEdit = null;

        // 刷新数据
        await RefreshTree();
        
        // 刷新列表格
        if (_pageElements_ColumnTable != null)
        {
            await _pageElements_ColumnTable.QueryAsync();
        }
    }

    // 新增：处理列数据更改但不需要关闭编辑器的事件
    private async Task HandleColumnDataChanged()
    {
        // 只刷新数据，不关闭编辑器
        await RefreshTree();
        
        // 刷新列表格
        if (_pageElements_ColumnTable != null)
        {
            await _pageElements_ColumnTable.QueryAsync();
        }
    }
    #endregion

    // 添加列查询方法
    private Task<QueryData<column_definition>> OnColumnQueryAsync(QueryPageOptions options)
    {
        if (_selectedTableEntity == null || _selectedTableEntity.column_definition == null)
        {
            return Task.FromResult(new QueryData<column_definition>
            {
                Items = Enumerable.Empty<column_definition>(),
                TotalCount = 0,
                IsFiltered = false,
                IsSorted = false
            });
        }

        // 获取有效的列定义
        IEnumerable<column_definition> items = _selectedTableEntity.column_definition
            .Where(c => c.is_valid == true);

        // 处理排序
        var isSorted = false;
        if (!string.IsNullOrEmpty(options.SortName))
        {
            // 特殊处理关联问题数排序
            if (options.SortName == "id" && options.SortOrder == SortOrder.Desc)
            {
                items = items.OrderByDescending(c => _columnQuestionCounts.GetValueOrDefault(c.id, 0));
            }
            else if (options.SortName == "id" && options.SortOrder == SortOrder.Asc)
            {
                items = items.OrderBy(c => _columnQuestionCounts.GetValueOrDefault(c.id, 0));
            }
            // 特殊处理选项集合排序
            else if (options.SortName == "option_set" && options.SortOrder == SortOrder.Desc)
            {
                items = items.OrderByDescending(c => c.option_set.Count);
            }
            else if (options.SortName == "option_set" && options.SortOrder == SortOrder.Asc)
            {
                items = items.OrderBy(c => c.option_set.Count);
            }
            // 其他字段正常排序
            else
            {
                items = items.Sort(options.SortName, options.SortOrder);
            }
            isSorted = true;
        }
        else
        {
            // 默认按排序索引排序
            items = items.OrderBy(c => c.sorted_index);
        }

        return Task.FromResult(new QueryData<column_definition>
        {
            Items = items,
            TotalCount = items.Count(),
            IsFiltered = false,
            IsSorted = isSorted
        });
    }
}
