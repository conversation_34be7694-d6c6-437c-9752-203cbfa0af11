# Cursor 规则

## 代码修改规则

1. 只修改与当前任务直接相关的代码
2. 除非注释有误，否则不要删除或修改现有注释
3. 除非特别要求，否则不要更改日志消息或其语言
4. 保持现有的代码风格和格式
5. 保留所有与当前更改无关的现有功能
6. 除非特别需要，否则不要修改配置设置
7. 维护现有的错误处理和日志记录模式
8. 保持注释和日志消息的原有语言（如中文）
9. 除非必要，否则不要更改变量名或方法签名
10. 保留现有的文档和XML注释
11. 不要修改由EF Core Power Tools生成的文件（如实体类、DbContext）
12. 让EF Core Power Tools处理所有数据库模型更改和迁移

## 任务专注度

1. 每次更改都应该专注于当前的具体任务
2. 记录所做的更改及其必要性
3. 如果对修改有不确定，先请求澄清
4. 保持更改的最小化和针对性
5. 保持现有的架构和模式

## 数据库和Entity Framework Core

1. 所有实体类和DbContext的修改都应通过EF Core Power Tools完成
2. 不要手动编辑带有自动生成注释头的文件
3. 如果需要数据库更改，先修改数据库，然后使用EF Core Power Tools更新模型
4. 保留所有EF Core Power Tools生成的配置和映射

## 数据库上下文使用规则

1. 除非明确要求，否则不要改变组件中DbContext的使用方式
2. 继续使用父组件提供的DbContext以维持实体跟踪
3. 避免创建新的DbContext实例，因为这可能破坏实体跟踪
4. 如果确实需要新的DbContext，必须先说明原因并获得批准
5. 记住实体变更需要由同一个DbContext跟踪才能正确保存
6. 当多个操作修改同一个实体时，必须使用同一个DbContext
7. 保持现有的DbContext生命周期管理方式
8. 未经明确批准，不要在不同的DbContext使用模式之间切换（如从注入改为工厂模式）

## 命名规则

1. 类名使用PascalCase命名法
2. 接口必须以"I"开头，如IMessageHub
3. 服务类以"Service"结尾，如QuestionImageService
4. 组件类以"Cmp_"开头，如Cmp_QuestionConfig
5. 私有字段以下划线开头，如_logger
6. 依赖注入的服务变量以"inj_"开头，如inj_logger
7. 常量使用全大写，单词间用下划线分隔
8. 方法名使用PascalCase，动词开头
9. 异步方法以"Async"结尾
10. 布尔属性和变量应该用"Is"、"Has"、"Can"等开头
11. 实体类按照数据库表名命名，使用下划线分隔，如Form_Question

## 代码组织规则

1. 业务逻辑放在BusinessLogic目录
2. 数据访问层代码放在DataAccess目录
3. UI组件放在CubeBase/Components目录
4. 共享组件放在Components/Shared目录
5. 页面组件放在Components/Pages目录
6. 工具类和扩展方法放在Infrastructure目录
7. 模型类放在Models目录
8. 配置文件放在项目根目录

## 日志记录规则

1. 使用ILogger进行日志记录
2. 日志消息使用中文
3. 错误和警告级别的日志必须提供详细信息
4. 在关键操作点记录警告级别日志
5. 异常必须记录到日志
6. 数据库操作的关键步骤要记录日志
7. 日志中包含必要的上下文信息

## Blazor组件规则

1. 组件参数使用[Parameter]特性标记
2. 必需的参数使用required关键字
3. 事件回调使用EventCallback
4. 组件状态变更后调用StateHasChanged
5. 使用CascadingParameter传递上下文数据
6. 在OnInitialized/OnParametersSet中初始化数据
7. 异步操作使用OnInitializedAsync/OnParametersSetAsync
8. 组件间通信优先使用EventCallback

## 异步编程规则

1. 异步方法使用async/await模式
2. 异步方法名以Async结尾
3. 避免使用.Result和.Wait()
4. 正确处理异步操作的取消
5. 使用CancellationTokenSource管理取消操作
6. 在长时间运行的异步操作中支持取消
7. 异步方法应返回Task或Task<T>

## 依赖注入规则

1. 服务注册在Program.cs中配置
2. 使用构造函数注入依赖
3. 遵循依赖倒置原则
4. 合理使用服务生命周期（Singleton/Scoped/Transient）
5. 使用接口注入而不是具体类型
6. 在Blazor组件中使用[Inject]特性注入服务

## 错误处理规则

1. 使用try-catch块处理可预见的异常
2. 记录异常详细信息到日志
3. 在适当的抽象层次处理异常
4. 使用自定义异常类型表达业务规则违反
5. 避免捕获后不处理异常
6. 在API边界提供统一的错误响应格式

## 安全规则

1. 敏感配置信息使用配置文件存储
2. 密码等敏感数据必须加密存储
3. 使用HTTPS进行通信
4. 实现适当的身份验证和授权
5. 防止SQL注入和XSS攻击
6. 遵循最小权限原则

## 性能优化规则

1. 合理使用异步操作
2. 避免不必要的数据库查询
3. 使用适当的缓存策略
4. 延迟加载大型数据
5. 优化Entity Framework查询
6. 避免N+1查询问题
7. 使用适当的数据分页