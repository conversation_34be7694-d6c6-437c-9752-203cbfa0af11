﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace DataAccess.Models;

public partial class sys_region
{
    /// <summary>
    /// 院区ID
    /// </summary>
    public Guid id { get; set; }

    /// <summary>
    /// 医院ID
    /// </summary>
    public Guid hospital_id { get; set; }

    /// <summary>
    /// 院区名称
    /// </summary>
    public string name { get; set; }

    public virtual sys_hospital hospital { get; set; }

    public virtual ICollection<sys_ward> sys_ward { get; set; } = new List<sys_ward>();
}