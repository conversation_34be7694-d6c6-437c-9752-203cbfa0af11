using BitzArt.Blazor.Auth;
using BitzArt.Blazor.Cookies;
using BusinessLogic.Auth;
using BusinessLogic.Cube;
using CubeBase.Components;
using DataAccess.Models;
using Easy.MessageHub;
using Microsoft.EntityFrameworkCore;
using MudBlazor;
using MudBlazor.Services;
using NLog;
using NLog.Web;
using System.Globalization;

namespace CubeBase
{
    public class Program
    {
        public static void Main(string[] args)
        {
            // NLog
            var logger = LogManager.Setup().LoadConfigurationFromFile("nlog.config").GetCurrentClassLogger();
            try
            {
                logger.Debug("启动应用程序...");

                var cultureInfo = new CultureInfo("zh-CN");
                CultureInfo.DefaultThreadCurrentCulture = cultureInfo;
                CultureInfo.DefaultThreadCurrentUICulture = cultureInfo;

                var builder = WebApplication.CreateBuilder(args);
                builder.Services.AddRazorComponents()
                    .AddInteractiveServerComponents();

                // 清除现有的日志提供程序
                builder.Logging.ClearProviders();
                builder.Logging.SetMinimumLevel(Microsoft.Extensions.Logging.LogLevel.Debug);

                // 使用NLog作为日志提供程序
                builder.Host.UseNLog();

                // 添加appsettings.json文件
                builder.Configuration.AddJsonFile("appsettings.json", optional: false, reloadOnChange: true);

                builder.Services.AddSignalR(options =>
                {
                    options.MaximumReceiveMessageSize = 5 * 1024 * 1024; // 设置为5MB
                });

                // 连接数据库
                var connectionString = builder.Configuration.GetConnectionString("DefaultConnection") ?? throw new InvalidOperationException("Connection string 'DefaultConnection' not found.");
                builder.Services.AddDbContextFactory<CubeContext>(options => options
                    .UseNpgsql(connectionString, npgsqlOptions => npgsqlOptions.UseQuerySplittingBehavior(QuerySplittingBehavior.SplitQuery)),
                    ServiceLifetime.Scoped);

                // 添加BlazorCookies
                builder.AddBlazorCookies();
                builder.Services.AddScoped<JwtTokenService>();
                builder.AddBlazorAuth<ServerSideAuthService>();

                // 添加MudServices
                builder.Services.AddMudServices(config =>
                {
                    config.SnackbarConfiguration.PositionClass = Defaults.Classes.Position.BottomLeft;
                    config.SnackbarConfiguration.PreventDuplicates = false;
                    config.SnackbarConfiguration.NewestOnTop = true;
                    config.SnackbarConfiguration.ShowCloseIcon = true;
                    config.SnackbarConfiguration.VisibleStateDuration = 8000;
                    config.SnackbarConfiguration.HideTransitionDuration = 800;
                });
                builder.Services.AddMudMarkdownServices();

                // 添加BootstrapBlazor
                builder.Services.AddBootstrapBlazor();

                // 添加MessageHub
                builder.Services.AddScoped<IMessageHub, MessageHub>();

                // 添加AutoMapper
                builder.Services.AddAutoMapper(typeof(MappingProfile));

                // 添加MedDictService
                builder.Services.AddScoped<IMedDictService, MedDictService>();

                builder.Services.AddScoped<CardService>();
                builder.Services.AddScoped<CardExportService>();
                builder.Services.AddScoped<CardImportService>();

                // 注册 DataDictLLMService 作为 IDataDictLLMService 接口的实现
                builder.Services.AddScoped<IDataDictLLMService, DataDictLLMService>();

                // 添加内存缓存服务
                builder.Services.AddMemoryCache();

                if (!builder.Environment.IsDevelopment())
                {
                    builder.Services.AddDatabaseDeveloperPageExceptionFilter();
                }

                var app = builder.Build();

                // 使用NLog记录应用程序启动信息
                logger.Info("应用程序启动");

                // Configure the HTTP request pipeline.
                if (!app.Environment.IsDevelopment())
                {
                    app.UseExceptionHandler("/Error");
                    // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
                    app.UseHsts();
                }
                app.UseHttpsRedirection();

                app.UseStaticFiles();
                app.UseAntiforgery();

                app.MapRazorComponents<App>()
                    .AddInteractiveServerRenderMode();
                app.Run();
            }
            catch (Exception ex)
            {
                // 记录应用程序启动时的异常
                logger.Error(ex, "应用程序启动时发生错误");
                throw;
            }
            finally
            {
                // 记录日志信息
                LogManager.Shutdown();
            }
        }
    }
}