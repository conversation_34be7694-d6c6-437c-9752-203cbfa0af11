﻿@inject IDialogService inj_dialogService
@inject ILogger<Cmp_FormTab> inj_logger
@inject IDbContextFactory<CubeContext> ContextFactory
@inject ISnackbar inj_snackbar
@implements IAsyncDisposable
@using System.Threading
@using System.Diagnostics

<MudTabs Elevation="1" Rounded="true" HeaderPosition="TabHeaderPosition.Before" Style="height:100%"
         ActiveTabClass="border-solid border-2 mud-border-primary"
         ActivePanelIndex="_activeTabIndex"
         ActivePanelIndexChanged="OnTabIndexChanged">
    <Header>
        <MudButtonGroup>
            <!--MudButton Variant="Variant.Filled" Color="Color.Primary" OnClick="SaveFormSet">保存</MudButton>
            <MudButton Variant="Variant.Filled" Color="Color.Primary" OnClick="ResetFormSet">重置</MudButton-->
            <MudIconButton Class="h-100" Icon="@Icons.Material.Filled.Add" OnClick="OpenFormDialog" />
        </MudButtonGroup>
    </Header>
    <ChildContent>
        @if (_currFormSet != null)
        {
            @for (int i = 0; i < _currFormSet.form_form.Count; i++)
            {
                var formIndex = i; // 捕获局部变量
                var form = _currFormSet.form_form.OrderBy(o => o.sort_index).ToArray()[formIndex];
                
                <MudTabPanel Text="@form.name" Tag="@form.id" ToolTip="@form.group_name">
                    @if (_activeTabIndex == formIndex && _loadedFormDetails.ContainsKey(form.id))
                    {
                        <Cmp_Form _context="@_parentContext" 
                                 Form="@_loadedFormDetails[form.id]" 
                                 OnParentRefresh="@OnFormContentChanged" />
                    }
                    else if (_activeTabIndex == formIndex)
                    {
                        <div class="d-flex justify-center align-center" style="height: 200px;">
                            <MudProgressCircular Color="Color.Primary" Indeterminate="true" />
                            <MudText Class="ml-2">正在加载表单详细数据...</MudText>
                        </div>
                    }
                </MudTabPanel>
            }
        }
    </ChildContent>
    <TabPanelHeader>
        <MudIconButton Icon="@Icons.Material.Filled.Edit" Size="Size.Small" OnClick="@((_) => EditFormName(context))" />
        <MudIconButton Class="ml-2 pa-1" Color="Color.Error" Icon="@Icons.Material.Filled.Remove"
                       OnClick="@((_) => RemoveTab(context))" />
    </TabPanelHeader>
    
</MudTabs>

@code {
    [Parameter]
    public required Guid FormSetID { get; set; }
    
    [Parameter]
    public required CubeContext _parentContext { get; set; }

    [CascadingParameter]
    public Dictionary<Guid, int> UsedColnums { get; set; } = new();

    private form_form_set? _currFormSet;
    private System.Threading.Timer? _autoSaveTimer;
    private readonly SemaphoreSlim _saveSemaphore = new(1, 1);
    private bool _hasUnsavedChanges = false;

    // 新增：分阶段加载相关字段
    private int _activeTabIndex = 0;
    private readonly Dictionary<Guid, form_form> _loadedFormDetails = new();
    private readonly SemaphoreSlim _loadingSemaphore = new(1, 1);
    private bool _isInitializing = false;

    protected override async Task OnInitializedAsync()
    {
        var stopwatch = Stopwatch.StartNew();
        inj_logger.LogWarning("[性能调试] Cmp_FormTab.OnInitializedAsync 开始 - FormSetID: {FormSetID}", FormSetID);
        
        _isInitializing = true;
        try
        {
            var sw1 = Stopwatch.StartNew();
            await LoadFormSetBasicAsync();
            sw1.Stop();
            inj_logger.LogWarning("[性能调试] LoadFormSetBasicAsync 耗时: {ElapsedMs}ms", sw1.ElapsedMilliseconds);
            
            // 启动自动保存定时器，每5秒检查一次是否需要保存
            _autoSaveTimer = new System.Threading.Timer(AutoSaveCallback, null, TimeSpan.FromSeconds(5), TimeSpan.FromSeconds(5));

            // 预加载第一个表单的详细数据
            if (_currFormSet?.form_form?.Any() == true)
            {
                var firstForm = _currFormSet.form_form.OrderBy(o => o.sort_index).First();
                var sw2 = Stopwatch.StartNew();
                await LoadFormDetailsAsync(firstForm.id);
                sw2.Stop();
                inj_logger.LogWarning("[性能调试] 预加载第一个表单详细数据耗时: {ElapsedMs}ms - FormID: {FormID}", sw2.ElapsedMilliseconds, firstForm.id);
            }
        }
        finally
        {
            _isInitializing = false;
            stopwatch.Stop();
            inj_logger.LogWarning("[性能调试] Cmp_FormTab.OnInitializedAsync 总耗时: {ElapsedMs}ms", stopwatch.ElapsedMilliseconds);
        }
    }

    protected override async Task OnParametersSetAsync()
    {
        var stopwatch = Stopwatch.StartNew();
        inj_logger.LogWarning("[性能调试] Cmp_FormTab.OnParametersSetAsync 开始 - 当前FormSetID: {CurrentID}, 新FormSetID: {NewID}", _currFormSet?.id, FormSetID);

        // 检查 FormSetID 是否发生变化
        if (_currFormSet?.id != FormSetID)
        {
            var changeStopwatch = Stopwatch.StartNew();
            inj_logger.LogWarning("[性能调试] FormSetID 发生变化，开始切换处理");
            
            _isInitializing = true;
            try
            {
                // 清空之前的详细数据缓存
                var clearTime = Stopwatch.StartNew();
                _loadedFormDetails.Clear();
                clearTime.Stop();
                inj_logger.LogWarning("[性能调试] 清空缓存耗时: {ElapsedMs}ms", clearTime.ElapsedMilliseconds);

                var loadBasicTime = Stopwatch.StartNew();
                await LoadFormSetBasicAsync();
                loadBasicTime.Stop();
                inj_logger.LogWarning("[性能调试] 切换时LoadFormSetBasicAsync耗时: {ElapsedMs}ms", loadBasicTime.ElapsedMilliseconds);
                
                // 预加载第一个表单的详细数据
                if (_currFormSet?.form_form?.Any() == true)
                {
                    var firstForm = _currFormSet.form_form.OrderBy(o => o.sort_index).First();
                    var loadDetailTime = Stopwatch.StartNew();
                    await LoadFormDetailsAsync(firstForm.id);
                    loadDetailTime.Stop();
                    inj_logger.LogWarning("[性能调试] 切换时加载第一个表单详细数据耗时: {ElapsedMs}ms", loadDetailTime.ElapsedMilliseconds);
                }
                
                // 重置Tab索引
                _activeTabIndex = 0;
                
                changeStopwatch.Stop();
                inj_logger.LogWarning("[性能调试] FormSetID切换处理总耗时: {ElapsedMs}ms", changeStopwatch.ElapsedMilliseconds);
            }
            finally
            {
                _isInitializing = false;
            }
        }
        await base.OnParametersSetAsync();

        stopwatch.Stop();
        inj_logger.LogWarning("[性能调试] Cmp_FormTab.OnParametersSetAsync 总耗时: {ElapsedMs}ms", stopwatch.ElapsedMilliseconds);
    }

    /// <summary>
    /// 只加载表单集的基础信息，不包括详细的卡片和问题数据
    /// </summary>
    private async Task LoadFormSetBasicAsync()
    {
        var stopwatch = Stopwatch.StartNew();
        inj_logger.LogWarning("[性能调试] LoadFormSetBasicAsync 开始 - FormSetID: {FormSetID}", FormSetID);
        
        try
        {
            _currFormSet = await _parentContext.form_form_set
                .Include(fs => fs.form_form.OrderBy(f => f.sort_index))
                .FirstOrDefaultAsync(fs => fs.id == FormSetID);
                
            stopwatch.Stop();
            var formCount = _currFormSet?.form_form?.Count ?? 0;
            inj_logger.LogWarning("[性能调试] LoadFormSetBasicAsync 完成 - 耗时: {ElapsedMs}ms, 表单数量: {FormCount}", stopwatch.ElapsedMilliseconds, formCount);
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            inj_logger.LogError(ex, "[性能调试] LoadFormSetBasicAsync 发生错误 - 耗时: {ElapsedMs}ms, FormSetID: {FormSetID}", stopwatch.ElapsedMilliseconds, FormSetID);
            throw;
        }
    }

    /// <summary>
    /// 按需加载指定表单的详细数据（卡片和问题）
    /// </summary>
    private async Task LoadFormDetailsAsync(Guid formId)
    {
        var stopwatch = Stopwatch.StartNew();
        inj_logger.LogWarning("[性能调试] LoadFormDetailsAsync 开始 - FormID: {FormID}", formId);
        
        if (_loadedFormDetails.ContainsKey(formId) || !await _loadingSemaphore.WaitAsync(100))
        {
            stopwatch.Stop();
            inj_logger.LogWarning("[性能调试] LoadFormDetailsAsync 跳过 - 已缓存或正在加载中 - 耗时: {ElapsedMs}ms", stopwatch.ElapsedMilliseconds);
            return; // 已加载或正在加载中
        }

        try
        {
            var queryStopwatch = Stopwatch.StartNew();
            var formWithDetails = await _parentContext.form_form
                .Include(f => f.form_card)
                .ThenInclude(c => c.form_question)
                .ThenInclude(q => q.column_definition)
                .FirstOrDefaultAsync(f => f.id == formId);
            queryStopwatch.Stop();

            if (formWithDetails != null)
            {
                _loadedFormDetails[formId] = formWithDetails;
                
                var cardCount = formWithDetails.form_card.Count;
                var questionCount = formWithDetails.form_card.Sum(c => c.form_question.Count);
                
                stopwatch.Stop();
                inj_logger.LogWarning("[性能调试] LoadFormDetailsAsync 完成 - FormID: {FormID}, 数据库查询耗时: {QueryMs}ms, 总耗时: {TotalMs}ms, 卡片数: {CardCount}, 问题数: {QuestionCount}", 
                    formId, queryStopwatch.ElapsedMilliseconds, stopwatch.ElapsedMilliseconds, cardCount, questionCount);
                
                // 只有在非初始化阶段且数据加载完成后才触发UI更新
                if (!_isInitializing)
                {
                    var stateChangeStopwatch = Stopwatch.StartNew();
                    StateHasChanged();
                    stateChangeStopwatch.Stop();
                    inj_logger.LogWarning("[性能调试] StateHasChanged 耗时: {ElapsedMs}ms", stateChangeStopwatch.ElapsedMilliseconds);
                }
            }
            else
            {
                stopwatch.Stop();
                inj_logger.LogWarning("[性能调试] LoadFormDetailsAsync 未找到表单 - FormID: {FormID}, 耗时: {ElapsedMs}ms", formId, stopwatch.ElapsedMilliseconds);
            }
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            inj_logger.LogError(ex, "[性能调试] LoadFormDetailsAsync 发生错误 - FormID: {FormID}, 耗时: {ElapsedMs}ms", formId, stopwatch.ElapsedMilliseconds);
        }
        finally
        {
            _loadingSemaphore.Release();
        }
    }

    /// <summary>
    /// Tab切换时的处理，按需加载数据
    /// </summary>
    private async Task OnTabIndexChanged(int newIndex)
    {
        var stopwatch = Stopwatch.StartNew();
        inj_logger.LogWarning("[性能调试] OnTabIndexChanged 开始 - 从Tab {OldIndex} 切换到 Tab {NewIndex}", _activeTabIndex, newIndex);
        
        if (_currFormSet?.form_form == null || newIndex >= _currFormSet.form_form.Count || newIndex < 0)
        {
            stopwatch.Stop();
            inj_logger.LogWarning("[性能调试] OnTabIndexChanged 参数无效 - 耗时: {ElapsedMs}ms", stopwatch.ElapsedMilliseconds);
            return;
        }

        _activeTabIndex = newIndex;
        var forms = _currFormSet.form_form.OrderBy(o => o.sort_index).ToArray();
        var selectedForm = forms[newIndex];

        // 如果该表单的详细数据还未加载，则异步加载
        if (!_loadedFormDetails.ContainsKey(selectedForm.id))
        {
            inj_logger.LogWarning("[性能调试] Tab切换需要加载新数据 - FormName: {FormName}", selectedForm.name);
            await LoadFormDetailsAsync(selectedForm.id);
            // LoadFormDetailsAsync 中已经处理了 StateHasChanged
        }
        else
        {
            inj_logger.LogWarning("[性能调试] Tab切换使用缓存数据 - FormName: {FormName}", selectedForm.name);
        }
        
        stopwatch.Stop();
        inj_logger.LogWarning("[性能调试] OnTabIndexChanged 完成 - 总耗时: {ElapsedMs}ms", stopwatch.ElapsedMilliseconds);
    }

    /// <summary>
    /// 表单内容变化时的回调，标记为有未保存更改
    /// </summary>
    private void OnFormContentChanged()
    {
        MarkAsChanged();
        // 不需要调用 StateHasChanged，因为数据变化会自动更新UI
    }

    /// <summary>
    /// 保持原有的完整加载方法，用于需要完整数据的操作
    /// </summary>
    private async Task LoadFormSetAsync()
    {
        try
        {
            _currFormSet = await _parentContext.form_form_set
                .Include(td => td.form_form)
                .ThenInclude(f => f.form_card)// Card有FormID的外键，这里会加载Form下的所有级联Card，不会遗漏
                .ThenInclude(c => c.form_question)
                .ThenInclude(q => q.column_definition)
                .FirstOrDefaultAsync(td => td.id == FormSetID);
        }
        catch (Exception ex)
        {
            inj_logger.LogError(ex, "[LoadFormSetAsync] 加载表单集数据时发生错误 - FormSetID: {FormSetID}", FormSetID);
            throw;
        }
    }

    private void AutoSaveCallback(object? state)
    {
        // 使用 Task.Run 在后台线程执行异步操作
        _ = Task.Run(async () => await AutoSaveAsync());
    }

    private async Task AutoSaveAsync()
    {
        if (!_hasUnsavedChanges || !await _saveSemaphore.WaitAsync(100))
        {
            return;
        }

        try
        {
            if (_parentContext.ChangeTracker.HasChanges())
            {
                await _parentContext.SaveChangesAsync();
                _hasUnsavedChanges = false;
                inj_logger.LogInformation("[AutoSave] 自动保存成功");
            }
        }
        catch (Exception ex)
        {
            inj_logger.LogError(ex, "[AutoSave] 自动保存失败");
        }
        finally
        {
            _saveSemaphore.Release();
        }
    }

    private void MarkAsChanged()
    {
        _hasUnsavedChanges = true;
    }

    private async Task OpenFormDialog()
    {
        var parameters = new DialogParameters()
            {
                ["FormSet"] = _currFormSet,
                ["OnFormModalClosed"] = EventCallback.Factory.Create(this, async (form_form newForm) =>
                {
                    await using var scope = await ContextFactory.CreateDbContextAsync();
                    using var transaction = await scope.Database.BeginTransactionAsync();
                    try 
                    {
                        var formSet = await scope.form_form_set
                            .Include(fs => fs.form_form)
                            .FirstOrDefaultAsync(fs => fs.id == _currFormSet.id);
                        
                        formSet.form_form.Add(newForm);
                        await scope.SaveChangesAsync();
                        await transaction.CommitAsync();
                        
                        await LoadFormSetBasicAsync();

                        // 切换到新添加的表单
                        if (_currFormSet?.form_form?.Any() == true)
                        {
                            var forms = _currFormSet.form_form.OrderBy(o => o.sort_index).ToArray();
                            var newFormIndex = Array.FindIndex(forms, f => f.id == newForm.id);
                            if (newFormIndex >= 0)
                            {
                                _activeTabIndex = newFormIndex;
                                // 立即加载新表单的详细数据
                                await LoadFormDetailsAsync(newForm.id);
                            }
                        }
                        
                        // 数据变化会自动触发UI更新，不需要手动调用 StateHasChanged
                    }
                    catch (Exception ex)
                    {
                        await transaction.RollbackAsync();
                        inj_logger.LogError(ex, "添加表单失败");
                        inj_snackbar.Add("添加表单失败", Severity.Error);
                    }
                })
            };
        
        await inj_dialogService.ShowAsync<Modal_Form>("添加表单", parameters);
    }

    private async Task EditFormName(MudTabPanel tabPanel)
    {
        var formToUpdate = _currFormSet.form_form.FirstOrDefault(f => f.id == (Guid)tabPanel.Tag);
        var parameters = new DialogParameters()
            {
                ["FormSet"] = _currFormSet,
                ["ExistingForm"] = formToUpdate,
                ["OnFormModalClosed"] = EventCallback.Factory.Create(this, async (form_form updatedForm) =>
                {
                    if (updatedForm != null)
                    {
                        formToUpdate.name = updatedForm.name;
                        formToUpdate.group_name = updatedForm.group_name;
                        MarkAsChanged();
                        await LoadFormSetBasicAsync();
                        // 更新缓存中的数据
                        if (_loadedFormDetails.ContainsKey(formToUpdate.id))
                        {
                            _loadedFormDetails[formToUpdate.id].name = updatedForm.name;
                            _loadedFormDetails[formToUpdate.id].group_name = updatedForm.group_name;
                        }
                        // 数据绑定会自动更新UI
                    }
                })
            };
        var options = new DialogOptions { 
            MaxWidth = MaxWidth.ExtraSmall, 
            FullWidth = true, 
            CloseButton = true, 
            CloseOnEscapeKey = true 
        };
        var dialog = await inj_dialogService.ShowAsync<Modal_Form>("编辑表单", parameters, options);
    }

    private async Task RemoveTab(MudTabPanel tabPanel)
    {
        // 弹出确认对话框
        var confirm = await inj_dialogService.ShowMessageBox($"确定要删除表单【{tabPanel.Text}】吗？", "", yesText: "确定", cancelText: "取消");
        if (confirm == true)
        {
            // 获取要删除的表单
            var formToDelete = _currFormSet.form_form.FirstOrDefault(f => f.id == (Guid)tabPanel.Tag);
            if (formToDelete != null)
            {
                // 获取删除表单在当前排序中的索引
                var forms = _currFormSet.form_form.OrderBy(o => o.sort_index).ToArray();
                var deletingIndex = Array.FindIndex(forms, f => f.id == formToDelete.id);

                // 先删除关联的卡片和问题
                foreach (var card in formToDelete.form_card.ToList())
                {
                    // 删除卡片下的所有问题
                    foreach (var question in card.form_question.ToList())
                    {
                        _parentContext.form_question.Remove(question);
                    }
                    _parentContext.form_card.Remove(card);
                }
                _parentContext.form_form.Remove(formToDelete); // 删除数据库中的表单

                // 从缓存中移除
                _loadedFormDetails.Remove(formToDelete.id);

                // 调整活动Tab索引
                if (deletingIndex >= 0)
                {
                    if (deletingIndex == _activeTabIndex && _activeTabIndex > 0)
                    {
                        _activeTabIndex--; // 如果删除的是当前Tab且不是第一个，切换到前一个
                    }
                    else if (deletingIndex < _activeTabIndex)
                    {
                        _activeTabIndex--; // 如果删除的Tab在当前Tab之前，索引需要减1
                    }
                    // 如果删除后没有Tab了，索引设为0
                    if (_currFormSet.form_form.Count <= 1)
                    {
                        _activeTabIndex = 0;
                    }
                }

                var entries = _parentContext.ChangeTracker.Entries().ToList();
                foreach (var entry in entries)
                {
                    inj_logger.LogWarning($"Entity: {entry.Entity.GetType().Name}, State: {entry.State}");
                }

                await _parentContext.SaveChangesAsync();
                await LoadFormSetBasicAsync();

                // 确保索引在有效范围内
                if (_currFormSet?.form_form?.Any() == true)
                {
                    var maxIndex = _currFormSet.form_form.Count - 1;
                    if (_activeTabIndex > maxIndex)
                    {
                        _activeTabIndex = maxIndex;
                    }
                }
                else
                {
                    _activeTabIndex = 0;
                }
                
                // 删除操作完成后，数据绑定会自动更新UI
            }
        }
    }

    public async ValueTask DisposeAsync()
    {
        if (_autoSaveTimer != null)
        {
            await _autoSaveTimer.DisposeAsync();
        }
        
        // 在组件销毁前进行最后一次保存
        if (_hasUnsavedChanges && await _saveSemaphore.WaitAsync(1000))
        {
            try
            {
                if (_parentContext.ChangeTracker.HasChanges())
                {
                    await _parentContext.SaveChangesAsync();
                    inj_logger.LogInformation("[Dispose] 组件销毁前保存成功");
                }
            }
            catch (Exception ex)
            {
                inj_logger.LogError(ex, "[Dispose] 组件销毁前保存失败");
            }
            finally
            {
                _saveSemaphore.Release();
            }
        }
        
        _saveSemaphore.Dispose();
        _loadingSemaphore.Dispose();
    }
}


<style>
.mud-tabs-panels{
overflow-y:auto;
flex: 1;
}
</style>