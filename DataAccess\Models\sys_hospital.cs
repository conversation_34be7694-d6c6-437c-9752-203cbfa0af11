﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace DataAccess.Models;

public partial class sys_hospital
{
    /// <summary>
    /// 医院ID
    /// </summary>
    public Guid id { get; set; }

    /// <summary>
    /// 医院名称
    /// </summary>
    public string name { get; set; }

    public virtual ICollection<form_card> form_card { get; set; } = new List<form_card>();

    public virtual ICollection<form_form> form_form { get; set; } = new List<form_form>();

    public virtual ICollection<form_form_set> form_form_set { get; set; } = new List<form_form_set>();

    public virtual ICollection<form_project> form_project { get; set; } = new List<form_project>();

    public virtual ICollection<form_question> form_question { get; set; } = new List<form_question>();

    public virtual ICollection<patient> patient { get; set; } = new List<patient>();

    public virtual ICollection<sys_department> sys_department { get; set; } = new List<sys_department>();

    public virtual ICollection<sys_permission_data> sys_permission_data { get; set; } = new List<sys_permission_data>();

    public virtual ICollection<sys_region> sys_region { get; set; } = new List<sys_region>();

    public virtual ICollection<sys_ward> sys_ward { get; set; } = new List<sys_ward>();
}