@inherits Cmp_Base
@inject CardService cardService
@inject ILogger<Cmp_CardIntegrityCheck> Logger
@inject ISnackbar Snackbar

<MudDialog>
    <DialogContent>
        <div class="card my-4">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">链表完整性检查 - @currentForm?.name</h5>
            </div>
            <div class="card-body">
                @if (isLoading)
                {
                    <div class="text-center p-4">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <p class="mt-2">正在检查链表完整性，请稍候...</p>
                    </div>
                }
                else if (issues != null && issues.Any())
                {
                    <div class="alert alert-warning">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        发现 @issues.Count 个链表完整性问题
                    </div>
                    
                    <div class="list-group mt-3">
                        @foreach (var issue in issues)
                        {
                            <div class="list-group-item list-group-item-action flex-column align-items-start @(issue.IsFixed ? "list-group-item-success" : "")">
                                <div class="d-flex w-100 justify-content-between">
                                    <h5 class="mb-1">
                                        @if (issue.IsFixed)
                                        {
                                            <i class="bi bi-check-circle-fill text-success me-2"></i>
                                        }
                                        @GetIssueTypeDisplayName(issue.IssueType)
                                    </h5>
                                    @if (!issue.IsFixed)
                                    {
                                        <button class="btn btn-sm btn-outline-primary" @onclick="() => FixIssue(issue)">
                                            修复问题
                                        </button>
                                    }
                                </div>
                                <p class="mb-1">@issue.Description</p>
                                <small class="text-muted">影响容器: @issue.ContainerName</small>
                                
                                @if (showDetails.ContainsKey(issue) && showDetails[issue])
                                {
                                    <div class="mt-2 border-top pt-2">
                                        <h6>影响项目列表:</h6>
                                        <div class="table-responsive">
                                            <table class="table table-sm table-striped">
                                                <thead>
                                                    <tr>
                                                        <th>类型</th>
                                                        <th>名称</th>
                                                        <th>ID</th>
                                                        <th>前驱ID</th>
                                                        <th>后继ID</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    @foreach (var item in issue.AffectedItems)
                                                    {
                                                        <tr>
                                                            <td>@item.item_type</td>
                                                            <td>@item.name</td>
                                                            <td><small>@item.id</small></td>
                                                            <td><small>@(item.pre_uid?.ToString() ?? "null")</small></td>
                                                            <td><small>@(item.next_uid?.ToString() ?? "null")</small></td>
                                                        </tr>
                                                    }
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                }
                                
                                <button class="btn btn-sm btn-link p-0 mt-2" @onclick="() => ToggleDetails(issue)">
                                    @(showDetails.ContainsKey(issue) && showDetails[issue] ? "隐藏详情" : "显示详情")
                                </button>
                            </div>
                        }
                    </div>
                    
                    @if (issues.Any(i => !i.IsFixed))
                    {
                        <button class="btn btn-primary mt-3" @onclick="FixAllIssues">
                            <i class="bi bi-wrench me-2"></i>修复所有问题
                        </button>
                    }
                }
                else
                {
                    <div class="alert alert-success">
                        <i class="bi bi-check-circle me-2"></i>
                        没有发现链表完整性问题，所有链表结构正常！
                    </div>
                }
                
                <div class="d-flex justify-content-between mt-4">
                    <button class="btn btn-outline-secondary" @onclick="CloseDialog">
                        <i class="bi bi-arrow-left me-2"></i>关闭
                    </button>
                    <button class="btn btn-outline-primary" @onclick="RefreshCheck">
                        <i class="bi bi-arrow-clockwise me-2"></i>重新检查
                    </button>
                </div>
            </div>
        </div>
    </DialogContent>
</MudDialog>

@if (showFixConfirmation)
{
    <div class="modal fade show" style="display: block;" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">确认修复</h5>
                    <button type="button" class="btn-close" @onclick="CancelFixConfirmation"></button>
                </div>
                <div class="modal-body">
                    <p>确定要修复选中的问题吗？此操作将重新构建容器中的链表结构。</p>
                    <p class="text-danger">注意：此操作无法撤销！</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" @onclick="CancelFixConfirmation">取消</button>
                    <button type="button" class="btn btn-primary" @onclick="ConfirmFix">
                        @if (isFixing)
                        {
                            <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                            <span>修复中...</span>
                        }
                        else
                        {
                            <span>确认修复</span>
                        }
                    </button>
                </div>
            </div>
        </div>
    </div>
    <div class="modal-backdrop fade show"></div>
}

@code {
    [CascadingParameter] IMudDialogInstance MudDialog { get; set; }
    
    [Parameter]
    public Guid FormId { get; set; }
    
    private form_form currentForm;
    private List<CardListIntegrityIssue> issues;
    private Dictionary<CardListIntegrityIssue, bool> showDetails = new();
    private bool isLoading = true;
    private bool showFixConfirmation = false;
    private bool isFixing = false;
    private CardListIntegrityIssue currentIssueToFix;
    
    protected override async Task OnPageInitializedAsync()
    {
        await LoadFormAndCheckIntegrity();
    }
    
    private async Task LoadFormAndCheckIntegrity()
    {
        isLoading = true;
        StateHasChanged();
        
        try
        {
            currentForm = await _context.form_form.FindAsync(FormId);
            if (currentForm == null)
            {
                Logger.LogWarning($"未找到ID为 {FormId} 的表单");
                return;
            }
            
            issues = await cardService.CheckCardListIntegrityAsync(_context, currentForm);
            foreach (var issue in issues)
            {
                showDetails[issue] = false;
                issue.IsFixed = false;
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "检查链表完整性时发生错误");
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }
    
    private void ToggleDetails(CardListIntegrityIssue issue)
    {
        if (showDetails.ContainsKey(issue))
        {
            showDetails[issue] = !showDetails[issue];
        }
        else
        {
            showDetails[issue] = true;
        }
    }
    
    private void FixIssue(CardListIntegrityIssue issue)
    {
        currentIssueToFix = issue;
        showFixConfirmation = true;
    }
    
    private void FixAllIssues()
    {
        var unfixedIssues = issues.FirstOrDefault(i => !i.IsFixed);
        if (unfixedIssues != null)
        {
            currentIssueToFix = null; // null表示修复所有问题
            showFixConfirmation = true;
        }
    }
    
    private void CancelFixConfirmation()
    {
        showFixConfirmation = false;
        currentIssueToFix = null;
    }
    
    private async Task ConfirmFix()
    {
        if (isFixing) return;
        
        isFixing = true;
        StateHasChanged();
        
        try
        {
            if (currentIssueToFix != null)
            {
                // 修复单个问题
                bool success = await cardService.FixCardListIntegrityAsync(_context, currentIssueToFix);
                if (success)
                {
                    currentIssueToFix.IsFixed = true;
                    Logger.LogInformation($"成功修复问题: {currentIssueToFix.Description}");
                    Snackbar.Add($"已修复问题: {currentIssueToFix.Description}", Severity.Success);
                }
                else
                {
                    Snackbar.Add("修复问题失败，请重试", Severity.Error);
                }
            }
            else
            {
                int successCount = 0;
                int failCount = 0;
                // 修复所有问题
                foreach (var issue in issues.Where(i => !i.IsFixed))
                {
                    bool success = await cardService.FixCardListIntegrityAsync(_context, issue);
                    if (success)
                    {
                        issue.IsFixed = true;
                        successCount++;
                        Logger.LogInformation($"成功修复问题: {issue.Description}");
                    }
                    else
                    {
                        failCount++;
                    }
                }
                
                if (successCount > 0)
                {
                    Snackbar.Add($"已成功修复 {successCount} 个问题", Severity.Success);
                }
                
                if (failCount > 0)
                {
                    Snackbar.Add($"有 {failCount} 个问题修复失败", Severity.Warning);
                }
            }
            
            // 重新检查
            await LoadFormAndCheckIntegrity();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "修复链表完整性问题时发生错误");
            Snackbar.Add($"修复链表完整性问题时发生错误: {ex.Message}", Severity.Error);
        }
        finally
        {
            isFixing = false;
            showFixConfirmation = false;
            StateHasChanged();
        }
    }
    
    private async Task RefreshCheck()
    {
        Snackbar.Add("正在重新检查链表完整性...", Severity.Info);
        await LoadFormAndCheckIntegrity();
        if (issues == null || !issues.Any())
        {
            Snackbar.Add("链表完整性检查完成，未发现问题", Severity.Success);
        }
        else
        {
            Snackbar.Add($"链表完整性检查完成，发现 {issues.Count} 个问题", Severity.Warning);
        }
    }
    
    private void CloseDialog()
    {
        MudDialog.Close(MudBlazor.DialogResult.Ok(true));
    }
    
    private string GetIssueTypeDisplayName(string issueType)
    {
        return issueType switch
        {
            "NoHeadNode" => "缺少头节点",
            "MultipleHeadNodes" => "多个头节点",
            "NoTailNode" => "缺少尾节点",
            "MultipleTailNodes" => "多个尾节点",
            "BrokenChain" => "链表断裂",
            _ => issueType
        };
    }
} 