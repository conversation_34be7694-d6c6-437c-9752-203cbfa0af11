﻿@inherits Cmp_Base
@inject ILogger<Cmp_QuestionConfig> inj_logger

<MudPopover Open="@ShowPop" AnchorOrigin="Origin.CenterCenter" TransformOrigin="Origin.CenterCenter" OverflowBehavior="OverflowBehavior.FlipOnOpen">
    <div class="d-flex align-items-center justify-content-between" style="background-color:#18B797; height:50px; border-radius:5px;">
        <div class="d-flex align-items-center">
            <h5 style="margin-bottom:0; color:white;">@($"【{Question.display_name}】配置项")</h5>
            <Select TValue="string" Color="BColor.Success" class="ml-3"  />
        </div>
        <MudIconButton Icon="@Icons.Material.Filled.Close" style="color:white;" OnClick="@OpenClosePopover"></MudIconButton>

    </div>

    <div class="tooltip-config">

        <div class="d-flex" style="padding-left: 5px;">
            <div class="col-auto col-form-label">必填项：</div>
            <Switch OnColor="BColor.Primary" ShowInnerText="true" OnInnerText="必" OffInnerText="非" @bind-Value="@Question.is_required" OnValueChanged="@OnParameterChanged" />
            @if (QuestionType == "文本")
            {
                <div class="col-auto col-form-label">单/多行文本：</div>
                <Switch OnColor="BColor.Primary" ShowInnerText="true" OnInnerText="多" OffInnerText="单" @bind-Value="@Question.text_is_multiple_line" OnValueChanged="@OnParameterChanged" />
            }
            @if (QuestionType == "选择")
            {
                <div class="col-auto col-form-label">单选/多选：</div>
                <Switch OnColor="BColor.Primary" ShowInnerText="true" OnInnerText="多" OffInnerText="单" @bind-Value="@Question.select_is_multiple_choice" OnValueChanged="@OnSelectTypeChanged" />
                <div class="col-auto col-form-label">是否需要"其他"选项：</div>
                <Switch OnColor="BColor.Primary" ShowInnerText="true" OnInnerText="是" OffInnerText="否" @bind-Value="@Question.select_is_has_others" OnValueChanged="@OnParameterChanged" />
            }
        </div>

        @if(QuestionType != "选择")
        {
            <MudStack Row Spacing="10" Class="mt-4">
                <div class="d-flex align-items-center">
                    <span style="width:100px;">提示词：</span>
                    <BootstrapInput @bind-Value="@Question.prompt_text" OnBlurAsync="@OnParameterChanged" />
                </div>
                <div class="d-flex align-items-center">
                    <span style="width:100px;">前缀：</span>
                    <BootstrapInput @bind-Value="@Question.prefix_text" OnBlurAsync="@OnParameterChanged" />
                </div>
                <div class="d-flex align-items-center">
                    <span style="width:100px;">后缀：</span>
                    <BootstrapInput @bind-Value="@Question.suffix_text" OnBlurAsync="@OnParameterChanged" />
                </div>
            </MudStack>
        }

        @if(QuestionType == "选择")
        {
            @if(!Question.select_is_multiple_choice)
            {        
                <RadioList TValue="string" Items="@SingleSelectItems" @bind-Value="Question.display_component"
                IsButton="true" Color="BColor.Success" ShowBorder="false" OnValueChanged="@OnParameterChanged" />
            }
            else
            {
                <RadioList TValue="string" Items="@MultipleSelectItems" @bind-Value="Question.display_component"
                IsButton="true" Color="BColor.Success" ShowBorder="false" OnValueChanged="@OnParameterChanged" />
            }

            <div class="col-auto col-form-label">选项过滤：<Button Color="BColor.Success" OnClick="@OpenSelectModal">导入</Button></div>
            <MultiSelect TValue="string" Items="@_originalOption" DisplayText="@Question.display_name" Color="BColor.Success" ShowToolbar="true" ShowSearch="true" ShowLabel="true"
            OnSelectedItemsChanged="@UpdateOptionSet" @bind-Value="@_sortedOptionSubsetValue" />

            <div class="col-auto col-form-label">默认选项：</div>            
            <MultiSelect TValue="string" Items="@_SortedOptionSubset" DisplayText="@Question.display_name" Color="BColor.Success"
            OnSelectedItemsChanged="@UpdateDefaultOptionSet" @bind-Value="@_defaultOptionSubsetValue" />

            <div class="col-auto col-form-label">否定项：</div>
            <MultiSelect TValue="string" Items="@_SortedOptionSubset" DisplayText="@Question.display_name" Color="BColor.Success"
            OnSelectedItemsChanged="@UpdateNoOthersAllowedOptionSet" @bind-Value="@_noOthersAllowedOptionValue" />
        }


        @if (QuestionType == "数值")
        {
            <MudStack Row Spacing="10" Class="mt-4">
                <div class="d-flex align-items-center">
                    <span style="width:100px;">量纲：</span>
                    <BootstrapInput @bind-Value="@Question.dimension_text" OnBlurAsync="@OnParameterChanged"  />
                </div>
                <div class="d-flex align-items-center">
                    <span style="width:100px;">数值格式：</span>
                    <BootstrapInput @bind-Value="@Question.number_decimal_places" OnBlurAsync="@OnParameterChanged"/>
                </div>
                <div class="d-flex align-items-center">
                    <span style="width:100px;">取值范围：</span>
                    <BootstrapInput @bind-Value="@Limit" OnBlurAsync="@OnLimitChanged" />
                </div>
            </MudStack>
            <div class="d-flex align-items-center mt-4">
                <span style="width:73px;">默认值：</span>
                <BootstrapInput @bind-Value="@Question.number_default_value" OnBlurAsync="@OnLimitChanged" style="width:23%;" />
            </div>
        }

        @if (QuestionType == "日期")
        {
            <div class="mt-4 align-items-center d-flex">
                <span>格式要求：</span>
                <div>
                    <MudSelect T="string" AnchorOrigin="Origin.BottomCenter" Variant="Variant.Outlined" 
                    Value="@Question.date_format" Margin="Margin.Dense" ValueChanged="@(v=> DateFormatChanged(v))">
                        @foreach (var item in dateFormatList)
                        {
                            <MudSelectItem Value="@item"></MudSelectItem>
                        }
                    </MudSelect>
                </div>

            </div>
        }

        @if (QuestionType == "文件")
        {
            <div class="mt-4 align-items-center d-flex">
                <span style="width:100px;">最大上传数量：</span>
                <div>
                    <BootstrapInput @bind-Value="@Question.file_max_file_count" OnBlurAsync="@OnParameterChanged" />
                </div>

            </div>
        }

        @if (true || QuestionType != "选择" || Question.display_component != "dropdownlist") // 调试所以有true
        {
            <div class="mt-4">
                <div class="col-auto col-form-label">
                    <span>宽度</span>
                </div>
                <div class="col-9">                   
                    @Question.text_width
                    <MudSlider T="short" Value="@(Question.text_width ?? 100)" Min="50" Max="100" Step="5" TickMarks="true" TickMarkLabels="@silderLabels"
                    ValueChanged="@OnTextWidthChanged" />
                </div>
            </div>
        }

        <div class="mt-4 align-items-center d-flex">
            <div class="col-auto col-form-label">
                <span>是否显示标注： </span>
            </div>
            <Switch OnColor="BColor.Primary" ShowInnerText="true" OnInnerText="是" OffInnerText="否" @bind-Value="@Question.is_show_label"  OnValueChanged="@OnParameterChanged" />

            <div class="col-auto col-form-label">
                <span>是否显示tip标注： </span>
            </div>
            <Switch OnColor="BColor.Primary" ShowInnerText="true" OnInnerText="是" OffInnerText="否" @bind-Value="@Question.is_tip_label" OnValueChanged="@OnParameterChanged" />

        </div>

        <div>
            <div class="col-auto col-form-label">
                <span>标注文字</span>
            </div>
            <div>
                <MudHtmlEditor Html="@Question.label_text" HtmlChanged="@OnHtmlChanged">
                    <MudHtmlToolbarOptions InsertImage="true" />
                </MudHtmlEditor>
            </div>
        </div>

    </div>
</MudPopover>

@code {
    [Parameter]
    public required CubeContext _context { get; set; }
    [Parameter]
    public required form_question Question { get; set; }
    [CascadingParameter]
    public required List<column_definition> _columnDefinitionList { get; set; }
    [Parameter]
    public EventCallback OnParentRefresh { get; set; } 
    [Parameter]
    public required string QuestionType{ get; set; }
    [Parameter]
    public required bool ShowPop{ get; set; }
    [Parameter]
    public EventCallback<bool> OnShowPopChanged { get; set; }

    // "0", "5", "10", "15", "20", "25", "30", "35", "40", "45", 
    private string[] silderLabels = new string[] { "50", "55", "60", "65", "70", "75", "80", "85", "90", "95", "100" };

    private List<SelectedItem> _originalOption {
        get {
            var _columnDefinition = _columnDefinitionList
            .Where(c => c.id == Question.column_definition_id)
            .FirstOrDefault();

            var options = new List<SelectedItem>();
            if(_columnDefinition != null){
                foreach (var item in _columnDefinition.option_set)
                {
                    options.Add(new SelectedItem{ Text = item, Value = item });
                }
            }
            return options;
        }
    }

    private List<SelectedItem> _SortedOptionSubset {
        get {
            var options = new List<SelectedItem>();
            if(Question.select_sorted_option_subset != null){
                foreach (var item in Question.select_sorted_option_subset)
                {
                    options.Add(new SelectedItem{ Text = item, Value = item });
                }
            }
            return options;
        }
    }

    private string _sortedOptionSubsetValue {
        get {
            return string.Join(",", Question.select_sorted_option_subset);
        }
        set {
            Question.select_sorted_option_subset = value.Split(',').ToList();
        }
    }

    private string _defaultOptionSubsetValue {
        get {
            return string.Join(",", Question.select_default_option==null? new List<string>():Question.select_default_option);
        }
        set {
            Question.select_default_option = value.Split(',').ToList();
        }
    }

    //否定项
    private string _noOthersAllowedOptionValue {
        get {
            return string.Join(",", Question.select_no_others_allowed==null? new List<string>():Question.select_no_others_allowed);
        }
        set {
            Question.select_no_others_allowed = value.Split(',').ToList();
        }
    }

    private async Task OpenSelectModal(){
        var dialog = await inj_dialogService.ShowAsync<Modal_Select>("导入选项", new DialogOptions());
        StateHasChanged();
        var dialogResult = await dialog.Result;
        if(!dialogResult.Canceled){
            _sortedOptionSubsetValue = dialogResult.Data.ToString();
            StateHasChanged();
            Question.select_sorted_option_subset = _sortedOptionSubsetValue.Split(',').ToList();
            await OnParentRefresh.InvokeAsync();
        }
    }

    private string Limit="[0~1000]"; //取值范围默认值

    private List<string> dateFormatList = new List<string> { "yyyy-MM", "yyyy-MM-dd", "yyyy-MM-dd HH:mm","yyyy-MM-dd HH:mm:ss", "HH:mm","HH:mm:ss"};
    private List<SelectedItem> SingleSelectItems = new List<SelectedItem> {
        new SelectedItem {Text="横排", Value="radio-row"},
        new SelectedItem {Text="竖排", Value="radio-column"},
        new SelectedItem {Text="按钮", Value="button-row"},
        new SelectedItem {Text="下拉列表", Value="dropdownlist"}
    };
    private List<SelectedItem> MultipleSelectItems = new List<SelectedItem> {
        new SelectedItem {Text="横排", Value="checkbox-row"},
        new SelectedItem {Text="竖排", Value="checkbox-column"},
        new SelectedItem {Text="按钮", Value="button-row"},
        new SelectedItem {Text="下拉列表", Value="dropdownlist"}
    };

    private CancellationTokenSource? _debounceTokenSource;
    private CancellationTokenSource? _htmlDebounceTokenSource;

    // 暂时没用，先放着
    protected override async Task OnPageInitializedAsync()
    {
        await base.OnPageInitializedAsync();
    }

    private async Task OnParameterChanged<T>(T _)
    {
        await OnParentRefresh.InvokeAsync();
    }

    //数值范围
    private async Task OnLimitChanged<T>(T _)
    {
        try
        {
            inj_logger.LogWarning($"[OnLimitChanged] 开始更新数值范围 - QuestionId: {Question.id}");
            ParseRange(Limit);
            await OnParentRefresh.InvokeAsync();
            inj_logger.LogWarning($"[OnLimitChanged] 数值范围更新完成");
        }
        catch (Exception ex)
        {
            inj_logger.LogError(ex, $"[OnLimitChanged] 更新数值范围时发生错误 - QuestionId: {Question.id}");
            throw;
        }
    }

    //存储数值范围
    public void ParseRange(string range)
    {
        try
        {
            var match = Regex.Match(range, @"([\[\(])(\d+\.?\d*)~(\d+\.?\d*)([\]\)])");
            if (match.Success)
            {
                Question.number_min = decimal.Parse(match.Groups[2].Value);
                Question.number_max = decimal.Parse(match.Groups[3].Value);
                Question.number_min_inclusive = match.Groups[1].Value == "[";
                Question.number_max_inclusive = match.Groups[4].Value == "]";
            }
            else
            {
                throw new ArgumentException("无效的范围格式");
            }
        }
        catch (Exception ex)
        {
            inj_logger.LogError(ex, "解析数值范围时发生错误");
            throw;
        }
    }

    private async Task OnHtmlChanged(string html){
        try
        {
            Question.label_text = html;
            await OnParentRefresh.InvokeAsync();

            // Cancel previous timer if exists
            _debounceTokenSource?.Cancel();
            _debounceTokenSource = new CancellationTokenSource();

            try
            {
                await Task.Delay(3000, _debounceTokenSource.Token);
                await OnParentRefresh.InvokeAsync();
            }
            catch (OperationCanceledException)
            {
                inj_logger.LogWarning($"[OnHtmlChanged] 更新操作被取消");
            }
        }
        catch (Exception ex)
        {
            inj_logger.LogError(ex, $"[OnHtmlChanged] 更新富文本时发生错误 - QuestionId: {Question.id}");
            throw;
        }
    }

    //日期格式
    private async Task DateFormatChanged(string value)
    {
        try
        {
            inj_logger.LogWarning($"[DateFormatChanged] 开始更新日期格式 - QuestionId: {Question.id}, Format: {value}");
            Question.date_format = value;
            await OnParentRefresh.InvokeAsync();
            inj_logger.LogWarning($"[DateFormatChanged] 日期格式更新完成");
        }
        catch (Exception ex)
        {
            inj_logger.LogError(ex, $"[DateFormatChanged] 更新日期格式时发生错误 - QuestionId: {Question.id}");
            throw;
        }
    }

    //标注是否显示
    // private async Task LabelTipChanged(bool value){
    //     Question.IsTipLabel = value;
    //     await _context.SaveChangesAsync();
    //     await OnParentRefresh.InvokeAsync();
    // }

    //单选/多选/组件样式/是否需要"其它"选项
    private async Task OnSelectTypeChanged(bool multipleChoice)
    {
        try
        {
            inj_logger.LogWarning($"[OnSelectTypeChanged] 开始更新选择类型 - QuestionId: {Question.id}, MultipleChoice: {multipleChoice}");
            if(multipleChoice)
            {
                if(Question.display_component==null)
                {
                    Question.display_component = "checkbox-row";
                }
                else if(Question.display_component=="radio-row")
                {
                    Question.display_component = "checkbox-row";
                }
                else if(Question.display_component=="radio-column")
                {
                    Question.display_component = "checkbox-column";
                }
            }
            else
            {
                if(Question.display_component==null)
                {
                    Question.display_component = "radio-row";
                }
                else if(Question.display_component=="checkbox-row")
                {
                    Question.display_component = "radio-row";
                }
                else if(Question.display_component=="checkbox-column")
                {
                    Question.display_component = "radio-column";
                }
            }
            await OnParentRefresh.InvokeAsync();
            inj_logger.LogWarning($"[OnSelectTypeChanged] 选择类型更新完成 - NewComponent: {Question.display_component}");
        }
        catch (Exception ex)
        {
            inj_logger.LogError(ex, $"[OnSelectTypeChanged] 更新选择类型时发生错误 - QuestionId: {Question.id}");
            throw;
        }
    }

    private async Task UpdateOptionSet(IEnumerable<SelectedItem> items)
    {
        try
        {
            inj_logger.LogWarning($"[UpdateOptionSet] 开始更新选项集 - QuestionId: {Question.id}");
            Question.select_sorted_option_subset = items.Select(item => item.Value).ToList();
            await OnParentRefresh.InvokeAsync();
            inj_logger.LogWarning($"[UpdateOptionSet] 选项集更新完成 - OptionCount: {items.Count()}");
        }
        catch (Exception ex)
        {
            inj_logger.LogError(ex, $"[UpdateOptionSet] 更新选项集时发生错误 - QuestionId: {Question.id}");
            throw;
        }
    }

    private async Task UpdateDefaultOptionSet(IEnumerable<SelectedItem> items)
    {
        try
        {
            inj_logger.LogWarning($"[UpdateDefaultOptionSet] 开始更新默认选项 - QuestionId: {Question.id}");
            Question.select_default_option = items.Select(item => item.Value).ToList();
            await OnParentRefresh.InvokeAsync();
            inj_logger.LogWarning($"[UpdateDefaultOptionSet] 默认选项更新完成 - OptionCount: {items.Count()}");
        }
        catch (Exception ex)
        {
            inj_logger.LogError(ex, $"[UpdateDefaultOptionSet] 更新默认选项时发生错误 - QuestionId: {Question.id}");
            throw;
        }
    }

    private async Task UpdateNoOthersAllowedOptionSet(IEnumerable<SelectedItem> items)
    {
        try
        {
            inj_logger.LogWarning($"[UpdateNoOthersAllowedOptionSet] 开始更新否定项 - QuestionId: {Question.id}");  
            Question.select_no_others_allowed = items.Select(item => item.Value).ToList();
            await OnParentRefresh.InvokeAsync();
            inj_logger.LogWarning($"[UpdateNoOthersAllowedOptionSet] 否定项更新完成 - OptionCount: {items.Count()}");
        }
        catch (Exception ex)
        {
            inj_logger.LogError(ex, $"[UpdateNoOthersAllowedOptionSet] 更新否定项时发生错误 - QuestionId: {Question.id}");
            throw;
        }
    }


    private async Task OpenClosePopover()
    {
        ShowPop = !ShowPop;
        await OnShowPopChanged.InvokeAsync(ShowPop);
    }

    protected override async Task OnParametersSetAsync()
    {
        try
        {
            inj_logger.LogInformation($"[OnParametersSetAsync] 开始初始化参数 - QuestionId: {Question.id}, QuestionType: {QuestionType}");
            bool isInitialized = false;

            if (QuestionType == "数值")
            {
                if (Question.number_min != null && Question.number_max != null && Question.number_min_inclusive != null && Question.number_max_inclusive != null)
                {
                    var first = Question.number_min_inclusive.Value ? "[" : "(";
                    var end = Question.number_max_inclusive.Value ? "]" : ")";
                    Limit = $"{first}{Question.number_min}~{Question.number_max}{end}";
                }
                else if (Question.number_min == null && Question.number_max == null && Question.number_min_inclusive == null && Question.number_max_inclusive == null)
                {
                    Question.number_min_inclusive = true;
                    Question.number_max_inclusive = true;
                    Question.number_min = 0;
                    Question.number_max = 1000;
                    isInitialized = true;
                }
                if (Question.number_decimal_places == null)
                {
                    Question.number_decimal_places = 2;
                    isInitialized = true;
                }
                if (string.IsNullOrWhiteSpace(Question.prompt_text))
                {
                    Question.prompt_text = "请输入数值";
                    isInitialized = true;
                }
                if (Question.text_width == null)
                {
                    Question.text_width = 50;
                    isInitialized = true;
                }
            }
            else if (QuestionType == "文件")
            {
                Question.file_max_file_count = Question.file_max_file_count == null ? 15 : Question.file_max_file_count;
                isInitialized = Question.file_max_file_count == null;
            }
            else if (QuestionType == "文本")
            {
                if (string.IsNullOrWhiteSpace(Question.prompt_text))
                {
                    Question.prompt_text = "请输入文本";
                    isInitialized = true;
                }
                if (Question.text_width == null)
                {
                    Question.text_width = 100;
                    isInitialized = true;
                }
            }

            

            if (isInitialized)
            {
                await OnParentRefresh.InvokeAsync();
                inj_logger.LogInformation($"[OnParametersSetAsync] 参数初始化完成");
            }
            else
            {
                inj_logger.LogInformation($"[OnParametersSetAsync] 无需初始化参数");
            }
        }
        catch (Exception ex)
        {
            inj_logger.LogError(ex, $"[OnParametersSetAsync] 初始化参数时发生错误 - QuestionId: {Question.id}");
            throw;
        }
    }

    private async Task OnTextWidthChanged(short value)
    {
        try
        {
            inj_logger.LogWarning($"[OnTextWidthChanged] 开始更新文本宽度 - QuestionId: {Question.id}, Width: {value}");
            Question.text_width = value;
            await OnParentRefresh.InvokeAsync();
            
            // Cancel previous timer if exists
            _debounceTokenSource?.Cancel();
            _debounceTokenSource = new CancellationTokenSource();
            
            try 
            {
                await Task.Delay(3000, _debounceTokenSource.Token);
                await OnParentRefresh.InvokeAsync();
            }
            catch (OperationCanceledException)
            {
                inj_logger.LogWarning($"[OnTextWidthChanged] 更新操作被取消");
            }
        }
        catch (Exception ex)
        {
            inj_logger.LogError(ex, $"[OnTextWidthChanged] 更新文本宽度时发生错误 - QuestionId: {Question.id}");
            throw;
        }
    }

    public void Dispose()
    {
        _debounceTokenSource?.Cancel();
        _debounceTokenSource?.Dispose();
    }
}


<style scoped>
    .tooltip-config {
        padding: 10px 15px;
        color: #333;
        text-align: left;
        font-size: 14px;
        max-height: 450px;
        overflow-y: auto;
    }

    .switch {
        padding: 3px 0px;
    }
</style>
