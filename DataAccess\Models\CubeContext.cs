﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore;

namespace DataAccess.Models;

public partial class CubeContext : DbContext
{
    public CubeContext(DbContextOptions<CubeContext> options)
        : base(options)
    {
    }

    public virtual DbSet<column_definition> column_definition { get; set; }

    public virtual DbSet<form_card> form_card { get; set; }

    public virtual DbSet<form_form> form_form { get; set; }

    public virtual DbSet<form_form_set> form_form_set { get; set; }

    public virtual DbSet<form_linker_rule> form_linker_rule { get; set; }

    public virtual DbSet<form_project> form_project { get; set; }

    public virtual DbSet<form_question> form_question { get; set; }

    public virtual DbSet<patient> patient { get; set; }

    public virtual DbSet<sys_department> sys_department { get; set; }

    public virtual DbSet<sys_hospital> sys_hospital { get; set; }

    public virtual DbSet<sys_license> sys_license { get; set; }

    public virtual DbSet<sys_permission> sys_permission { get; set; }

    public virtual DbSet<sys_permission_data> sys_permission_data { get; set; }

    public virtual DbSet<sys_region> sys_region { get; set; }

    public virtual DbSet<sys_role> sys_role { get; set; }

    public virtual DbSet<sys_user> sys_user { get; set; }

    public virtual DbSet<sys_ward> sys_ward { get; set; }

    public virtual DbSet<table_definition> table_definition { get; set; }

    public virtual DbSet<unique_patient> unique_patient { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.HasPostgresExtension("uuid-ossp");

        modelBuilder.Entity<column_definition>(entity =>
        {
            entity.HasKey(e => e.id).HasName("ColunmDefinition_pkey");

            entity.Property(e => e.id)
                .HasDefaultValueSql("uuid_generate_v1()")
                .HasComment("ID");
            entity.Property(e => e.data_type)
                .IsRequired()
                .HasComment("数据类型");
            entity.Property(e => e.display_name)
                .IsRequired()
                .HasComment("字段显示名称");
            entity.Property(e => e.history_ver_array).HasComment("历史版本号数组");
            entity.Property(e => e.is_changed)
                .HasDefaultValue(true)
                .HasComment("版本发布后是否发生改变");
            entity.Property(e => e.is_valid)
                .HasDefaultValue(true)
                .HasComment("是否有效");
            entity.Property(e => e.note_cn).HasComment("中文释义");
            entity.Property(e => e.note_en).HasComment("英文释义");
            entity.Property(e => e.option_set).HasComment("选项List");
            entity.Property(e => e.sorted_index)
                .HasDefaultValue(0)
                .HasComment("排序索引");
            entity.Property(e => e.table_id).HasComment("外键-表ID");
            entity.Property(e => e.unique_name)
                .IsRequired()
                .HasComment("字段名称");
            entity.Property(e => e.update_time)
                .HasDefaultValueSql("now()")
                .HasColumnType("timestamp without time zone");

            entity.HasOne(d => d.table).WithMany(p => p.column_definition)
                .HasForeignKey(d => d.table_id)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_TableId_TableDefinition_Id");
        });

        modelBuilder.Entity<form_card>(entity =>
        {
            entity.HasKey(e => e.id).HasName("Form_Card_pkey");

            entity.ToTable("form_card", "form");

            entity.Property(e => e.id)
                .ValueGeneratedNever()
                .HasComment("题卡ID");
            entity.Property(e => e.created_at)
                .HasDefaultValueSql("now()")
                .HasComment("创建时间")
                .HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.department_id).HasComment("所属科室ID");
            entity.Property(e => e.department_name)
                .IsRequired()
                .HasComment("所属科室名称");
            entity.Property(e => e.description).HasComment("题卡描述");
            entity.Property(e => e.form_id).HasComment("表单ID");
            entity.Property(e => e.form_name)
                .IsRequired()
                .HasComment("表单名称");
            entity.Property(e => e.form_set_id).HasComment("表单集ID");
            entity.Property(e => e.form_set_name)
                .IsRequired()
                .HasComment("表单集名称");
            entity.Property(e => e.hospital_id).HasComment("所属医院ID");
            entity.Property(e => e.hospital_name)
                .IsRequired()
                .HasComment("所属医院名称");
            entity.Property(e => e.is_hidden)
                .HasDefaultValue(false)
                .HasComment("是否默认隐藏");
            entity.Property(e => e.name)
                .IsRequired()
                .HasComment("题卡名称");
            entity.Property(e => e.parent_id).HasComment("父级题卡ID");
            entity.Property(e => e.project_id).HasComment("项目ID");
            entity.Property(e => e.project_name)
                .IsRequired()
                .HasComment("项目名称");
            entity.Property(e => e.type)
                .IsRequired()
                .HasComment("题卡类型");
            entity.Property(e => e.updated_at)
                .HasDefaultValueSql("now()")
                .HasComment("更新时间")
                .HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.ward_id).HasComment("所属病区ID");
            entity.Property(e => e.ward_name)
                .IsRequired()
                .HasComment("所属病区名称");

            entity.HasOne(d => d.department).WithMany(p => p.form_card)
                .HasForeignKey(d => d.department_id)
                .HasConstraintName("FK_Form_Card_Sys_Department");

            entity.HasOne(d => d.form).WithMany(p => p.form_card)
                .HasForeignKey(d => d.form_id)
                .HasConstraintName("FK_Form_Card_Form");

            entity.HasOne(d => d.form_set).WithMany(p => p.form_card)
                .HasForeignKey(d => d.form_set_id)
                .HasConstraintName("FK_Form_Card_FormSet");

            entity.HasOne(d => d.hospital).WithMany(p => p.form_card)
                .HasForeignKey(d => d.hospital_id)
                .HasConstraintName("FK_Form_Card_Sys_Hospital");

            entity.HasOne(d => d.parent).WithMany(p => p.Inverseparent)
                .HasForeignKey(d => d.parent_id)
                .OnDelete(DeleteBehavior.Cascade)
                .HasConstraintName("FK_Form_Card_Parent");

            entity.HasOne(d => d.project).WithMany(p => p.form_card)
                .HasForeignKey(d => d.project_id)
                .HasConstraintName("FK_Form_Card_Project");

            entity.HasOne(d => d.ward).WithMany(p => p.form_card)
                .HasForeignKey(d => d.ward_id)
                .HasConstraintName("FK_Form_Form_Sys_Ward");
        });

        modelBuilder.Entity<form_form>(entity =>
        {
            entity.HasKey(e => e.id).HasName("Form_Form_pkey");

            entity.ToTable("form_form", "form");

            entity.Property(e => e.id)
                .ValueGeneratedNever()
                .HasComment("表单ID");
            entity.Property(e => e.created_at)
                .HasDefaultValueSql("now()")
                .HasComment("创建时间")
                .HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.department_id).HasComment("所属科室ID");
            entity.Property(e => e.department_name)
                .IsRequired()
                .HasComment("所属科室名称");
            entity.Property(e => e.form_set_id).HasComment("表单集ID");
            entity.Property(e => e.form_set_name)
                .IsRequired()
                .HasComment("表单集名称");
            entity.Property(e => e.group_name)
                .IsRequired()
                .HasComment("表单分组名称");
            entity.Property(e => e.hospital_id).HasComment("所属医院ID");
            entity.Property(e => e.hospital_name)
                .IsRequired()
                .HasComment("所属医院名称");
            entity.Property(e => e.name)
                .IsRequired()
                .HasComment("表单名称");
            entity.Property(e => e.project_id).HasComment("项目ID");
            entity.Property(e => e.project_name)
                .IsRequired()
                .HasComment("项目名称");
            entity.Property(e => e.sort_index)
                .HasDefaultValue((short)0)
                .HasComment("排序索引");
            entity.Property(e => e.updated_at)
                .HasDefaultValueSql("now()")
                .HasComment("更新时间")
                .HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.ward_id).HasComment("所属病区ID");
            entity.Property(e => e.ward_name)
                .IsRequired()
                .HasComment("所属病区名称");

            entity.HasOne(d => d.department).WithMany(p => p.form_form)
                .HasForeignKey(d => d.department_id)
                .HasConstraintName("FK_Form_Form_Sys_Department");

            entity.HasOne(d => d.form_set).WithMany(p => p.form_form)
                .HasForeignKey(d => d.form_set_id)
                .HasConstraintName("FK_Form_Form_FormSet");

            entity.HasOne(d => d.hospital).WithMany(p => p.form_form)
                .HasForeignKey(d => d.hospital_id)
                .HasConstraintName("FK_Form_Form_Sys_Hospital");

            entity.HasOne(d => d.project).WithMany(p => p.form_form)
                .HasForeignKey(d => d.project_id)
                .HasConstraintName("FK_Form_Form_Project");

            entity.HasOne(d => d.ward).WithMany(p => p.form_form)
                .HasForeignKey(d => d.ward_id)
                .HasConstraintName("FK_Form_Form_Sys_Ward");
        });

        modelBuilder.Entity<form_form_set>(entity =>
        {
            entity.HasKey(e => e.id).HasName("Form_FormSet_pkey");

            entity.ToTable("form_form_set", "form", tb => tb.HasComment("表单集"));

            entity.Property(e => e.id)
                .ValueGeneratedNever()
                .HasComment("表单集ID");
            entity.Property(e => e.created_at)
                .HasDefaultValueSql("now()")
                .HasComment("创建时间")
                .HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.department_id).HasComment("所属科室ID");
            entity.Property(e => e.department_name)
                .IsRequired()
                .HasComment("所属科室名称");
            entity.Property(e => e.description).HasComment("表单集描述");
            entity.Property(e => e.hospital_id).HasComment("所属医院ID");
            entity.Property(e => e.hospital_name)
                .IsRequired()
                .HasComment("所属医院名称");
            entity.Property(e => e.name)
                .IsRequired()
                .HasComment("表单集名称");
            entity.Property(e => e.project_id).HasComment("项目ID");
            entity.Property(e => e.project_name)
                .IsRequired()
                .HasComment("项目名称");
            entity.Property(e => e.type).HasComment("表单集类型");
            entity.Property(e => e.updated_at)
                .HasDefaultValueSql("now()")
                .HasComment("更新时间")
                .HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.ward_id).HasComment("所属病区ID");
            entity.Property(e => e.ward_name)
                .IsRequired()
                .HasComment("所属病区名称");

            entity.HasOne(d => d.department).WithMany(p => p.form_form_set)
                .HasForeignKey(d => d.department_id)
                .HasConstraintName("FK_FormSet_Sys_Department");

            entity.HasOne(d => d.hospital).WithMany(p => p.form_form_set)
                .HasForeignKey(d => d.hospital_id)
                .HasConstraintName("FK_FormSet_Sys_Hospital");

            entity.HasOne(d => d.project).WithMany(p => p.form_form_set)
                .HasForeignKey(d => d.project_id)
                .HasConstraintName("FK_FormSet_Form_Project");

            entity.HasOne(d => d.ward).WithMany(p => p.form_form_set)
                .HasForeignKey(d => d.ward_id)
                .HasConstraintName("FK_FormSet_Sys_Ward");
        });

        modelBuilder.Entity<form_linker_rule>(entity =>
        {
            entity.HasKey(e => e.id).HasName("form_linker_rule_pkey");

            entity.ToTable("form_linker_rule", "form", tb => tb.HasComment("表单问题联动规则表"));

            entity.Property(e => e.id)
                .HasDefaultValueSql("uuid_generate_v1()")
                .HasComment("ID");
            entity.Property(e => e.action_function)
                .IsRequired()
                .HasComment("执行函数名称");
            entity.Property(e => e.created_at)
                .HasDefaultValueSql("CURRENT_TIMESTAMP")
                .HasComment("创建时间")
                .HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.dependent_field_ids).HasComment("依赖字段ID");
            entity.Property(e => e.formset_id).HasComment("表单集ID");
            entity.Property(e => e.is_composite)
                .HasDefaultValue(false)
                .HasComment("是否是复合类型");
            entity.Property(e => e.parameters).HasComment("参数");
            entity.Property(e => e.priority)
                .HasDefaultValue(0)
                .HasComment("优先级");
            entity.Property(e => e.publish_obj_calss)
                .IsRequired()
                .HasDefaultValueSql("'form_question'::text")
                .HasComment("发布者对象类型");
            entity.Property(e => e.publish_obj_id)
                .IsRequired()
                .HasComment("发布者对象ID");
            entity.Property(e => e.rule_name).IsRequired();
            entity.Property(e => e.rule_type)
                .IsRequired()
                .HasDefaultValueSql("'default'::text")
                .HasComment("规则类型");
            entity.Property(e => e.subscribe_obj_calss)
                .IsRequired()
                .HasDefaultValueSql("'form_question'::text")
                .HasComment("订阅者对象类型");
            entity.Property(e => e.subscribe_obj_id)
                .IsRequired()
                .HasComment("订阅者对象ID");
            entity.Property(e => e.trigger_condition).HasComment("触发条件");
            entity.Property(e => e.updated_at)
                .HasDefaultValueSql("CURRENT_TIMESTAMP")
                .HasComment("更新时间")
                .HasColumnType("timestamp(6) without time zone");

            entity.HasOne(d => d.formset).WithMany(p => p.form_linker_rule)
                .HasForeignKey(d => d.formset_id)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("form_linker_rule_formset_id_fkey");
        });

        modelBuilder.Entity<form_project>(entity =>
        {
            entity.HasKey(e => e.id).HasName("Project_pkey");

            entity.ToTable("form_project", "form", tb => tb.HasComment("项目"));

            entity.Property(e => e.id)
                .ValueGeneratedNever()
                .HasComment("项目ID");
            entity.Property(e => e.created_at)
                .HasDefaultValueSql("now()")
                .HasComment("创建时间")
                .HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.department_id).HasComment("所属科室ID");
            entity.Property(e => e.department_name)
                .IsRequired()
                .HasComment("所属科室名称");
            entity.Property(e => e.display_name).HasComment("项目描述");
            entity.Property(e => e.hospital_id).HasComment("所属医院ID");
            entity.Property(e => e.hospital_name)
                .IsRequired()
                .HasComment("所属医院名称");
            entity.Property(e => e.name)
                .IsRequired()
                .HasComment("项目名称");
            entity.Property(e => e.scan_code_msg).HasComment("扫码消息");
            entity.Property(e => e.updated_at)
                .HasDefaultValueSql("now()")
                .HasComment("更新时间")
                .HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.ward_id).HasComment("所属病区ID");
            entity.Property(e => e.ward_name)
                .IsRequired()
                .HasComment("所属病区名称");

            entity.HasOne(d => d.department).WithMany(p => p.form_project)
                .HasForeignKey(d => d.department_id)
                .HasConstraintName("FK_Project_Sys_Department");

            entity.HasOne(d => d.hospital).WithMany(p => p.form_project)
                .HasForeignKey(d => d.hospital_id)
                .HasConstraintName("FK_Project_Sys_Hospital");

            entity.HasOne(d => d.ward).WithMany(p => p.form_project)
                .HasForeignKey(d => d.ward_id)
                .HasConstraintName("FK_Project_Sys_Ward");
        });

        modelBuilder.Entity<form_question>(entity =>
        {
            entity.HasKey(e => e.id).HasName("form_question_pkey");

            entity.ToTable("form_question", "form");

            entity.Property(e => e.id)
                .ValueGeneratedNever()
                .HasComment("题目ID");
            entity.Property(e => e.card_id).HasComment("题卡ID");
            entity.Property(e => e.column_definition_id).HasComment("列定义ID");
            entity.Property(e => e.column_name).HasComment("列名");
            entity.Property(e => e.created_at)
                .HasDefaultValueSql("now()")
                .HasComment("创建时间")
                .HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.data_type).HasComment("数据类型");
            entity.Property(e => e.date_format).HasComment("日期格式");
            entity.Property(e => e.date_max)
                .HasComment("最大日期")
                .HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.date_min)
                .HasComment("最小日期")
                .HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.date_reserved4).HasComment("日期保留字段4");
            entity.Property(e => e.date_reserved5).HasComment("日期保留字段5");
            entity.Property(e => e.date_reserved6).HasComment("日期保留字段6");
            entity.Property(e => e.date_reserved7).HasComment("日期保留字段7");
            entity.Property(e => e.date_reserved8).HasComment("日期保留字段8");
            entity.Property(e => e.date_reserved9).HasComment("日期保留字段9");
            entity.Property(e => e.department_id).HasComment("所属科室ID");
            entity.Property(e => e.department_name)
                .IsRequired()
                .HasComment("所属科室名称");
            entity.Property(e => e.dimension_id).HasComment("量纲ID");
            entity.Property(e => e.dimension_text).HasComment("量纲");
            entity.Property(e => e.display_component).HasComment("显示组件");
            entity.Property(e => e.display_name).HasComment("显示名称");
            entity.Property(e => e.display_style).HasComment("显示样式");
            entity.Property(e => e.file_max_file_count).HasComment("最大上传文件数量");
            entity.Property(e => e.form_id).HasComment("表单ID");
            entity.Property(e => e.form_name)
                .IsRequired()
                .HasComment("表单名称");
            entity.Property(e => e.form_set_id).HasComment("表单集ID");
            entity.Property(e => e.form_set_name)
                .IsRequired()
                .HasComment("表单集名称");
            entity.Property(e => e.has_default_value)
                .HasDefaultValue(false)
                .HasComment("是否有默认值");
            entity.Property(e => e.hospital_id).HasComment("所属医院ID");
            entity.Property(e => e.hospital_name)
                .IsRequired()
                .HasComment("所属医院名称");
            entity.Property(e => e.is_disabled)
                .HasDefaultValue(false)
                .HasComment("是否禁用");
            entity.Property(e => e.is_hidden)
                .HasDefaultValue(false)
                .HasComment("是否隐藏");
            entity.Property(e => e.is_required)
                .HasDefaultValue(false)
                .HasComment("是否必填");
            entity.Property(e => e.is_show_label)
                .HasDefaultValue(false)
                .HasComment("是否显示标注文字");
            entity.Property(e => e.is_tip_label)
                .HasDefaultValue(false)
                .HasComment("是否用Tip显示标注文字");
            entity.Property(e => e.label_text).HasComment("标注文字");
            entity.Property(e => e.number_decimal_places).HasComment("小数位数");
            entity.Property(e => e.number_default_value).HasComment("默认值");
            entity.Property(e => e.number_format).HasComment("格式");
            entity.Property(e => e.number_is_integer)
                .HasDefaultValue(false)
                .HasComment("是否整数");
            entity.Property(e => e.number_max).HasComment("最大值");
            entity.Property(e => e.number_max_inclusive).HasComment("最大值是否包含");
            entity.Property(e => e.number_min).HasComment("最小值");
            entity.Property(e => e.number_min_inclusive).HasComment("最小值是否包含");
            entity.Property(e => e.number_reserved9).HasComment("数字保留字段9");
            entity.Property(e => e.placeholder_width_ratio)
                .HasDefaultValue((short)1)
                .HasComment("输入框宽度比例");
            entity.Property(e => e.prefix_text).HasComment("前缀");
            entity.Property(e => e.project_id).HasComment("项目ID");
            entity.Property(e => e.project_name)
                .IsRequired()
                .HasComment("项目名称");
            entity.Property(e => e.prompt_text).HasComment("提示文字");
            entity.Property(e => e.reserved5).HasComment("保留字段5");
            entity.Property(e => e.reserved6).HasComment("保留字段6");
            entity.Property(e => e.reserved7).HasComment("保留字段7");
            entity.Property(e => e.reserved8).HasComment("保留字段8");
            entity.Property(e => e.reserved9).HasComment("保留字段9");
            entity.Property(e => e.select_default_option).HasComment("默认选中项");
            entity.Property(e => e.select_is_has_others)
                .HasDefaultValue(false)
                .HasComment("是否需要“其它”选项");
            entity.Property(e => e.select_is_multiple_choice)
                .HasDefaultValue(false)
                .HasComment("是否多选");
            entity.Property(e => e.select_no_others_allowed).HasComment("选了我，其他进不来");
            entity.Property(e => e.select_reserved6).HasComment("选择题保留字段6");
            entity.Property(e => e.select_reserved7).HasComment("选择题保留字段7");
            entity.Property(e => e.select_reserved8).HasComment("选择题保留字段8");
            entity.Property(e => e.select_reserved9).HasComment("选择题保留字段9");
            entity.Property(e => e.select_sorted_option_subset).HasComment("排序选项子集");
            entity.Property(e => e.sort_index)
                .HasDefaultValue(0)
                .HasComment("排序索引");
            entity.Property(e => e.suffix_text).HasComment("后缀");
            entity.Property(e => e.table_definition_id).HasComment("表定义ID");
            entity.Property(e => e.table_name).HasComment("表名");
            entity.Property(e => e.text_is_multiple_line)
                .HasDefaultValue(false)
                .HasComment("是否多行");
            entity.Property(e => e.text_max_length).HasComment("文本最大长度");
            entity.Property(e => e.text_max_line).HasComment("最大行数");
            entity.Property(e => e.text_reserved5).HasComment("文本保留字段5");
            entity.Property(e => e.text_reserved6).HasComment("文本保留字段6");
            entity.Property(e => e.text_reserved7).HasComment("文本保留字段7");
            entity.Property(e => e.text_reserved8).HasComment("文本保留字段8");
            entity.Property(e => e.text_reserved9).HasComment("文本保留字段9");
            entity.Property(e => e.text_width).HasComment("宽度");
            entity.Property(e => e.updated_at)
                .HasDefaultValueSql("now()")
                .HasComment("更新时间")
                .HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.vcard_flag).HasComment("虚拟卡片标志");
            entity.Property(e => e.vcard_name).HasComment("虚拟卡片名称");
            entity.Property(e => e.ward_id).HasComment("所属病区ID");
            entity.Property(e => e.ward_name)
                .IsRequired()
                .HasComment("所属病区名称");

            entity.HasOne(d => d.card).WithMany(p => p.form_question)
                .HasForeignKey(d => d.card_id)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_Form_Question_Card");

            entity.HasOne(d => d.column_definition).WithMany(p => p.form_question)
                .HasForeignKey(d => d.column_definition_id)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_Form_Question_ColumnDefinition");

            entity.HasOne(d => d.department).WithMany(p => p.form_question)
                .HasForeignKey(d => d.department_id)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_Form_Question_Department");

            entity.HasOne(d => d.form).WithMany(p => p.form_question)
                .HasForeignKey(d => d.form_id)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_Form_Question_Form");

            entity.HasOne(d => d.form_set).WithMany(p => p.form_question)
                .HasForeignKey(d => d.form_set_id)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_Form_Question_FormSet");

            entity.HasOne(d => d.hospital).WithMany(p => p.form_question)
                .HasForeignKey(d => d.hospital_id)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_Form_Question_Hospital");

            entity.HasOne(d => d.project).WithMany(p => p.form_question)
                .HasForeignKey(d => d.project_id)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_Form_Question_Project");

            entity.HasOne(d => d.table_definition).WithMany(p => p.form_question)
                .HasForeignKey(d => d.table_definition_id)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_Form_Question_TableDefinition");

            entity.HasOne(d => d.ward).WithMany(p => p.form_question)
                .HasForeignKey(d => d.ward_id)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_Form_Question_Ward");
        });

        modelBuilder.Entity<patient>(entity =>
        {
            entity.HasKey(e => e.id).HasName("patient_pkey");

            entity.ToTable(tb => tb.HasComment("患者表"));

            entity.Property(e => e.id)
                .ValueGeneratedNever()
                .HasComment("主键");
            entity.Property(e => e.address_content).HasComment("地址内容");
            entity.Property(e => e.address_type).HasComment("地址类型");
            entity.Property(e => e.age).HasComment("年龄");
            entity.Property(e => e.birthday).HasComment("出生日期");
            entity.Property(e => e.contact_name).HasComment("联系人");
            entity.Property(e => e.contact_phone).HasComment("联系人电话");
            entity.Property(e => e.contact_relation).HasComment("联系人关系");
            entity.Property(e => e.create_time)
                .HasDefaultValueSql("CURRENT_TIMESTAMP")
                .HasComment("创建时间")
                .HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.death_cause).HasComment("死亡原因");
            entity.Property(e => e.death_time)
                .HasComment("死亡时间")
                .HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.education).HasComment("教育程度");
            entity.Property(e => e.email).HasComment("邮箱");
            entity.Property(e => e.gender).HasComment("性别");
            entity.Property(e => e.hospital_id).HasComment("医院id");
            entity.Property(e => e.inpatient_number).HasComment("住院号");
            entity.Property(e => e.is_valid)
                .HasDefaultValue(true)
                .HasComment("是否有效");
            entity.Property(e => e.marital_status).HasComment("婚姻状况");
            entity.Property(e => e.medical_record_number).HasComment("病案号");
            entity.Property(e => e.name).HasComment("姓名");
            entity.Property(e => e.nation).HasComment("民族");
            entity.Property(e => e.native_place).HasComment("籍贯");
            entity.Property(e => e.occupation).HasComment("职业");
            entity.Property(e => e.phone_number).HasComment("联系电话号码");
            entity.Property(e => e.phone_type).HasComment("联系电话类型");
            entity.Property(e => e.project_id).HasComment("ProjectId");
            entity.Property(e => e.sid_number).HasComment("证件号码");
            entity.Property(e => e.sid_type).HasComment("证件类型");
            entity.Property(e => e.source_type)
                .IsRequired()
                .HasDefaultValueSql("'care'::text")
                .HasComment("患者来源(Care/followup)");
            entity.Property(e => e.unique_id).HasComment("唯一患者id");
            entity.Property(e => e.weixin_id).HasComment("微信号");
            entity.Property(e => e.weixin_phone_number).HasComment("微信号绑定电话");

            entity.HasOne(d => d.hospital).WithMany(p => p.patient)
                .HasForeignKey(d => d.hospital_id)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("patient_hospital_id_fkey");

            entity.HasOne(d => d.project).WithMany(p => p.patient)
                .HasForeignKey(d => d.project_id)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("patient_project_id_fkey");
        });

        modelBuilder.Entity<sys_department>(entity =>
        {
            entity.HasKey(e => e.id).HasName("Department_pkey");

            entity.ToTable("sys_department", "system");

            entity.Property(e => e.id)
                .ValueGeneratedNever()
                .HasComment("科室ID");
            entity.Property(e => e.display_name).HasComment("科室显示名称");
            entity.Property(e => e.hospital_id).HasComment("医院ID");
            entity.Property(e => e.name)
                .IsRequired()
                .HasComment("科室名称");

            entity.HasOne(d => d.hospital).WithMany(p => p.sys_department)
                .HasForeignKey(d => d.hospital_id)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_HospitalId_SysHospital_Id");
        });

        modelBuilder.Entity<sys_hospital>(entity =>
        {
            entity.HasKey(e => e.id).HasName("Hospital_pkey");

            entity.ToTable("sys_hospital", "system");

            entity.Property(e => e.id)
                .ValueGeneratedNever()
                .HasComment("医院ID");
            entity.Property(e => e.name)
                .IsRequired()
                .HasComment("医院名称");
        });

        modelBuilder.Entity<sys_license>(entity =>
        {
            entity.HasKey(e => e.id).HasName("Sys_License_pkey");

            entity.ToTable("sys_license", "system", tb => tb.HasComment("许可证表"));

            entity.Property(e => e.id)
                .ValueGeneratedNever()
                .HasComment("主键");
            entity.Property(e => e.code)
                .IsRequired()
                .HasComment("许可证代码");
            entity.Property(e => e.create_time)
                .HasDefaultValueSql("CURRENT_TIMESTAMP")
                .HasComment("创建时间")
                .HasColumnType("timestamp without time zone");
            entity.Property(e => e.describe).HasComment("许可证描述");
            entity.Property(e => e.is_valid)
                .HasDefaultValue(true)
                .HasComment("是否有效");
            entity.Property(e => e.name)
                .IsRequired()
                .HasComment("许可证名称");
            entity.Property(e => e.project_id).HasComment("授权项目ID");
            entity.Property(e => e.project_name).HasComment("授权项目名称");
            entity.Property(e => e.type).HasComment("许可证类型");
            entity.Property(e => e.valid_end_time)
                .HasComment("有效结束时间")
                .HasColumnType("timestamp without time zone");
            entity.Property(e => e.valid_start_time)
                .HasComment("有效开始时间")
                .HasColumnType("timestamp without time zone");

            entity.HasOne(d => d.project).WithMany(p => p.sys_license)
                .HasForeignKey(d => d.project_id)
                .HasConstraintName("Sys_License_project_id_fkey");
        });

        modelBuilder.Entity<sys_permission>(entity =>
        {
            entity.HasKey(e => e.id).HasName("Permission_pkey");

            entity.ToTable("sys_permission", "system", tb => tb.HasComment("功能权限"));

            entity.Property(e => e.id)
                .HasDefaultValueSql("uuid_generate_v1()")
                .HasComment("ID");
            entity.Property(e => e.code)
                .HasMaxLength(255)
                .HasComment("权限Code");
            entity.Property(e => e.describe).HasComment("权限描述");
            entity.Property(e => e.name)
                .IsRequired()
                .HasComment("权限名称");
            entity.Property(e => e.type)
                .IsRequired()
                .HasDefaultValueSql("'Default'::text")
                .HasComment("权限类型");
        });

        modelBuilder.Entity<sys_permission_data>(entity =>
        {
            entity.HasKey(e => e.id).HasName("Sys_Role_DataPermission_pkey");

            entity.ToTable("sys_permission_data", "system", tb => tb.HasComment("角色数据权限表"));

            entity.Property(e => e.id).ValueGeneratedNever();
            entity.Property(e => e.department_id).HasComment("科室ID");
            entity.Property(e => e.department_name).HasComment("科室名称");
            entity.Property(e => e.form_card_id).HasComment("表单卡ID");
            entity.Property(e => e.form_card_name).HasComment("表单卡名称");
            entity.Property(e => e.form_form_id).HasComment("表单ID");
            entity.Property(e => e.form_form_name).HasComment("表单名称");
            entity.Property(e => e.form_formset_id).HasComment("表单集ID");
            entity.Property(e => e.form_formset_name).HasComment("表单集名称");
            entity.Property(e => e.form_project_id).HasComment("项目ID");
            entity.Property(e => e.form_project_name).HasComment("项目名称");
            entity.Property(e => e.hospital_id).HasComment("医院ID");
            entity.Property(e => e.hospital_name)
                .IsRequired()
                .HasComment("医院名称");
            entity.Property(e => e.region_id).HasComment("区域ID");
            entity.Property(e => e.region_name).HasComment("区域名称");
            entity.Property(e => e.role_id).HasComment("角色ID");
            entity.Property(e => e.ward_id).HasComment("病区ID");
            entity.Property(e => e.ward_name).HasComment("病区名称");

            entity.HasOne(d => d.hospital).WithMany(p => p.sys_permission_data)
                .HasForeignKey(d => d.hospital_id)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("Sys_Role_DataPermission_HospitalId_fkey");

            entity.HasOne(d => d.role).WithMany(p => p.sys_permission_data)
                .HasForeignKey(d => d.role_id)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("Sys_Role_DataPermission_RoleId_fkey");
        });

        modelBuilder.Entity<sys_region>(entity =>
        {
            entity.HasKey(e => e.id).HasName("Region_pkey");

            entity.ToTable("sys_region", "system");

            entity.Property(e => e.id)
                .ValueGeneratedNever()
                .HasComment("院区ID");
            entity.Property(e => e.hospital_id).HasComment("医院ID");
            entity.Property(e => e.name)
                .IsRequired()
                .HasComment("院区名称");

            entity.HasOne(d => d.hospital).WithMany(p => p.sys_region)
                .HasForeignKey(d => d.hospital_id)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_HospitalId_SysHospital_Id");
        });

        modelBuilder.Entity<sys_role>(entity =>
        {
            entity.HasKey(e => e.id).HasName("Role_pkey");

            entity.ToTable("sys_role", "system", tb => tb.HasComment("角色"));

            entity.Property(e => e.id)
                .HasDefaultValueSql("uuid_generate_v1()")
                .HasComment("角色ID");
            entity.Property(e => e.describe)
                .IsRequired()
                .HasComment("角色描述");
            entity.Property(e => e.is_valid)
                .HasDefaultValue(false)
                .HasComment("是否有效");
            entity.Property(e => e.name)
                .IsRequired()
                .HasComment("角色名称");
            entity.Property(e => e.owner_user_id).HasComment("拥有者用户ID");
            entity.Property(e => e.type)
                .IsRequired()
                .HasDefaultValueSql("'Default'::text")
                .HasComment("角色类型");

            entity.HasMany(d => d.license).WithMany(p => p.role)
                .UsingEntity<Dictionary<string, object>>(
                    "sys_map_role_license",
                    r => r.HasOne<sys_license>().WithMany()
                        .HasForeignKey("license_id")
                        .OnDelete(DeleteBehavior.ClientSetNull)
                        .HasConstraintName("Map_Role_License_LicenseId_fkey"),
                    l => l.HasOne<sys_role>().WithMany()
                        .HasForeignKey("role_id")
                        .OnDelete(DeleteBehavior.ClientSetNull)
                        .HasConstraintName("Map_Role_License_RoleId_fkey"),
                    j =>
                    {
                        j.HasKey("role_id", "license_id").HasName("Map_Role_License_pkey");
                        j.ToTable("sys_map_role_license", "system", tb => tb.HasComment("角色和许可证映射表"));
                        j.IndexerProperty<Guid>("role_id").HasComment("角色ID");
                        j.IndexerProperty<Guid>("license_id").HasComment("许可证ID");
                    });

            entity.HasMany(d => d.permission).WithMany(p => p.role)
                .UsingEntity<Dictionary<string, object>>(
                    "sys_map_role_permission",
                    r => r.HasOne<sys_permission>().WithMany()
                        .HasForeignKey("permission_id")
                        .OnDelete(DeleteBehavior.ClientSetNull)
                        .HasConstraintName("Map_Role_Permission_PermissionId_fkey"),
                    l => l.HasOne<sys_role>().WithMany()
                        .HasForeignKey("role_id")
                        .OnDelete(DeleteBehavior.ClientSetNull)
                        .HasConstraintName("Map_Role_Permission_RoleId_fkey"),
                    j =>
                    {
                        j.HasKey("role_id", "permission_id").HasName("Map_Role_Permission_pkey");
                        j.ToTable("sys_map_role_permission", "system", tb => tb.HasComment("角色和权限映射表"));
                        j.IndexerProperty<Guid>("role_id").HasComment("角色ID");
                        j.IndexerProperty<Guid>("permission_id").HasComment("权限ID");
                    });
        });

        modelBuilder.Entity<sys_user>(entity =>
        {
            entity.HasKey(e => e.id).HasName("User_pkey");

            entity.ToTable("sys_user", "system", tb => tb.HasComment("用户"));

            entity.Property(e => e.id)
                .HasDefaultValueSql("uuid_generate_v1()")
                .HasComment("用户ID");
            entity.Property(e => e.account)
                .IsRequired()
                .HasComment("用户登录账号");
            entity.Property(e => e.avatar_filename).HasComment("头像文件名");
            entity.Property(e => e.can_grant_to_other)
                .HasDefaultValue(false)
                .HasComment("向后授权");
            entity.Property(e => e.display_name)
                .IsRequired()
                .HasDefaultValueSql("''::text")
                .HasComment("显示名称");
            entity.Property(e => e.is_valid)
                .HasDefaultValue(true)
                .HasComment("是否有效");
            entity.Property(e => e.last_login_ip).HasComment("最后登录IP");
            entity.Property(e => e.last_login_time)
                .HasComment("最后登录时间")
                .HasColumnType("timestamp(0) without time zone");
            entity.Property(e => e.password)
                .IsRequired()
                .HasComment("用户登录密码");
            entity.Property(e => e.weixin_id).HasComment("微信ID");
            entity.Property(e => e.weixin_message).HasComment("微信消息");

            entity.HasMany(d => d.role).WithMany(p => p.user)
                .UsingEntity<Dictionary<string, object>>(
                    "sys_map_user_role",
                    r => r.HasOne<sys_role>().WithMany()
                        .HasForeignKey("role_id")
                        .OnDelete(DeleteBehavior.ClientSetNull)
                        .HasConstraintName("Map_User_Role_RoleId_fkey"),
                    l => l.HasOne<sys_user>().WithMany()
                        .HasForeignKey("user_id")
                        .OnDelete(DeleteBehavior.ClientSetNull)
                        .HasConstraintName("Map_User_Role_UserId_fkey"),
                    j =>
                    {
                        j.HasKey("user_id", "role_id").HasName("Map_User_Role_pkey");
                        j.ToTable("sys_map_user_role", "system", tb => tb.HasComment("用户和角色映射表"));
                        j.IndexerProperty<Guid>("user_id").HasComment("用户ID");
                        j.IndexerProperty<Guid>("role_id").HasComment("角色ID");
                    });
        });

        modelBuilder.Entity<sys_ward>(entity =>
        {
            entity.HasKey(e => e.id).HasName("Ward_pkey");

            entity.ToTable("sys_ward", "system");

            entity.Property(e => e.id)
                .ValueGeneratedNever()
                .HasComment("病区ID");
            entity.Property(e => e.department_id).HasComment("科室ID");
            entity.Property(e => e.hospital_id).HasComment("医院ID");
            entity.Property(e => e.name)
                .IsRequired()
                .HasComment("病区名称");
            entity.Property(e => e.region_id).HasComment("院区ID");

            entity.HasOne(d => d.department).WithMany(p => p.sys_ward)
                .HasForeignKey(d => d.department_id)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_DepartmentId_SysDepartment_Id");

            entity.HasOne(d => d.hospital).WithMany(p => p.sys_ward)
                .HasForeignKey(d => d.hospital_id)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_HospitalId_SysHospital_Id");

            entity.HasOne(d => d.region).WithMany(p => p.sys_ward)
                .HasForeignKey(d => d.region_id)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_RegionId_SysRegion_Id");
        });

        modelBuilder.Entity<table_definition>(entity =>
        {
            entity.HasKey(e => e.id).HasName("TableDefinition_pkey");

            entity.Property(e => e.id)
                .HasDefaultValueSql("uuid_generate_v1()")
                .HasComment("ID");
            entity.Property(e => e.display_name)
                .IsRequired()
                .HasComment("表的显示名称");
            entity.Property(e => e.history_ver_array).HasComment("历史版本号数组");
            entity.Property(e => e.is_changed)
                .HasDefaultValue(true)
                .HasComment("版本发布后是否发生改变");
            entity.Property(e => e.is_valid)
                .HasDefaultValue(true)
                .HasComment("是否有效");
            entity.Property(e => e.node_type)
                .IsRequired()
                .HasComment("节点类型（Visual，Table）");
            entity.Property(e => e.note_cn).HasComment("中文释义");
            entity.Property(e => e.note_en).HasComment("英文释义");
            entity.Property(e => e.parent_node_id).HasComment("父节点ID");
            entity.Property(e => e.sorted_index)
                .HasDefaultValue(0)
                .HasComment("排序索引");
            entity.Property(e => e.unique_name)
                .IsRequired()
                .HasComment("表的唯一名称");
            entity.Property(e => e.update_time)
                .HasDefaultValueSql("now()")
                .HasColumnType("timestamp without time zone");
        });

        modelBuilder.Entity<unique_patient>(entity =>
        {
            entity.HasKey(e => e.id).HasName("unique_patient_pkey");

            entity.ToTable(tb => tb.HasComment("唯一患者表"));

            entity.Property(e => e.id)
                .ValueGeneratedNever()
                .HasComment("主键");
            entity.Property(e => e.address_content).HasComment("地址内容");
            entity.Property(e => e.address_type).HasComment("地址类型");
            entity.Property(e => e.birthday).HasComment("出生日期");
            entity.Property(e => e.default_flag).HasComment("默认就诊人标记");
            entity.Property(e => e.gender).HasComment("性别");
            entity.Property(e => e.login_password).HasComment("患者手机号登录密码");
            entity.Property(e => e.medical_card).HasComment("医保卡号");
            entity.Property(e => e.medical_type).HasComment("医保类型");
            entity.Property(e => e.name).HasComment("姓名");
            entity.Property(e => e.old_name).HasComment("曾用名");
            entity.Property(e => e.phone_number).HasComment("联系电话号码");
            entity.Property(e => e.phone_type).HasComment("联系电话类型");
            entity.Property(e => e.sid_address).HasComment("身份证地址");
            entity.Property(e => e.sid_number).HasComment("身份证号码");
            entity.Property(e => e.sid_type).HasComment("身份证类型");
            entity.Property(e => e.sid_verification).HasComment("身份证验证");
            entity.Property(e => e.weixin_avatar_url).HasComment("微信头像");
            entity.Property(e => e.weixin_id).HasComment("微信号（小程序的openid）");
            entity.Property(e => e.weixin_phone_number).HasComment("微信号绑定电话");
            entity.Property(e => e.weixin_relation).HasComment("与当前微信联系人关系");
        });
        modelBuilder.HasSequence("seq_CubeVersionNumber");

        OnModelCreatingPartial(modelBuilder);
    }

    partial void OnModelCreatingPartial(ModelBuilder modelBuilder);
}