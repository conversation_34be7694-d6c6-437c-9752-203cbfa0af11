using System;
using System.Text;
using System.Threading.Tasks;
using Bio.AI;
using Bio.AI.Models;
using DataAccess.Models;
using Microsoft.Extensions.Logging;

namespace BusinessLogic.Cube;

public partial class MedDictService : IMedDictService
{
    /// <summary>
    /// 分析列定义应该归属于哪个分类
    /// </summary>
    /// <param name="context">数据库上下文</param>
    /// <param name="columnDefinition">列定义实例</param>
    /// <param name="logger">日志记录器</param>
    /// <param name="onPartialResponse">流式输出回调函数，用于接收部分响应</param>
    /// <returns>分析结果</returns>
    public async Task<string> BelongToAnalysisAsync(
        CubeContext context,
        column_definition columnDefinition,
        ILogger logger,
        Action<string>? onPartialResponse = null)
    {
        ArgumentNullException.ThrowIfNull(context);
        ArgumentNullException.ThrowIfNull(columnDefinition);

        try
        {
            // 获取完整的数据字典分类信息
            string dataDictionary = await ExportAllNodesDataDictionaryAsync(context);

            // 获取列定义所属表的信息
            var tableDefinition = await context.table_definition
                .FindAsync(columnDefinition.table_id)
                ?? throw new InvalidOperationException($"找不到列定义所属的表 (ID: {columnDefinition.table_id})");

            // 构建提示词
            var promptBuilder = new StringBuilder();
            promptBuilder.AppendLine("# 任务描述");
            promptBuilder.AppendLine("分析给定的数据库列定义应该归属于哪个分类。");
            promptBuilder.AppendLine();

            promptBuilder.AppendLine("# 列定义信息");
            promptBuilder.AppendLine($"- 列名称: {columnDefinition.display_name}");
            promptBuilder.AppendLine($"- 列编码: {columnDefinition.unique_name}");
            promptBuilder.AppendLine($"- 数据类型: {columnDefinition.data_type}");

            if (columnDefinition.data_type == "选择" && columnDefinition.option_set != null && columnDefinition.option_set.Count > 0)
            {
                promptBuilder.AppendLine($"- 选项集: {string.Join(", ", columnDefinition.option_set)}");
            }
            promptBuilder.AppendLine();

            promptBuilder.AppendLine("# 所属表信息");
            promptBuilder.AppendLine($"- 表名称: {tableDefinition.display_name}");
            promptBuilder.AppendLine($"- 表编码: {tableDefinition.unique_name}");
            promptBuilder.AppendLine();

            promptBuilder.AppendLine("# 数据字典分类信息");
            promptBuilder.AppendLine(dataDictionary);
            promptBuilder.AppendLine();

            promptBuilder.AppendLine("# 要求");
            promptBuilder.AppendLine("1. 分析这个列定义应该归属于哪个分类（可以给出多个答案）");
            promptBuilder.AppendLine("2. 给出详细的分类解释，说明为什么这个列定义应该归属于这些分类");
            promptBuilder.AppendLine("3. 如果有多个可能的分类，请按照匹配度从高到低排序");
            promptBuilder.AppendLine("4. 回答格式应包含'推荐分类'和'分析理由'两部分");
            promptBuilder.AppendLine("5. 在推荐分类中必须包含分类的ID和父节点信息，从根节点开始，依次输出，格式为：根节点 > 二级节点 > 三级节点 > ... > 分类名称 (ID: 节点ID)");

            string systemPrompt = "你是一个专业的医学数据库分类专家，擅长分析医学数据库中的列定义应该归属于哪个分类。请基于提供的数据字典分类信息，分析给定的列定义应该归属于哪个分类。";
            string userPrompt = promptBuilder.ToString();

            logger.LogInformation("开始调用LLM进行列定义归属分析: {ColumnName}", columnDefinition.display_name);

            string result;

            if (onPartialResponse != null)
            {
                // 使用Bio.AI流式输出
                var fullResponseBuilder = new StringBuilder();

                await foreach (var streamResult in BioAI.ThinkStreamAsync(
                    prompt: userPrompt,
                    model: LLModel.OpenRouter.Gemini_25_Flash_05_20,
                    systemPrompt: systemPrompt,
                    onTokenReceived: token => {
                        // 将部分响应添加到完整响应中
                        fullResponseBuilder.Append(token.Content);
                        // 调用回调函数，将部分响应传递给调用者
                        onPartialResponse(token.Content);
                    }))
                {
                    // 流式处理完成
                }

                result = fullResponseBuilder.ToString();
            }
            else
            {
                // 使用Bio.AI非流式输出
                var bioResult = await BioAI.ThinkAsync(
                    prompt: userPrompt,
                    model: LLModel.OpenRouter.Gemini_25_Flash_05_20,
                    systemPrompt: systemPrompt
                );

                result = bioResult.Answer;
            }

            logger.LogInformation("列定义归属分析完成: {ColumnName}", columnDefinition.display_name);

            return result;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "执行列定义归属分析时出错: {ColumnName}", columnDefinition.display_name);
            throw;
        }
    }
}
