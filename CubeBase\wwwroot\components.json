﻿{
  "ComponentType": "MudField",
  "Parameters": {
    "Label": "列表区域",
    "Variant": "Variant.Outlined"
  },
  "Children": [
    {
      "ComponentType": "MudButton",
      "Parameters": {
        "Color": "Color.Primary",
        "Variant": "Variant.Filled",
        "Disabled": "@(_selectedTableEntity == null || _selectedTableEntity.NodeType == \"Table\")"
      },
      "Events": {
        "OnClick": "AddNewTable"
      },
      "Content": "增加新节点"
    },
    {
      "ComponentType": "MudPaper",
      "Parameters": {
        "Class": "pa-0",
        "Elevation": "0"
      },
      "Children": [
        {
          "ComponentType": "TreeView",
          "Parameters": {
            "TItem": "TableDefinition",
            "Items": "@_treeItemsList",
            "ShowIcon": "true"
          },
          "Events": {
            "OnTreeItemClick": "OnTreeViewItemClick"
          },
          "References": {
            "@ref": "_pageElements_TreeView"
          }
        }
      ]
    },
    {
      "ComponentType": "button",
      "Events": {
        "onclick": "ToggleVisibility"
      },
      "Content": "@(isVisible ? \"隐藏\" : \"显示\") 组件"
    }
  ]
}