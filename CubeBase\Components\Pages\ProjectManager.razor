﻿@page "/ProjectManager"
@inherits BasePage
@inject ILogger<ProjectManager> inj_logger

<MudGrid Class="ma-0">
    <!-- 医院管理部分 -->
    <MudItem xs="10">
        <h5>医院管理</h5>
        <Table @ref="_pageElements_ProjectTable" TItem="form_project" OnClickRowCallback="OnHospitalTableRowClick"
        OnQueryAsync="OnProjectQueryAsync" SearchMode="SearchMode.Top" HeaderStyle="TableHeaderStyle.Light"
        LineNoText="序号" ShowToolbar ShowAddButton="false" ShowEditButton="false" ShowDeleteButton="false"
        ShowExtendButtons="false" ClickToSelect IsStriped IsBordered ShowLineNo ShowColumnList="true">
            <TableColumns>
                <TableColumn @bind-Field="@context.name" Text="名称" Sortable="true" Filterable="true" />
                @* <TableColumn @bind-Field="@context.description" Text="描述" Sortable="true" Filterable="true" /> *@
                <TableColumn @bind-Field="@context.hospital_name" Text="所属医院" Sortable="true" Filterable="true" />
                <TableColumn @bind-Field="@context.department_name" Text="所属科室" Sortable="true" Filterable="true" />
                <TableColumn @bind-Field="@context.ward_name" Text="所属病区" Sortable="true" Filterable="true" />
            </TableColumns>
        </Table>
        <!-- 添加项目按钮 -->
        <MudButton Class="ma-2 pa-2" Color="Color.Primary" Variant="Variant.Filled" OnClick="AddNewProject">
            添加
        </MudButton>
    </MudItem>
</MudGrid>

<!-- 项目添加/编辑模态框 -->
@if (_tempProjectEntity != null)
{
    <Modal @ref="_pageElements_ProjectModal" IsBackdrop="true">
        <ModalDialog Title="添加/编辑 - 项目" Size="BSize.Medium">
            <BodyTemplate>
                <EditForm Model="_tempProjectEntity" OnSubmit="OnProjectSubmit" class="project-form">
                    <DataAnnotationsValidator />
                    <ValidationSummary />

                    <div class="form-group">
                        <label for="projectId">主键</label>
                        <input id="projectId" class="form-control" @bind=" _tempProjectEntity.id" readonly disabled />
                    </div>

                    <div class="form-group">
                        <label for="projectName">项目名称</label>
                        <input id="projectName" type="text" class="form-control" @bind=" _tempProjectEntity.name" required />
                    </div>

                    @* <div class="form-group"> *@
                    @*     <label for="projectDescription">项目描述</label> *@
                    @*     <textarea id="projectDescription" class="form-control" @bind=" _tempProjectEntity.description"></textarea> *@
                    @* </div> *@

                    <div class="form-group">
                        <label for="hospitalSelect">所属医院</label>
                        <select id="hospitalSelect" class="form-control" @bind=" _tempProjectEntity.hospital_id" @bind:after="OnSelectedHospitalChanged">
                            @if (_contextHospitalList != null)
                            {
                                @foreach (var hospitalItem in _contextHospitalList.OrderBy(td => td.name))
                                {
                                    <option value="@hospitalItem.id">@hospitalItem.name</option>
                                }
                            }
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="departmentSelect">所属科室</label>
                        <select id="departmentSelect" class="form-control" @bind=" _tempProjectEntity.department_id" @bind:after="OnSelectedDepartmentChanged">
                            @if (_selectDepartmentList != null)
                            {
                                @foreach (var departmentItem in _selectDepartmentList.OrderBy(td => td.name))
                                {
                                    <option value="@departmentItem.id">@($"{departmentItem.hospital.name} - {departmentItem.name}")</option>
                                }
                            }
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="wardSelect">所属病区</label>
                        <select id="wardSelect" class="form-control" @bind=" _tempProjectEntity.ward_id">
                            @if (_selectWardList != null)
                            {
                                @foreach (var wardItem in _selectWardList.OrderBy(td => td.name))
                                {
                                    <option value="@wardItem.id">@($"{wardItem.hospital.name} - {wardItem.department.name} - {wardItem.name}")</option>
                                }
                            }
                        </select>
                    </div>

                    <div class="form-group text-right">
                        <button type="submit" class="submit-button">提交</button>
                    </div>
                </EditForm>
            </BodyTemplate>
        </ModalDialog>
    </Modal>
}

@code {
    // 上下文数据列表
    private List<form_project> _contextProjectList;
    private List<sys_hospital> _contextHospitalList;
    private List<sys_department> _contextDepartmentList;
    private List<sys_ward> _contextWardList;
    // 选中的实体
    private form_project? _selectedProjectEntity;
    private List<sys_department>? _selectDepartmentList;
    private List<sys_ward>? _selectWardList;
    // 临时实体，用于编辑
    private form_project? _tempProjectEntity;
    // 表格引用
    private Table<form_project>? _pageElements_ProjectTable { get; set; }
    // 模态框引用
    private Modal? _pageElements_ProjectModal;
    // 显示标志位
    private bool _showProjectModal = false;
    // 组件渲染后首次执行
    protected override async Task OnInitializedAsync()
    {
        await InvokeAsync(async () => await ReloadAll());
    }

    // 重新加载所有数据
    private async Task ReloadAll()
    {
        // 顺序执行数据库操作，避免并发使用 DbContext
        _contextProjectList = await inj_cubeContext.form_project
        .AsNoTracking()
        .OrderBy(td => td.name)
        .ToListAsync();

        _contextHospitalList = await inj_cubeContext.sys_hospital
        .OrderBy(td => td.name)
        .ToListAsync();

        _contextDepartmentList = await inj_cubeContext.sys_department
        .Include(td => td.hospital)
        .OrderBy(td => td.name)
        .ToListAsync();

        _contextWardList = await inj_cubeContext.sys_ward
        .Include(td => td.hospital)
        .OrderBy(td => td.name)
        .ToListAsync();

        if (_pageElements_ProjectTable != null)
        {
            await _pageElements_ProjectTable.QueryAsync();
        }

        StateHasChanged();
    }

    private Task<QueryData<form_project>> OnProjectQueryAsync(QueryPageOptions options)
    {
        return OnQueryAsync(_contextProjectList, options, "name", h => h.name);
    }

    private async Task OnHospitalTableRowClick(form_project item)
    {
        _selectedProjectEntity = item;
        // 这里可以添加更多逻辑，例如打开模态框或导航到详细信息页面
        inj_logger.LogInformation($"Selected Project: {item.name}");

        _tempProjectEntity = inj_mapper.Map<form_project>(item);

        await ShowModel();
    }

    private async Task AddNewProject()
    {
        _selectedProjectEntity = new();
        _selectedProjectEntity.id = SequentialGuidGenerator.NewGuid();
        _selectedProjectEntity.hospital_id = _contextHospitalList.First().id;
        _selectedProjectEntity.department_id = _contextDepartmentList.Where(td => td.hospital.id == _selectedProjectEntity.hospital_id).First().id;
        _selectedProjectEntity.ward_id = _contextWardList.Where(td => td.hospital.id == _selectedProjectEntity.hospital_id).First().id;
        _tempProjectEntity = inj_mapper.Map<form_project>(_selectedProjectEntity);

        await ShowModel();
    }

    private async Task ShowModel()
    {
        if (_pageElements_ProjectModal != null)
        {
            await InvokeAsync(() => _pageElements_ProjectModal.Show());
        }
        _selectDepartmentList = _contextDepartmentList.Where(td => td.hospital.id == _tempProjectEntity.hospital_id).OrderBy(td => td.name).ToList();
        _selectWardList = _contextWardList.Where(td => td.department.id == _tempProjectEntity.department_id).OrderBy(td => td.name).ToList();
        await InvokeAsync(StateHasChanged);
    }

    private async Task OnProjectSubmit()
    {
        if (_tempProjectEntity != null)
        {
            inj_mapper.Map(_tempProjectEntity, _selectedProjectEntity);

            if (_selectedProjectEntity.id == Guid.Empty)
            {
                inj_snackbar.Add("_selectedProjectEntity 为空！", Severity.Error);
                return;
            }

            _selectedProjectEntity.hospital_name = _contextHospitalList.First(td => td.id == _selectedProjectEntity.hospital_id).name;
            _selectedProjectEntity.department_name = _contextDepartmentList.First(td => td.id == _selectedProjectEntity.department_id).name;
            _selectedProjectEntity.ward_name = _contextWardList.First(td => td.id == _selectedProjectEntity.ward_id).name;
            _selectedProjectEntity.display_name = _selectedProjectEntity.name;

            using (var transaction = await inj_cubeContext.Database.BeginTransactionAsync())
            {
                try
                {
                    bool isNew = !_contextProjectList.Any(td => td.id.Equals(_selectedProjectEntity.id));

                    if (isNew)
                    {
                        inj_cubeContext.form_project.Add(_selectedProjectEntity);
                        inj_snackbar.Add("添加项目成功", Severity.Success);
                    }
                    else
                    {
                        var existingEntity = await inj_cubeContext.form_project
                            .FirstOrDefaultAsync(p => p.id == _selectedProjectEntity.id);
                        if (existingEntity != null)
                        {
                            inj_cubeContext.Entry(existingEntity).State = EntityState.Detached;
                        }
                        inj_cubeContext.form_project.Update(_selectedProjectEntity);
                    }
                    await inj_cubeContext.SaveChangesAsync();
                    await transaction.CommitAsync();
                    await ReloadAll();
                }
                catch (Exception ex)
                {
                    await transaction.RollbackAsync();
                    inj_logger.LogError(ex, $"保存 {typeof(form_project).Name} 时出错: {ex.Message}");
                    inj_snackbar.Add($"保存 {typeof(form_project).Name} 信息时发生错误: {ex.Message}", Severity.Error);
                }
            }
        }
        await _pageElements_ProjectModal.Close();
    }

    private async Task OnSelectedHospitalChanged()
    {
        // 更新科室列表，筛选出与选定医院相关的科室
        _selectDepartmentList = _contextDepartmentList
            .Where(d => d.hospital.id == _tempProjectEntity.hospital_id)
            .OrderBy(d => d.name)
            .ToList();

        // 如果有科室，设置默认选中的科室
        if (_selectDepartmentList.Any())
        {
            _tempProjectEntity.department_id = _selectDepartmentList.First().id;

        }
        else
        {
            _tempProjectEntity.department_id = Guid.Empty;
        }

        // 更新病区列表，筛选出与选定医院相关的病区
        _selectWardList = _contextWardList
            .Where(w => w.department.id == _tempProjectEntity.department_id)
            .OrderBy(w => w.name)
            .ToList();

        // 如果有病区，设置默认选中的病区
        if (_selectWardList.Any())
        {
            _tempProjectEntity.ward_id = _selectWardList.First().id;
        }
        else
        {
            _tempProjectEntity.ward_id = Guid.Empty;
        }

        // 刷新界面以反映更改
        await InvokeAsync(StateHasChanged);
    }

    private async Task OnSelectedDepartmentChanged()
    {
        // 更新病区列表，筛选出与选定医院相关的病区
        _selectWardList = _contextWardList
            .Where(w => w.department.id == _tempProjectEntity.department_id)
            .OrderBy(w => w.name)
            .ToList();

        // 如果有病区，设置默认选中的病区
        if (_selectWardList.Any())
        {
            _tempProjectEntity.ward_id = _selectWardList.First().id;
        }
        else
        {
            _tempProjectEntity.ward_id = Guid.Empty;
        }

        // 刷新界面以反映更改
        await InvokeAsync(StateHasChanged);
    }
}