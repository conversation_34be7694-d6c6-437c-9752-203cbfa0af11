using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;
using DataAccess.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Logging.Abstractions;
using Microsoft.JSInterop;
using Microsoft.Extensions.Configuration;

namespace BusinessLogic.Cube;

/// <summary>
/// 卡片导入服务类
/// </summary>
public class CardImportService
{
    private readonly ILogger<CardImportService> _logger;
    private readonly IJSRuntime _jsRuntime;
    private readonly IConfiguration _configuration;

    public CardImportService(ILogger<CardImportService> logger, IJSRuntime jsRuntime, IConfiguration configuration)
    {
        _logger = logger;
        _jsRuntime = jsRuntime;
        _configuration = configuration;
    }

    /// <summary>
    /// 从JSON文件导入卡片
    /// </summary>
    /// <param unique_name="context">数据库上下文</param>
    /// <param unique_name="form">目标表单对象</param>
    /// <param unique_name="targetCardId">目标父卡片ID，如果为null则导入到表单根层级</param>
    /// <param unique_name="jsonContent">JSON内容字符串</param>
    /// <returns>导入结果信息</returns>
    public async Task<ImportResult> ImportCardFromJsonAsync(CubeContext context, form_form form, Guid? targetCardId, string jsonContent)
    {
        var result = new ImportResult();
        
        try
        {
            if (targetCardId.HasValue)
            {
                var targetCard = await context.form_card.FindAsync(targetCardId.Value);
                if (targetCard == null)
                {
                    _logger.LogWarning($"找不到指定的目标卡片ID: {targetCardId.Value}");
                    result.Success = false;
                    result.Message = "找不到指定的目标卡片";
                    return result;
                }
                
                _logger.LogInformation($"开始导入卡片到目标卡片: {targetCard.name}(ID: {targetCard.id})");
            }
            else
            {
                _logger.LogInformation($"开始导入卡片到表单根级: {form.name}(ID: {form.id})");
            }

            // 解析并验证JSON
            var jsonElement = await ParseAndValidateJsonAsync(jsonContent);
            if (jsonElement == null)
            {
                result.Success = false;
                result.Message = "JSON解析失败或格式不正确";
                return result;
            }

            // 验证导入结构是否符合规范
            if (!ValidateJsonStructure(jsonElement.Value))
            {
                result.Success = false;
                result.Message = "JSON结构不符合导入规范";
                return result;
            }

            // 获取CardService实例
            var cardServiceLogger = NullLogger<CardService>.Instance;
            var cardService = new CardService(cardServiceLogger, _configuration);

            // 建立column_definition缓存
            var columnDefinitions = await context.column_definition.Include(c => c.table).ToListAsync();
            var columnDefinitionDict = columnDefinitions.ToDictionary(c => c.id, c => c);

            // 执行导入逻辑 - 将整个JSON对象作为一个卡片处理
            var (rootCard, questionCount) = await ImportCardAsync(context, form, targetCardId, jsonElement.Value, cardService, columnDefinitionDict);
            
            // 保存更改
            await context.SaveChangesAsync();

            // 设置成功结果
            result.Success = true;
            result.Message = $"成功导入卡片，共导入1个卡片及其子卡片，{questionCount}个问题";
            result.ImportedCards = new List<form_card> { rootCard };
            result.ImportedQuestionCount = questionCount;
            
            _logger.LogInformation($"成功导入卡片到表单: {form.name}，共导入1个卡片及其子卡片，{questionCount}个问题");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "导入卡片时发生异常");
            result.Success = false;
            result.Message = $"导入失败: {ex.Message}";
        }
        
        return result;
    }

    /// <summary>
    /// 解析和验证JSON数据
    /// </summary>
    /// <param unique_name="jsonContent">JSON内容字符串</param>
    /// <returns>解析后的JSON对象，如果解析失败则返回null</returns>
    private async Task<JsonElement?> ParseAndValidateJsonAsync(string jsonContent)
    {
        try
        {
            // 解析JSON
            using JsonDocument document = JsonDocument.Parse(jsonContent);
            var root = document.RootElement;
            
            // 验证基本结构
            if (!ValidateJsonStructure(root))
            {
                _logger.LogWarning("JSON结构验证失败");
                return null;
            }
            
            return root.Clone();
        }
        catch (JsonException ex)
        {
            _logger.LogError(ex, "解析JSON时出错");
            return null;
        }
    }

    /// <summary>
    /// 验证JSON结构是否符合预期格式
    /// </summary>
    /// <param unique_name="root">JSON根元素</param>
    /// <returns>是否符合格式</returns>
    private bool ValidateJsonStructure(JsonElement root)
    {
        try
        {
            // 验证根元素必要字段
            if (!root.TryGetProperty("type", out var typeElement) || 
                typeElement.GetString() != "card")
            {
                _logger.LogWarning("JSON根元素必须是卡片类型");
                return false;
            }

            if (!root.TryGetProperty("name", out _))
            {
                _logger.LogWarning("JSON根元素缺少name字段");
                return false;
            }

            if (!root.TryGetProperty("items", out var itemsElement) || 
                itemsElement.ValueKind != JsonValueKind.Array)
            {
                _logger.LogWarning("JSON根元素必须包含items数组");
                return false;
            }

            // 验证元数据
            if (root.TryGetProperty("metadata", out var metadataElement))
            {
                if (!metadataElement.TryGetProperty("version", out var versionElement))
                {
                    _logger.LogWarning("JSON元数据缺少version字段");
                    return false;
                }
                
                // 检查版本兼容性
                var version = versionElement.GetString();
                if (version != "2.0")
                {
                    _logger.LogWarning($"不支持的JSON格式版本: {version}，当前支持的版本是2.0");
                    return false;
                }
            }
            
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "验证JSON结构时出错");
            return false;
        }
    }

    /// <summary>
    /// 验证卡片导入JSON格式是否符合基本要求
    /// </summary>
    /// <param unique_name="jsonContent">要验证的JSON字符串</param>
    /// <returns>验证结果，包含是否有效和错误消息</returns>
    private (bool isValid, string message) ValidateCardJson(string jsonContent)
    {
        try
        {
            using JsonDocument doc = JsonDocument.Parse(jsonContent);
            JsonElement root = doc.RootElement;
            
            // 检查基本字段
            if (!root.TryGetProperty("type", out JsonElement typeElement) || 
                typeElement.GetString() != "card")
            {
                return (false, "JSON格式错误：根元素必须是card类型");
            }
            
            // 检查必要的顶级字段
            string[] requiredFields = { "name", "items", "properties", "metadata" };
            foreach (string field in requiredFields)
            {
                if (!root.TryGetProperty(field, out _))
                {
                    return (false, $"JSON格式错误：缺少必要字段 {field}");
                }
            }
            
            // 检查统计信息
            if (!root.TryGetProperty("statistics", out JsonElement stats) ||
                !stats.TryGetProperty("total_cards", out _) ||
                !stats.TryGetProperty("total_questions", out _))
            {
                return (false, "JSON格式错误：缺少统计信息字段");
            }
            
            return (true, "JSON格式验证通过");
        }
        catch (JsonException ex)
        {
            return (false, $"JSON解析错误: {ex.Message}");
        }
        catch (Exception ex)
        {
            return (false, $"验证过程中发生错误: {ex.Message}");
        }
    }

    // 递归导入卡片及其子项
    private async Task<(form_card Card, int QuestionCount)> ImportCardAsync(
        CubeContext context, 
        form_form form, 
        Guid? parentCardId, 
        JsonElement cardJson, 
        CardService cardService,
        Dictionary<Guid, column_definition> columnDefinitionDict)
    {
        int questionCount = 0;
        
        // 检查JSON元素是否包含type字段，确认它是卡片
        if (!cardJson.TryGetProperty("type", out var typeJson) || typeJson.GetString() != "card")
        {
            throw new Exception("JSON元素不是有效的卡片格式，缺少type字段或type不是'card'");
        }
        
        // 获取卡片属性
        JsonElement properties;
        if (cardJson.TryGetProperty("properties", out var propsElement))
        {
            properties = propsElement;
        }
        else
        {
            // 对于老版本格式，可能直接使用根元素的属性
            properties = cardJson;
        }
        
        // 获取卡片名称
        string cardName;
        if (properties.TryGetProperty("name", out var nameElement))
        {
            cardName = nameElement.GetString();
        }
        else if (cardJson.TryGetProperty("name", out var rootNameElement))
        {
            cardName = rootNameElement.GetString();
        }
        else
        {
            cardName = $"导入卡片-{DateTime.Now:yyyyMMddHHmmss}";
        }
        
        // 创建新卡片
        var card = new form_card
        {
            id = Guid.NewGuid(),
            name = cardName,
            form_id = form.id,
            form = form,
            form_name = form.name,
            form_set_id = form.form_set_id,
            form_set_name = form.form_set_name,
            project_id = form.project_id,
            project_name = form.project_name,
            ward_id = form.ward_id,
            ward_name = form.ward_name,
            department_id = form.department_id,
            department_name = form.department_name,
            hospital_id = form.hospital_id,
            hospital_name = form.hospital_name
        };
        
        // 设置卡片类型和是否隐藏
        if (properties.TryGetProperty("type", out var cardTypeElement))
        {
            card.type = cardTypeElement.GetString();
        }
        else
        {
            card.type = "default"; // 默认类型
        }
        
        if (properties.TryGetProperty("is_hidden", out var hiddenElement))
        {
            card.is_hidden = hiddenElement.GetBoolean();
        }
        
        // 添加卡片到表单或父卡片
        if (parentCardId.HasValue)
        {
            var parentCard = await context.form_card.FindAsync(parentCardId.Value);
            card = await cardService.AddCardToParentCardAsync(context, parentCard, card);
        }
        else
        {
            card = await cardService.AddCardToRootAsync(context, form, card);
        }
        
        // 处理子项
        if (cardJson.TryGetProperty("items", out var itemsElement))
        {
            var items = itemsElement.EnumerateArray();
            foreach (var item in items)
            {
                string itemType = item.GetProperty("type").GetString();
                
                if (itemType == "card")
                {
                    // 递归处理子卡片
                    var (childCard, childQuestionCount) = await ImportCardAsync(context, form, card.id, item, cardService, columnDefinitionDict);
                    questionCount += childQuestionCount;
                }
                else if (itemType == "question")
                {
                    // 处理问题
                    var question = await ImportQuestionAsync(context, card, item, cardService, columnDefinitionDict);
                    if (question != null)
                    {
                        questionCount++;
                    }
                }
            }
        }
        
        return (card, questionCount);
    }
    
    // 导入单个问题
    private async Task<form_question> ImportQuestionAsync(
        CubeContext context, 
        form_card card, 
        JsonElement questionJson, 
        CardService cardService,
        Dictionary<Guid, column_definition> columnDefinitionDict)
    {
        var properties = questionJson.GetProperty("properties");
        
        // 获取column_definition_id
        var columnDefinitionId = new Guid(properties.GetProperty("column_definition_id").GetString());
        
        // 确保column_definition存在
        if (!columnDefinitionDict.TryGetValue(columnDefinitionId, out var columnDefinition))
        {
            throw new Exception($"找不到column_definition_id为{columnDefinitionId}的列定义");
        }
        
        // 添加问题
        var question = await cardService.AddQuestionToCardAsync(context, card, columnDefinition);
        
        // 更新问题的其他属性
        foreach (var property in properties.EnumerateObject())
        {
            var propertyName = property.Name;
            var propertyValue = property.Value;
            
            // 跳过已处理的属性
            if (propertyName == "column_definition_id")
                continue;
                
            var questionType = question.GetType();
            var propertyInfo = questionType.GetProperty(propertyName);
            
            if (propertyInfo != null && propertyInfo.CanWrite)
            {
                // 根据属性类型设置值
                if (propertyValue.ValueKind == JsonValueKind.String)
                {
                    var stringValue = propertyValue.GetString();
                    if (propertyInfo.PropertyType == typeof(Guid) || propertyInfo.PropertyType == typeof(Guid?))
                    {
                        if (Guid.TryParse(stringValue, out var guidValue))
                        {
                            propertyInfo.SetValue(question, guidValue);
                        }
                    }
                    else
                    {
                        propertyInfo.SetValue(question, stringValue);
                    }
                }
                else if (propertyValue.ValueKind == JsonValueKind.Number)
                {
                    if (propertyInfo.PropertyType == typeof(int) || propertyInfo.PropertyType == typeof(int?))
                    {
                        propertyInfo.SetValue(question, propertyValue.GetInt32());
                    }
                    else if (propertyInfo.PropertyType == typeof(short) || propertyInfo.PropertyType == typeof(short?))
                    {
                        propertyInfo.SetValue(question, (short)propertyValue.GetInt32());
                    }
                    else if (propertyInfo.PropertyType == typeof(double) || propertyInfo.PropertyType == typeof(double?))
                    {
                        propertyInfo.SetValue(question, propertyValue.GetDouble());
                    }
                    else if (propertyInfo.PropertyType == typeof(decimal) || propertyInfo.PropertyType == typeof(decimal?))
                    {
                        propertyInfo.SetValue(question, propertyValue.GetDecimal());
                    }
                }
                else if (propertyValue.ValueKind == JsonValueKind.True || propertyValue.ValueKind == JsonValueKind.False)
                {
                    propertyInfo.SetValue(question, propertyValue.GetBoolean());
                }
                else if (propertyValue.ValueKind == JsonValueKind.Array)
                {
                    // 处理数组类型，主要是List<string>
                    if (propertyInfo.PropertyType.IsGenericType && 
                        propertyInfo.PropertyType.GetGenericTypeDefinition() == typeof(List<>) &&
                        propertyInfo.PropertyType.GetGenericArguments()[0] == typeof(string))
                    {
                        var stringList = new List<string>();
                        foreach (var item in propertyValue.EnumerateArray())
                        {
                            if (item.ValueKind == JsonValueKind.String)
                            {
                                stringList.Add(item.GetString());
                            }
                        }
                        propertyInfo.SetValue(question, stringList);
                    }
                }
            }
        }
        
        // 更新问题
        context.form_question.Update(question);
        
        return question;
    }
}

/// <summary>
/// 导入结果类
/// </summary>
public class ImportResult
{
    /// <summary>
    /// 导入是否成功
    /// </summary>
    public bool Success { get; set; }
    
    /// <summary>
    /// 结果消息
    /// </summary>
    public string Message { get; set; }
    
    /// <summary>
    /// 导入的卡片列表
    /// </summary>
    public List<form_card> ImportedCards { get; set; } = new List<form_card>();
    
    /// <summary>
    /// 导入的问题数量
    /// </summary>
    public int ImportedQuestionCount { get; set; }
} 