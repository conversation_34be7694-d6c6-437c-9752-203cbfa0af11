<?xml version="1.0" encoding="utf-8"?>
<configuration>
	<packageSources>
		<!-- 默认的公共源 -->
		<add key="nuget.org" value="https://api.nuget.org/v3/index.json" protocolVersion="3" />
		<add key="BioNuGet" value="http://222.128.24.148:5555/v3/index.json" allowInsecureConnections="true" />
	</packageSources>

	<!-- 确保启用了所有源 -->
	<activePackageSource>
		<add key="All" value="(Aggregate source)" />
	</activePackageSource>

	<!-- 如果你的本地NuGet服务器需要凭证，可以在这里添加 -->
	<!--
  <packageSourceCredentials>
    <MyLocalNuGetFeed>
      <add key="Username" value="your_username" />
      <add key="ClearTextPassword" value="your_password" />
    </MyLocalNuGetFeed>
  </packageSourceCredentials>
  -->
</configuration>
