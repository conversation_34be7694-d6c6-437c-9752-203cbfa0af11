﻿@implements IAsyncDisposable

@code {
    [Inject]
    protected IDbContextFactory<CubeContext> ContextFactory { get; set; } = default!;

    protected CubeContext? context { get; private set; }

    protected override async Task OnInitializedAsync()
    {
        context = await ContextFactory.CreateDbContextAsync();
        await OnPageInitializedAsync();
        await base.OnInitializedAsync();
    }

    // 提供一个虚方法供子类重写，实现自己的初始化逻辑
    protected virtual Task OnPageInitializedAsync() => Task.CompletedTask;

    public virtual async ValueTask DisposeAsync()
    {
        if (context != null)
        {
            await context.DisposeAsync();
        }
        await ValueTask.CompletedTask;
    }

    /// <summary>
    /// 通用查询方法
    /// 使用动态 LINQ 优化过滤和排序逻辑
    /// </summary>
    protected async Task<QueryData<T>> OnQueryAsync<T>(
        List<T>? contextList, 
        QueryPageOptions options, 
        string sortField, 
        Func<T, string> sortSelector) where T : class
    {
        IEnumerable<T> items = contextList ?? Enumerable.Empty<T>();

        // 处理过滤
        bool isFiltered = false;
        if (options.Filters.Any())
        {
            // 使用动态 LINQ 处理过滤
            var filterPredicate = options.Filters.GetFilterFunc<T>();
            items = items.Where(filterPredicate);
            isFiltered = true;
        }

        // 处理排序
        bool isSorted = false;
        if (!string.IsNullOrEmpty(options.SortName))
        {
            if (options.SortName == sortField)
            {
                items = options.SortOrder == SortOrder.Asc
                    ? items.OrderBy(sortSelector)
                    : items.OrderByDescending(sortSelector);
                isSorted = true;
            }
            else
            {
                items = items.Sort(options.SortName, options.SortOrder);
                isSorted = true;
            }
        }

        return await Task.FromResult(new QueryData<T>()
        {
            Items = items,
            IsSorted = isSorted,
            IsFiltered = isFiltered
        });
    }
}