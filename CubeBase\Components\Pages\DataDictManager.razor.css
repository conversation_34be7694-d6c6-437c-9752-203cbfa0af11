.data-dict-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
}
.data-dict-table th {
    background-color: #2f756c;
    color: white;
    padding: 10px;
    text-align: left;
    position: sticky;
    top: 0;
    z-index: 1;
}
.data-dict-table td {
    border-bottom: 1px solid #e0e0e0;
    vertical-align: top;
}
.data-dict-table tr:hover {
    background-color: rgba(0, 0, 0, 0.04);
}
.data-dict-table .id-column {
    width: 120px;
}
.data-dict-table .name-column {
    font-family: Consolas, "Courier New", monospace;
    width: 350px;
    white-space: normal;
    word-wrap: break-word;
}
.data-dict-table .attr-column {
    max-width: 200px;
    word-break: break-word;
}
.attr-name {
    font-weight: bold;
}
.data-dict-table .type-column {
    width: 100px;
    font-weight: 500;
}
.data-dict-table .match-result-column {
    width: 350px;
    word-break: break-word;
}
/* 匹配结果样式 */
.match-name-line {
    margin-bottom: 2px;
    font-weight: 500;
    color: #2e7d32;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
}

.suggest-name-line {
    margin-bottom: 2px;
    font-weight: 500;
    color: #0277bd; /* 蓝色 */
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
}

.require-name-line {
    margin-bottom: 2px;
    font-weight: bold;
    color: #d32f2f; /* 红色 */
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
}

.match-type-line {
    margin-bottom: 2px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.match-type {
    font-weight: bold;
    color: #1976d2;
    font-size: 13px;
}

.suggest-type {
    font-weight: bold;
    color: #0277bd; /* 蓝色 */
    font-size: 13px;
}

.require-type {
    font-weight: bold;
    color: #d32f2f; /* 红色 */
    font-size: 13px;
}

.node-id {
    color: #555;
    font-family: Consolas, "Courier New", monospace;
    font-size: 12px;
}

.target {
    color: #424242;
    font-size: 13px;
    margin-bottom: 2px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
}

.suggest-target {
    color: #0277bd; /* 蓝色 */
    font-size: 13px;
    margin-bottom: 2px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
}

.suggest-alt-line {
    color: #0277bd; /* 蓝色 */
    font-size: 13px;
    font-style: italic;
    margin-bottom: 2px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
}

.require-target {
    color: #d32f2f; /* 红色 */
    font-weight: bold;
    font-size: 13px;
    margin-bottom: 2px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
}

.alternate-target {
    color: #424242;
    font-size: 13px;
    font-style: italic;
    margin-bottom: 2px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
}
.data-dict-container {
    max-height: calc(100vh - 400px);
    overflow: auto;
    box-shadow: inset 0 -5px 5px -5px rgba(0,0,0,0.1);
    border-radius: 4px;
}

::deep .attr-name {
    font-weight: bold;
}