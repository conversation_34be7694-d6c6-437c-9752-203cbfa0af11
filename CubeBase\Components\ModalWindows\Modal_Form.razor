﻿@inherits Modal_Base

<MudDialog>
    <TitleContent>
        <MudText Typo="Typo.h6">@MudDialog.Title</MudText>
    </TitleContent>
    <DialogContent>
        <MudForm @ref="_pageUI_mudForm" @bind-IsValid="@_success" @bind-Errors="@_errors">
            <MudStack Orientation="Orientation.Vertical" Spacing="2">
                @if (ExistingForm != null) {
                    <MudTextField T="string" Label="表单ID" Value="@ExistingForm.id.ToString()" ReadOnly="true"/>
                }
                <MudTextField T="string" Label="医院名称" Value="@FormSet.hospital_name" Disabled />
                <MudTextField T="string" Label="科室名称" Value="@FormSet.department_name" Disabled />
                <MudTextField T="string" Label="病区名称" Value="@FormSet.ward_name" Disabled />
                <MudTextField T="string" Label="项目名称" Value="@FormSet.project_name" Disabled />
                <MudTextField T="string" Label="表单集名称" Value="@FormSet.name" Disabled />
                <MudTextField T="string" Label="表单名称" Required="true" RequiredError="表单名称不能为空" @bind-Value="_formName" />
                <MudTextField T="string" Label="表单描述" @bind-Value="_formDescription" />
            </MudStack>
        </MudForm>
    </DialogContent>
    <DialogActions>
        <MudButton Variant="Variant.Filled" OnClick="Cancel">取消</MudButton>
        <MudButton Variant="Variant.Filled" Color="Color.Primary" OnClick="@(async () => await Submit())">保存</MudButton>
    </DialogActions>
</MudDialog>

@code {
    [CascadingParameter]
    private IMudDialogInstance MudDialog { get; set; }
    [Parameter]
    public form_form_set? FormSet { get; set; }
    [Parameter]
    public form_form? ExistingForm { get; set; }
    [Parameter]
    public EventCallback<form_form> OnFormModalClosed { get; set; }
    private MudForm? _pageUI_mudForm;
    private bool _success = false;
    private string[] _errors = [];
    private string _formName = "";
    private string _formDescription = "";

    protected override void OnInitialized()
    {
        if (ExistingForm != null)
        {
            _formName = ExistingForm.name;
            _formDescription = ExistingForm.group_name;
        }
    }

    private void Cancel() => MudDialog.Cancel();

    private async Task Submit()
    {
        await _pageUI_mudForm.Validate();

        if (_pageUI_mudForm.IsValid)
        {
            form_form resultForm;
            
            if (ExistingForm != null)
            {
                ExistingForm.name = _formName;
                ExistingForm.group_name = _formDescription;
                resultForm = ExistingForm;
            }
            else
            {
                resultForm = new form_form
                {
                    id = SequentialGuidGenerator.NewGuid(),
                    name = _formName,
                    group_name = _formDescription,
                    form_set_id = FormSet.id,
                    form_set_name = FormSet.name,
                    project_id = FormSet.project_id,
                    project_name = FormSet.project_name,
                    hospital_id = FormSet.hospital_id,
                    hospital_name = FormSet.hospital_name,
                    department_id = FormSet.department_id,
                    department_name = FormSet.department_name,
                    ward_id = FormSet.ward_id,
                    ward_name = FormSet.ward_name
                };
            }

            if (OnFormModalClosed.HasDelegate)
            {
                await OnFormModalClosed.InvokeAsync(resultForm);
            }

            MudDialog.Close(MudBlazor.DialogResult.Ok(true));
        }
    }
}