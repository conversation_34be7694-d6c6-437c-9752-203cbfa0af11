﻿.tree-view-container {
    height: 100vh;
    padding: 16px;
    overflow: auto; /* 添加滚动条以处理内容溢出 */
}
.form-container {
    padding: 16px;
}
.sticky-container {
    position: relative;
    height: 100%;
}

.sticky-form {
    position: sticky;
    top: 16px; /* 距离顶部的距离 */
    max-height: calc(100vh - 32px); /* 视口高度减去上下边距 */
    overflow-y: auto; /* 内容过多时显示滚动条 */
}

.right-panel {
    position: relative;
}

.right-panel-content {
    position: fixed;
    /*top: 64px;*/ /* MudBlazor AppBar 的默认高度 */
    right: 0;
    width: 25%;
    height: 100vh; /* 减去 AppBar 高度 */
    padding: 16px;
    background-color: var(--mud-palette-background);
    z-index: 1000;
    box-shadow: var(--mud-elevation-4);
    transition: all 0.3s ease-in-out;
}

.form-wrapper {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.scrollable-form {
    flex: 1;
    overflow-y: auto;
    padding-right: 16px;
    padding-bottom: 16px;
    max-height: calc(100vh - 144px); /* 减去 AppBar 高度和其他间距 */
}

/* 确保左侧内容不被固定定位的右侧面板遮挡 */
.mud-grid > .mud-grid-item:not(.right-panel) {
    margin-right: 25% !important; /* 使用 !important 确保样式优先级 */
}

/* 适配移动设备 */
@media (max-width: 600px) {
    .right-panel-content {
        width: 100%;
        height: 50vh;
        bottom: 0;
        top: auto;
    }

    .mud-grid > .mud-grid-item:not(.right-panel) {
        margin-right: 0 !important;
        margin-bottom: 50vh;
    }
}