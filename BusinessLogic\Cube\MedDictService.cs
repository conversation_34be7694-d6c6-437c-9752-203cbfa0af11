using System.Text;
using DataAccess.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;
using System.Threading;

namespace BusinessLogic.Cube;

/// <summary>
/// 医学数据字典服务实现类
/// </summary>
public partial class MedDictService : IMedDictService
{
    private readonly ILogger<MedDictService> _logger;
    private readonly IMemoryCache _cache;
    private static readonly ConcurrentDictionary<Guid, string> _pathCache = new();
    private static readonly object _cacheLock = new object();

    // 新增：全局数据缓存
    private const string GLOBAL_TABLE_DEFINITIONS_CACHE_KEY = "global_table_definitions_all";
    private static readonly SemaphoreSlim _globalDataCacheSemaphore = new(1, 1);

    public MedDictService(ILogger<MedDictService> logger, IMemoryCache cache)
    {
        _logger = logger;
        _cache = cache;
    }

    #region 强类型节点模型

    /// <summary>
    /// 节点基类（表或视图节点）
    /// </summary>
    private class TreeNode
    {
        public Guid Id { get; set; }
        public string UniqueName { get; set; }
        public string DisplayName { get; set; }
        public string NodeType { get; set; } // "Table" 或 "Visual"
        public string FullDisplayPath { get; set; }
        public List<TreeNode> Children { get; set; } = new();
    }

    /// <summary>
    /// 表节点
    /// </summary>
    private class TableNode : TreeNode
    {
        public List<ColumnNode> Columns { get; set; } = new();
    }

    /// <summary>
    /// 列节点
    /// </summary>
    private class ColumnNode
    {
        public Guid Id { get; set; }
        public string UniqueName { get; set; }
        public string DisplayName { get; set; }
        public string DataType { get; set; }
        public string NoteCn { get; set; }
        public List<string> Options { get; set; } // 新增：用于存储选择类型的选项
    }

    #endregion

    #region 全局数据缓存方法

    /// <summary>
    /// 获取所有有效的 table_definition 数据，使用全局缓存
    /// </summary>
    /// <param name="context">数据库上下文</param>
    /// <returns>所有有效的 table_definition 字典</returns>
    public async Task<Dictionary<Guid, table_definition>> GetAllTableDefinitionsAsync(CubeContext context)
    {
        // 先检查缓存
        if (_cache.TryGetValue(GLOBAL_TABLE_DEFINITIONS_CACHE_KEY, out Dictionary<Guid, table_definition> cachedData))
        {
            return cachedData;
        }

        // 使用信号量确保只有一个线程执行数据库查询
        await _globalDataCacheSemaphore.WaitAsync();
        try
        {
            // 双重检查，防止其他线程已经加载了数据
            if (_cache.TryGetValue(GLOBAL_TABLE_DEFINITIONS_CACHE_KEY, out cachedData))
            {
                return cachedData;
            }

            var stopwatch = System.Diagnostics.Stopwatch.StartNew();
            _logger.LogWarning("[性能调试] GetAllTableDefinitionsAsync 开始全局数据加载");

            // 执行数据库查询
            var allTableDefinitions = await context.table_definition
                .AsNoTracking()
                .Where(t => t.is_valid)
                .ToListAsync();

            var nodesById = allTableDefinitions.ToDictionary(n => n.id);

            stopwatch.Stop();
            _logger.LogWarning("[性能调试] GetAllTableDefinitionsAsync 完成全局数据加载 - 耗时: {ElapsedMs}ms, 节点数: {NodeCount}", 
                stopwatch.ElapsedMilliseconds, allTableDefinitions.Count);

            // 缓存30分钟（增加缓存时间，因为 table_definition 更改不频繁）
            _cache.Set(GLOBAL_TABLE_DEFINITIONS_CACHE_KEY, nodesById, TimeSpan.FromMinutes(30));

            return nodesById;
        }
        finally
        {
            _globalDataCacheSemaphore.Release();
        }
    }

    /// <summary>
    /// 清除全局数据缓存和路径缓存
    /// 当 table_definition 数据发生变化时应调用此方法
    /// </summary>
    public void ClearAllCaches()
    {
        _logger.LogWarning("[缓存管理] 开始清除所有MedDictService缓存");
        
        // 清除全局数据缓存
        _cache.Remove(GLOBAL_TABLE_DEFINITIONS_CACHE_KEY);
        
        // 清除路径缓存
        var cacheField = _cache.GetType().GetField("_cache", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        if (cacheField?.GetValue(_cache) is System.Collections.IDictionary cache)
        {
            var keysToRemove = new List<object>();
            foreach (var key in cache.Keys)
            {
                if (key?.ToString()?.StartsWith("column_path_") == true)
                {
                    keysToRemove.Add(key);
                }
            }
            
            foreach (var key in keysToRemove)
            {
                _cache.Remove(key);
            }
            
            _logger.LogWarning("[缓存管理] 清除了 {PathCacheCount} 个路径缓存项", keysToRemove.Count);
        }
        
        // 清除静态路径缓存
        _pathCache.Clear();
        
        _logger.LogWarning("[缓存管理] 所有MedDictService缓存已清除");
    }

    #endregion

    #region 公共导出方法

    /// <summary>
    /// 导出医学数据字典为文本格式
    /// </summary>
    /// <param name="context">数据库上下文</param>
    /// <returns>包含医学数据字典的文本字符串</returns>
    public async Task<string> ExportMedicalDataDictionaryAsync(CubeContext context)
    {
        return await ExportDataDictionaryAsync(
            context: context,
            startNodeId: null,
            includeColumns: true,
            includeVisualNodes: false,
            includeNodeIdInColumn: false,
            formatDescriptionAction: AppendTableFormatDescription);
    }

    /// <summary>
    /// 导出所有有效节点（Table和Visual）的数据字典为文本格式，不包含列信息
    /// </summary>
    /// <param name="context">数据库上下文</param>
    /// <returns>包含所有节点数据字典的文本字符串</returns>
    public async Task<string> ExportAllNodesDataDictionaryAsync(CubeContext context)
    {
        return await ExportDataDictionaryAsync(
            context: context,
            startNodeId: null,
            includeColumns: false,
            includeVisualNodes: true,
            includeNodeIdInColumn: false,
            formatDescriptionAction: AppendTableFormatDescription);
    }

    /// <summary>
    /// 从指定节点开始导出子树数据字典为文本格式
    /// </summary>
    /// <param name="context">数据库上下文</param>
    /// <param name="startNodeId">起始节点ID</param>
    /// <returns>包含指定子树数据字典的文本字符串</returns>
    public async Task<string> ExportSubtreeDataDictionaryAsync(CubeContext context, Guid startNodeId)
    {
        return await ExportDataDictionaryAsync(
            context: context,
            startNodeId: startNodeId,
            includeColumns: true,
            includeVisualNodes: false,
            includeNodeIdInColumn: true,
            formatDescriptionAction: AppendTableFormatDescription);
    }

    /// <summary>
    /// 核心导出方法，统一处理所有导出逻辑
    /// </summary>
    /// <param name="context">数据库上下文</param>
    /// <param name="startNodeId">可选的起始节点ID，如果提供，则只导出该节点的子树</param>
    /// <param name="includeColumns">是否包含列信息</param>
    /// <param name="includeVisualNodes">是否包含Visual节点</param>
    /// <param name="includeNodeIdInColumn">是否在列信息中包含节点ID</param>
    /// <param name="formatDescriptionAction">格式说明添加方法</param>
    /// <returns>包含数据字典的文本字符串</returns>
    private async Task<string> ExportDataDictionaryAsync(
        CubeContext context,
        Guid? startNodeId,
        bool includeColumns,
        bool includeVisualNodes,
        bool includeNodeIdInColumn,
        Action<StringBuilder> formatDescriptionAction)
    {
        var resultBuilder = new StringBuilder();

        try
        {
            List<Guid> relevantNodeIds = null;

            // 如果指定了起始节点，则需要验证节点并获取子树节点ID
            if (startNodeId.HasValue)
            {
                // 检查起始节点是否存在且有效
                var startNode = await context.table_definition
                    .FirstOrDefaultAsync(t => t.id == startNodeId.Value && t.is_valid);

                if (startNode == null)
                {
                    return $"错误: 未找到有效的起始节点 ID: {startNodeId}";
                }

                // 获取并添加起始节点的完整路径
                var fullPath = await GetColumnNodePathAsync(context, startNodeId.Value);
                resultBuilder.AppendLine(fullPath);
                // 添加一个空行以分隔路径和树内容，如果需要的话
                // resultBuilder.AppendLine(); 

                // 获取子树相关的节点ID (Convert HashSet to List)
                var descendantIds = await GetAllDescendantIdsAsync(context, startNodeId.Value);
                relevantNodeIds = descendantIds.ToList();
                // Also need to add the start node itself to the list for the export logic
                relevantNodeIds.Insert(0, startNodeId.Value);
            }

            // 获取节点和列数据
            var (allNodes, columnsByTableId) = await FetchNodesAndColumnsAsync(context, relevantNodeIds, includeColumns);

            // 构建树结构
            List<TreeNode> tree;

            if (startNodeId.HasValue)
            {
                // 构建子树
                tree = BuildSubtreeStructure(startNodeId.Value, allNodes, columnsByTableId, filterVisualNodes: !includeVisualNodes);
            }
            else
            {
                // 构建完整树，并直接在构建过程中处理Visual节点过滤
                tree = BuildTreeStructure(
                    allNodes,
                    columnsByTableId,
                    includeOnlyTableNodes: false,
                    filterVisualNodes: !includeVisualNodes);
            }

            // 生成文本
            var textLines = FormatTreeToText(tree, includeColumns, includeNodeIdInColumn);
            foreach (var line in textLines)
            {
                resultBuilder.AppendLine(line);
            }

            // 添加文件格式说明
            formatDescriptionAction(resultBuilder);
        }
        catch (Exception ex)
        {
            resultBuilder.AppendLine($"导出过程中发生错误: {ex.Message}");
        }

        return resultBuilder.ToString();
    }

    #endregion

    #region 数据获取方法

    /// <summary>
    /// 获取所有有效节点和可选的列信息
    /// </summary>
    /// <param name="context">数据库上下文</param>
    /// <param name="nodeIds">可选的节点ID列表，如果提供，则只获取这些节点</param>
    /// <param name="includeColumns">是否包含列信息</param>
    /// <returns>包含节点和可选列信息的元组</returns>
    private async Task<(Dictionary<Guid, table_definition> allNodes, Dictionary<Guid, List<column_definition>> columnsByTableId)>
        FetchNodesAndColumnsAsync(CubeContext context, List<Guid> nodeIds = null, bool includeColumns = true)
    {
        // 使用全局缓存获取节点数据
        Dictionary<Guid, table_definition> allNodes;
        
        if (nodeIds != null && nodeIds.Any())
        {
            // 如果指定了节点ID列表，则过滤数据
            var allNodesData = await GetAllTableDefinitionsAsync(context);
            allNodes = allNodesData.Where(kvp => nodeIds.Contains(kvp.Key)).ToDictionary(kvp => kvp.Key, kvp => kvp.Value);
        }
        else
        {
            // 使用全部数据
            allNodes = await GetAllTableDefinitionsAsync(context);
        }

        // 初始化一个空的列字典
        var columnsByTableId = new Dictionary<Guid, List<column_definition>>();

        // 如果需要包含列信息，则获取列定义
        if (includeColumns)
        {
            // 获取表节点的ID列表
            var tableNodeIds = allNodes.Values
                .Where(n => n.node_type == "Table")
                .Select(n => n.id)
                .ToList();

            // 获取有效的列定义
            IQueryable<column_definition> columnsQuery = context.column_definition.Where(c => c.is_valid);

            // 如果指定了表节点ID列表，则只获取这些表的列
            if (tableNodeIds.Any())
            {
                columnsQuery = columnsQuery.Where(c => tableNodeIds.Contains(c.table_id));
            }

            var columns = await columnsQuery.ToListAsync();

            // 按 table_id 对列进行分组
            columnsByTableId = columns
                .GroupBy(c => c.table_id)
                .ToDictionary(g => g.Key, g => g.ToList());
        }

        return (allNodes, columnsByTableId);
    }

    /// <summary>
    /// 获取用于构建树的所有有效 table_definition 及其有效的 column_definition。
    /// </summary>
    /// <param name="context">数据库上下文</param>
    /// <returns>包含有效表和列的列表</returns>
    public async Task<List<table_definition>> GetValidTableTreeAsync(CubeContext context)
    {
        if (context == null)
            throw new ArgumentNullException(nameof(context));

        // Mirrors the logic previously in CubeManager.RefreshTree
        return await context.table_definition
            .AsNoTracking() // Use AsNoTracking for read-only operations
            .Include(table => table.column_definition
                .Where(c => c.is_valid == true)
                .OrderBy(cd => cd.sorted_index)) // Ensure columns are ordered
            .Where(table => table.is_valid == true)
            .OrderBy(td => td.sorted_index) // Ensure tables are ordered
            .ToListAsync();
    }

    /// <summary>
    /// 获取指定节点的所有后代节点的 ID 集合 (不包含自身)。
    /// </summary>
    /// <param name="context">数据库上下文</param>
    /// <param name="nodeId">起始节点的 ID</param>
    /// <returns>包含所有后代节点 ID 的 HashSet</returns>
    /// <exception cref="ArgumentNullException">当 context 为空时抛出</exception>
    /// <exception cref="ArgumentException">当 nodeId 无效时抛出</exception>
    public async Task<HashSet<Guid>> GetAllDescendantIdsAsync(CubeContext context, Guid nodeId)
    {
        if (context == null)
            throw new ArgumentNullException(nameof(context));
        if (nodeId == Guid.Empty)
            throw new ArgumentException("无效的节点 ID", nameof(nodeId));

        var descendantIds = new HashSet<Guid>();
        var queue = new Queue<Guid>();

        // Start BFS from the direct children of the initial node
        var initialChildren = await context.table_definition
                                    .Where(t => t.parent_node_id == nodeId && t.is_valid)
                                    .Select(t => t.id)
                                    .ToListAsync();

        foreach(var childId in initialChildren)
        {
            if (descendantIds.Add(childId)) // Add direct children
            {
                queue.Enqueue(childId); // Enqueue direct children to find their descendants
            }
        }

        // Pre-fetch all relevant parent-child relations for efficiency
        var allValidTableRelations = await context.table_definition
                                     .Where(t => t.is_valid)
                                     .Select(t => new { t.id, t.parent_node_id })
                                     .ToListAsync();

        var childrenMap = allValidTableRelations
                          .GroupBy(t => t.parent_node_id)
                          .Where(g => g.Key != Guid.Empty) // Exclude root nodes if parent_node_id is nullable Guid? or handle Guid.Empty
                          .ToDictionary(g => g.Key, g => g.Select(t => t.id).ToList());

        // Continue BFS for descendants of the direct children
        while (queue.Count > 0)
        {
            var currentId = queue.Dequeue();

            if (childrenMap.TryGetValue(currentId, out var children))
            {
                foreach (var childId in children)
                {
                    if (descendantIds.Add(childId)) // Add descendant and check if it was already added
                    {
                        queue.Enqueue(childId);
                    }
                }
            }
        }

        return descendantIds;
    }

    #endregion

    #region 树构建方法

    /// <summary>
    /// 构建树结构
    /// </summary>
    /// <param name="nodesById">节点字典</param>
    /// <param name="columnsByTableId">列数据字典</param>
    /// <param name="includeOnlyTableNodes">是否只包含Table节点</param>
    /// <param name="filterVisualNodes">是否过滤Visual节点（提升其子节点）</param>
    /// <returns>构建的树结构</returns>
    private List<TreeNode> BuildTreeStructure(
        Dictionary<Guid, table_definition> nodesById,
        Dictionary<Guid, List<column_definition>> columnsByTableId = null,
        bool includeOnlyTableNodes = false,
        bool filterVisualNodes = false)
    {
        var result = new List<TreeNode>();
        var nodeMap = new Dictionary<Guid, TreeNode>();

        // 第一步：创建所有节点对象
        foreach (var nodeEntry in nodesById)
        {
            var node = nodeEntry.Value;

            // 如果只包含Table节点，且当前节点不是Table，则跳过
            if (includeOnlyTableNodes && node.node_type != "Table")
                continue;

            TreeNode treeNode;

            // 为Table节点创建TableNode，为其他节点创建普通TreeNode
            if (node.node_type == "Table")
            {
                var tableNode = new TableNode
                {
                    Id = node.id,
                    UniqueName = node.unique_name,
                    DisplayName = node.display_name,
                    NodeType = node.node_type,
                    FullDisplayPath = GetFullDisplayPath(node.id, nodesById)
                };

                // 如果提供了列信息，则添加到TableNode
                if (columnsByTableId != null && columnsByTableId.TryGetValue(node.id, out var columns))
                {
                    foreach (var column in columns)
                    {
                        tableNode.Columns.Add(new ColumnNode
                        {
                            Id = column.id,
                            UniqueName = column.unique_name,
                            DisplayName = column.display_name,
                            DataType = column.data_type,
                            NoteCn = column.note_cn,
                            // 如果数据类型为"选择"，则填充Options
                            Options = column.data_type == "选择" ? column.option_set : null
                        });
                    }
                }

                treeNode = tableNode;
            }
            else
            {
                treeNode = new TreeNode
                {
                    Id = node.id,
                    UniqueName = node.unique_name,
                    DisplayName = node.display_name,
                    NodeType = node.node_type,
                    FullDisplayPath = GetFullDisplayPath(node.id, nodesById)
                };
            }

            nodeMap[node.id] = treeNode;
        }

        // 第二步：构建父子关系
        foreach (var nodeEntry in nodesById)
        {
            var node = nodeEntry.Value;

            // 如果只包含Table节点，且当前节点不是Table，则跳过
            if (includeOnlyTableNodes && node.node_type != "Table")
                continue;

            // 如果节点不在nodeMap中，说明已被过滤掉，跳过
            if (!nodeMap.TryGetValue(node.id, out var treeNode))
                continue;

            // 如果有父节点且父节点在节点映射中，则添加到父节点的子节点列表
            if (node.parent_node_id != Guid.Empty && nodeMap.TryGetValue(node.parent_node_id, out var parentNode))
            {
                parentNode.Children.Add(treeNode);
            }
            // 否则作为根节点
            else
            {
                result.Add(treeNode);
            }
        }

        // 如果需要过滤Visual节点，则执行过滤
        if (filterVisualNodes)
        {
            result = FilterVisualNodes(result);
        }

        return result;
    }

    /// <summary>
    /// 构建子树结构
    /// </summary>
    /// <param name="startNodeId">起始节点ID</param>
    /// <param name="nodesById">节点字典</param>
    /// <param name="columnsByTableId">列数据字典</param>
    /// <param name="filterVisualNodes">是否过滤Visual节点</param>
    /// <returns>构建的子树结构</returns>
    private List<TreeNode> BuildSubtreeStructure(
        Guid startNodeId,
        Dictionary<Guid, table_definition> nodesById,
        Dictionary<Guid, List<column_definition>> columnsByTableId,
        bool filterVisualNodes = false)
    {
        // 检查起始节点是否存在
        if (!nodesById.TryGetValue(startNodeId, out _))
        {
            return new List<TreeNode>();
        }

        // 构建完整树结构
        var fullTree = BuildTreeStructure(nodesById, columnsByTableId, includeOnlyTableNodes: false);

        // 查找起始节点
        var startNode = FindNodeById(fullTree, startNodeId);

        if (startNode != null)
        {
            var result = new List<TreeNode> { startNode };

            // 如需过滤Visual节点，则执行过滤
            if (filterVisualNodes)
            {
                result = FilterVisualNodes(result);
            }

            return result;
        }

        return new List<TreeNode>();
    }

    /// <summary>
    /// 在树中查找指定ID的节点
    /// </summary>
    private TreeNode FindNodeById(List<TreeNode> tree, Guid nodeId)
    {
        foreach (var node in tree)
        {
            if (node.Id == nodeId)
                return node;

            // 递归查找子节点
            var foundInChildren = FindNodeById(node.Children, nodeId);
            if (foundInChildren != null)
                return foundInChildren;
        }

        return null;
    }

    /// <summary>
    /// 递归构建表的完整显示路径
    /// </summary>
    private string GetFullDisplayPath(Guid nodeId, Dictionary<Guid, table_definition> nodesById, List<string> pathParts = null)
    {
        pathParts ??= new List<string>();

        if (!nodesById.TryGetValue(nodeId, out var nodeData))
        {
            return "错误: 未找到路径段";
        }

        // 将当前节点的显示名称添加到路径开头
        pathParts.Insert(0, nodeData.display_name ?? $"未知(ID:{nodeId})");

        // 如果父节点ID存在且有效，则递归获取父路径
        if (nodeData.parent_node_id != Guid.Empty && nodesById.ContainsKey(nodeData.parent_node_id))
        {
            return GetFullDisplayPath(nodeData.parent_node_id, nodesById, pathParts);
        }
        else
        {
            // 到达根节点或父节点无效，返回完整路径字符串
            return string.Join(" > ", pathParts);
        }
    }

    /// <summary>
    /// 递归过滤掉Visual节点，提升其Table子节点
    /// </summary>
    private List<TreeNode> FilterVisualNodes(List<TreeNode> nodes)
    {
        var filteredList = new List<TreeNode>();

        foreach (var node in nodes)
        {
            if (node.NodeType == "Table")
            {
                // 保留 'Table' 节点，递归处理其子节点
                var filteredChildren = FilterVisualNodes(node.Children);
                node.Children = filteredChildren;
                filteredList.Add(node);
            }
            else if (node.NodeType == "Visual")
            {
                // 丢弃 'Visual' 节点, 但处理其子节点
                var filteredChildren = FilterVisualNodes(node.Children);
                filteredList.AddRange(filteredChildren);
            }
        }

        return filteredList;
    }

    #endregion

    #region 文本格式化方法

    /// <summary>
    /// 格式化树为文本
    /// </summary>
    private List<string> FormatTreeToText(List<TreeNode> tree, bool includeColumns, bool includeNodeIdInColumn = false)
    {
        var lines = new List<string>();

        foreach (var node in tree)
        {
            lines.AddRange(FormatNodeToText(node, 0, includeColumns, includeNodeIdInColumn));
        }

        return lines;
    }

    /// <summary>
    /// 递归地将节点格式化为基于缩进的文本行
    /// </summary>
    private List<string> FormatNodeToText(TreeNode node, int indentLevel, bool includeColumns, bool includeNodeIdInColumn = false)
    {
        var lines = new List<string>();
        var indent = new string(' ', indentLevel * 4); // 4个空格作为一级缩进
        const string separator = "|";

        // 根据节点类型添加不同的前缀
        var prefix = node.NodeType == "Table" ? $"T:({node.Id})" : "V:";
        var nodeLine = $"{indent}{prefix}{node.UniqueName}{separator}{node.DisplayName}{separator}{node.FullDisplayPath}";
        lines.Add(nodeLine);

        // 如果是TableNode且需要包含列信息，则添加列信息
        if (node.NodeType == "Table" && includeColumns && node is TableNode tableNode)
        {
            var columnIndent = new string(' ', (indentLevel + 1) * 4);
            foreach (var column in tableNode.Columns)
            {
                // 在C:后面添加列ID（如果需要）
                var columnPrefix = includeNodeIdInColumn ? $"C:({column.Id})" : "C:";
                var columnBasicInfo = $"{columnIndent}{columnPrefix}{column.UniqueName}{separator}{column.DisplayName}{separator}{column.DataType}{separator}{column.NoteCn ?? string.Empty}";

                // 如果数据类型为"选择"且选项不为空，则添加选项信息
                if (column.DataType == "选择" && column.Options != null && column.Options.Any())
                {
                    var optionsString = string.Join("###", column.Options);
                    lines.Add($"{columnBasicInfo}{separator}{optionsString}");
                }
                else
                {
                    lines.Add(columnBasicInfo);
                }
            }
        }

        // 递归格式化子节点
        foreach (var childNode in node.Children)
        {
            lines.AddRange(FormatNodeToText(childNode, indentLevel + 1, includeColumns, includeNodeIdInColumn));
        }

        return lines;
    }

    /// <summary>
    /// 添加表格式文件说明
    /// </summary>
    private void AppendTableFormatDescription(StringBuilder builder)
    {
        builder.AppendLine();
        builder.AppendLine("--- 文件格式说明 ---");
        builder.AppendLine("# 结构: 基于缩进的层级 (每级4个空格)");
        builder.AppendLine("# 行前缀:");
        builder.AppendLine("#   T:(id) = 表 (Table)，括号中包含节点ID");
        builder.AppendLine("#   V: = 视图 (Visual)");
        builder.AppendLine("#   C:(id) = 列 (Column), 归属于上一级缩进更少的 T节点，括号中包含节点ID");
        builder.AppendLine("# 字段分隔符: | (竖线)");
        builder.AppendLine("# 表行 T:(id)<unique_name>|<display_name>|<full_display_path>");
        builder.AppendLine("#   - full_display_path: 根 > 父 > ... > 当前 (显示名称路径)");
        builder.AppendLine("# 列行 C:(id)<unique_name>|<display_name>|<data_type>|<alias>[|<options>]");
        builder.AppendLine("#   - options: (可选字段) 当 data_type 为 '选择' 时出现，内容为'###'分隔的选项列表。例如：选项A###选项B###选项C");
    }

    #endregion

    #region 路径构建方法
    /// <summary>
    /// 获取指定节点的从根节点开始的节点路径
    /// </summary>
    /// <param name="context">数据库上下文</param>
    /// <param name="nodeId">目标节点的ID</param>
    /// <returns>包含从根节点开始的节点路径的字符串 L0 > L1 > L2 > ... > Ln</returns>
    public async Task<string> GetColumnNodePathAsync(CubeContext context, Guid nodeId)
    {
        // 检查缓存
        var cacheKey = $"column_path_{nodeId}";
        if (_cache.TryGetValue(cacheKey, out string cachedPath))
        {
            return cachedPath;
        }

        // 注意：这里传入的 context 可能是短生命周期的，如果 GetColumnNodePathInternalAsync 
        // 内部也依赖此 context 进行多次查询，则没问题。
        // 如果 GetColumnNodePathInternalAsync 内部尝试使用一个长生命周期的 context，则可能出现问题。
        // 从当前代码看，Internal 方法也接受 context，所以还好。
        var path = await GetColumnNodePathInternalAsync(context, nodeId);
        
        // 缓存15分钟 (原为5分钟)
        // 考虑到 table_definition 数量不多，且路径查找可能频繁，可以适当延长缓存或在服务层面缓存所有节点的路径。
        _cache.Set(cacheKey, path, TimeSpan.FromMinutes(15)); 
        
        return path;
    }

    private async Task<string> GetColumnNodePathInternalAsync(CubeContext context, Guid nodeId)
    {
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();
        _logger.LogWarning("[性能调试] GetColumnNodePathInternalAsync V3 开始 - NodeID: {NodeID}", nodeId);

        if (nodeId == Guid.Empty)
        {
            stopwatch.Stop();
            _logger.LogWarning("[性能调试] GetColumnNodePathInternalAsync V3 无效NodeID - 耗时: {ElapsedMs}ms", stopwatch.ElapsedMilliseconds);
            return "错误: 无效的节点 ID";
        }

        var pathSegments = new List<string>();

        // 使用全局数据缓存获取所有节点数据
        var dataFetchStopwatch = System.Diagnostics.Stopwatch.StartNew();
        var nodesById = await GetAllTableDefinitionsAsync(context);
        dataFetchStopwatch.Stop();
        _logger.LogWarning("[性能调试] V3: 获取全局数据耗时: {ElapsedMs}ms (可能来自缓存)", dataFetchStopwatch.ElapsedMilliseconds);
        
        var currentId = nodeId;
        int iterations = 0; // 防止因数据问题导致的无限循环
        const int maxPathDepth = 50; // 假设正常的路径深度不会超过这个值

        while (currentId != Guid.Empty && iterations < maxPathDepth)
        {
            iterations++;
            if (nodesById.TryGetValue(currentId, out var currentNode))
            {
                pathSegments.Insert(0, currentNode.display_name ?? $"未知(ID:{currentId})");
                // 检查是否为根节点 (parent_node_id 为 Guid.Empty)
                if (currentNode.parent_node_id == Guid.Empty)
                {
                    break; 
                }
                currentId = currentNode.parent_node_id;
            }
            else
            {
                // 当前 currentId 在预加载的 nodesById 字典中未找到
                if (currentId == nodeId) // 如果是初始节点就没找到
                {
                    stopwatch.Stop();
                    _logger.LogWarning("[性能调试] GetColumnNodePathInternalAsync V3 未找到初始节点 {InitialNodeID} (在全局缓存的有效节点集合中) - 总耗时: {ElapsedMs}ms", 
                        nodeId, stopwatch.ElapsedMilliseconds);
                    return $"错误: 未找到有效的初始节点 ID: {nodeId}";
                }
                // 如果是父节点链中存在问题
                stopwatch.Stop();
                _logger.LogWarning("[性能调试] GetColumnNodePathInternalAsync V3 父节点链断裂于 {BrokenNodeID} (未在全局缓存的有效节点集合中找到) - 总耗时: {ElapsedMs}ms", 
                    currentId, stopwatch.ElapsedMilliseconds);
                 pathSegments.Insert(0, $"[父节点缺失或无效:{currentId}]"); // 标记路径中断
                break; 
            }
        }

        if (iterations >= maxPathDepth)
        {
            _logger.LogWarning("[性能调试] GetColumnNodePathInternalAsync V3 达到最大迭代次数 {MaxIterations}，可能存在循环父子关系或路径过深。NodeID: {NodeID}", 
                maxPathDepth, nodeId);
            // 可以选择返回错误或部分路径
        }

        if (!pathSegments.Any() || pathSegments.First().StartsWith("[")) // 如果没有路径段，或者第一个路径段是错误标记
        {
            stopwatch.Stop();
             // 确认初始节点是否真的在字典中，以便调试
            bool initialNodeExisted = nodesById.ContainsKey(nodeId);
            _logger.LogWarning("[性能调试] GetColumnNodePathInternalAsync V3 路径段为空或无效。初始NodeID: {NodeID}, 是否在字典中: {InitialNodeExisted} - 总耗时: {ElapsedMs}ms", 
                nodeId, initialNodeExisted, stopwatch.ElapsedMilliseconds);
            return $"错误: 未能为节点 ID 构建完整路径: {nodeId}";
        }

        var result = string.Join(" > ", pathSegments);
        stopwatch.Stop();
        _logger.LogWarning("[性能调试] GetColumnNodePathInternalAsync V3 完成 - 总耗时: {ElapsedMs}ms, 数据获取耗时: {DataFetchMs}ms, 路径深度: {PathDepth}, 结果: {Result}", 
            stopwatch.ElapsedMilliseconds, dataFetchStopwatch.ElapsedMilliseconds, pathSegments.Count, result);
        
        return result;
    }
    #endregion
}
