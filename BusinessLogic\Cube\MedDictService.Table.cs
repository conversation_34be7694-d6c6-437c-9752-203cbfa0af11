using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using DataAccess.Models;
using Infrastructure;
using Microsoft.EntityFrameworkCore;

namespace BusinessLogic.Cube;

public partial class MedDictService : IMedDictService
{
    #region 表管理方法

    /// <summary>
    /// 添加一个新的表定义到指定的节点中（但不保存更改）
    /// </summary>
    /// <param name="context">数据库上下文</param>
    /// <param name="newTableData">包含新表信息的对象</param>
    /// <returns>已添加到上下文的新创建的表定义实体</returns>
    /// <exception cref="ArgumentNullException">当 context 或 newTableData 为空时抛出</exception>
    /// <exception cref="ArgumentException">当输入验证失败时抛出</exception>
    /// <exception cref="InvalidOperationException">当父节点无效或验证失败时抛出</exception>
    public async Task<table_definition> AddTableDefinitionAsync(CubeContext context, table_definition newTableData)
    {
        if (context == null)
            throw new ArgumentNullException(nameof(context));
        if (newTableData == null)
            throw new ArgumentNullException(nameof(newTableData));

        // 规范化并验证唯一编码
        var uniqueName = (newTableData.unique_name ?? "").Trim().ToLower();
        if (string.IsNullOrWhiteSpace(uniqueName))
            throw new ArgumentException("表的唯一编码不能为空", nameof(newTableData.unique_name));
        if (uniqueName.Length > 60)
            throw new ArgumentException("表的唯一编码不能超过60个字符", nameof(newTableData.unique_name));
        
        // 验证显示名称
        if (string.IsNullOrWhiteSpace(newTableData.display_name))
            throw new ArgumentException("表的显示名称不能为空", nameof(newTableData.display_name));
            
        // 验证节点类型
        if (newTableData.node_type != "Table" && newTableData.node_type != "Visual")
            throw new ArgumentException("无效的节点类型，必须是 'Table' 或 'Visual'", nameof(newTableData.node_type));

        // 验证唯一编码是否已存在
        var duplicateExists = await context.table_definition
            .AnyAsync(t => t.is_valid && t.unique_name.ToLower() == uniqueName);
        if (duplicateExists)
            throw new ArgumentException($"表的唯一编码 '{uniqueName}' 已存在", nameof(newTableData.unique_name));

        // 验证父节点
        Guid? parentNodeId = newTableData.parent_node_id == Guid.Empty ? null : newTableData.parent_node_id;
        if (parentNodeId.HasValue)
        {
            var parentNode = await context.table_definition
                .FirstOrDefaultAsync(t => t.id == parentNodeId.Value && t.is_valid);

            if (parentNode == null)
                throw new InvalidOperationException($"指定的父节点 (ID: {parentNodeId.Value}) 不存在或无效");
                
            if (parentNode.node_type != "Visual")
                throw new InvalidOperationException($"指定的父节点 (ID: {parentNodeId.Value}, Name: {parentNode.display_name}) 不是 'Visual' 类型，不能添加子节点");
        }

        // 计算新节点的排序索引
        int maxSortedIndex = 0;
        var siblings = await context.table_definition
            .Where(t => t.parent_node_id == newTableData.parent_node_id && t.is_valid)
            .Select(t => t.sorted_index)
            .ToListAsync();
            
        if (siblings.Any())
        {
            maxSortedIndex = siblings.Max();
        }

        var createdTable = new table_definition
        {
            id = SequentialGuidGenerator.NewGuid(),
            unique_name = uniqueName,
            display_name = newTableData.display_name,
            node_type = newTableData.node_type,
            parent_node_id = newTableData.parent_node_id,
            sorted_index = maxSortedIndex + 1,
            is_valid = true,
            // 如果是 Visual 节点，column_definitions 应该是空的
            column_definition = newTableData.node_type == "Table" ? new List<column_definition>() : null 
        };

        context.table_definition.Add(createdTable);
        
        return createdTable;
    }

    /// <summary>
    /// 更新指定的表定义（但不保存更改）
    /// </summary>
    /// <param name="context">数据库上下文</param>
    /// <param name="tableData">包含更新表信息的对象</param>
    /// <exception cref="ArgumentNullException">当 context 或 tableData 为空时抛出</exception>
    /// <exception cref="ArgumentException">当输入验证失败时抛出</exception>
    /// <exception cref="InvalidOperationException">当节点不存在、父节点无效或验证失败时抛出</exception>
    public async Task UpdateTableDefinitionAsync(CubeContext context, table_definition tableData)
    {
        if (context == null)
            throw new ArgumentNullException(nameof(context));
        if (tableData == null)
            throw new ArgumentNullException(nameof(tableData));
        if (tableData.id == Guid.Empty)
             throw new ArgumentException("表数据必须包含有效的 id", nameof(tableData.id));

        // 规范化并验证唯一编码
        var uniqueName = (tableData.unique_name ?? "").Trim().ToLower();
        if (string.IsNullOrWhiteSpace(uniqueName))
            throw new ArgumentException("表的唯一编码不能为空", nameof(tableData.unique_name));
        if (uniqueName.Length > 60)
            throw new ArgumentException("表的唯一编码不能超过60个字符", nameof(tableData.unique_name));
            
        // 验证显示名称
        if (string.IsNullOrWhiteSpace(tableData.display_name))
            throw new ArgumentException("表的显示名称不能为空", nameof(tableData.display_name));
            
        // 验证节点类型
        if (tableData.node_type != "Table" && tableData.node_type != "Visual")
            throw new ArgumentException("无效的节点类型，必须是 'Table' 或 'Visual'", nameof(tableData.node_type));

        // 验证唯一编码是否与其它节点冲突
        var duplicateExists = await context.table_definition
            .AnyAsync(t => t.is_valid && t.id != tableData.id && t.unique_name.ToLower() == uniqueName);
        if (duplicateExists)
            throw new ArgumentException($"表的唯一编码 '{uniqueName}' 已被其它节点使用", nameof(tableData.unique_name));

        // 获取原始父节点ID
         var originalParentId = await context.table_definition
            .AsNoTracking() // 不需要跟踪原始实体
            .Where(t => t.id == tableData.id)
            .Select(t => t.parent_node_id)
            .FirstOrDefaultAsync();
            
        // 验证父节点
        Guid? parentNodeId = tableData.parent_node_id == Guid.Empty ? null : tableData.parent_node_id;
        if (parentNodeId.HasValue)
        {
             // 不能将自己设为父节点
            if (parentNodeId.Value == tableData.id)
                throw new InvalidOperationException("不能将节点自身设置为其父节点");
                
            // 不能将后代设为父节点 (需要获取所有后代ID)
            var descendantIds = await GetAllDescendantIdsAsync(context, tableData.id);
            if (descendantIds.Contains(parentNodeId.Value))
                 throw new InvalidOperationException("不能将节点的后代设置为其父节点");
                
            var parentNode = await context.table_definition
                .AsNoTracking() // 不需要跟踪父节点实体
                .FirstOrDefaultAsync(t => t.id == parentNodeId.Value && t.is_valid);

            if (parentNode == null)
                throw new InvalidOperationException($"指定的父节点 (ID: {parentNodeId.Value}) 不存在或无效");
                
            if (parentNode.node_type != "Visual")
                throw new InvalidOperationException($"指定的父节点 (ID: {parentNodeId.Value}, Name: {parentNode.display_name}) 不是 'Visual' 类型，不能设置为父节点");
        }
        
        // 如果父节点改变，重新计算排序索引
        if (tableData.parent_node_id != originalParentId)
        {
             int maxSortedIndex = 0;
             var siblings = await context.table_definition
                 .Where(t => t.parent_node_id == tableData.parent_node_id && t.is_valid && t.id != tableData.id) // 排除自身
                 .Select(t => t.sorted_index)
                 .ToListAsync();
                 
             if (siblings.Any())
             {
                 maxSortedIndex = siblings.Max();
             }
             tableData.sorted_index = maxSortedIndex + 1;
        }
        
        // 如果节点类型从 Table 变为 Visual，则需要处理其下的列？（当前逻辑是 CubeManager 处理的，服务层不处理列）
        // 如果节点类型从 Visual 变为 Table，则可以开始添加列。

        // 标记实体为已修改
        context.Update(tableData);
    }

    /// <summary>
    /// 删除指定的表定义（逻辑删除，但不保存更改）
    /// </summary>
    /// <param name="context">数据库上下文</param>
    /// <param name="tableId">要删除的表的ID</param>
    /// <exception cref="ArgumentNullException">当 context 为空时抛出</exception>
    /// <exception cref="ArgumentException">当 tableId 无效时抛出</exception>
    /// <exception cref="InvalidOperationException">当节点不存在时抛出</exception>
    public async Task DeleteTableDefinitionAsync(CubeContext context, Guid tableId)
    {
        if (context == null)
            throw new ArgumentNullException(nameof(context));
        if (tableId == Guid.Empty)
            throw new ArgumentException("无效的表 ID", nameof(tableId));

        var tableToDelete = await context.table_definition
            .FirstOrDefaultAsync(t => t.id == tableId);

        if (tableToDelete == null)
            throw new InvalidOperationException($"尝试删除的表/节点 (ID: {tableId}) 在数据库中未找到。");

        if (!tableToDelete.is_valid)
        {
             // 如果已经是无效的，则无需操作
             return;
        }

        // 获取所有后代节点ID (包括自身)
        var allIdsToDelete = await GetAllDescendantIdsAsync(context, tableId); // 这个方法需要包含自身ID

        // 逻辑删除所有相关表/节点
        var tablesToUpdate = await context.table_definition
            .Where(t => allIdsToDelete.Contains(t.id) && t.is_valid)
            .ToListAsync();
            
        foreach (var table in tablesToUpdate)
        {
            table.is_valid = false;
        }

        // 获取这些表/节点下的所有有效列ID
        var columnIdsToDelete = await context.column_definition
            .Where(c => allIdsToDelete.Contains(c.table_id) && c.is_valid)
            .Select(c => c.id)
            .ToListAsync();

        // 逻辑删除所有相关列
        if (columnIdsToDelete.Any())
        {
             var columnsToUpdate = await context.column_definition
                 .Where(c => columnIdsToDelete.Contains(c.id))
                 .ToListAsync();
                 
             foreach (var column in columnsToUpdate)
             {
                 column.is_valid = false;
             }
        }
        
        // EF Core 会跟踪已加载实体的更改，无需显式调用 Update
    }

    /// <summary>
    /// 改变指定表的排序顺序，并重新索引同一父节点下的所有表/节点（但不保存更改）
    /// </summary>
    /// <param name="context">数据库上下文</param>
    /// <param name="tableId">要改变排序顺序的表的ID</param>
    /// <param name="direction">改变的方向（"up" 或 "down"）</param>
    /// <exception cref="ArgumentNullException">当 context 为空时抛出</exception>
    /// <exception cref="ArgumentException">当 tableId 或 direction 无效时抛出</exception>
    /// <exception cref="InvalidOperationException">当节点不存在、无效或无法移动时抛出</exception>
    public async Task ChangeTableSortOrderAsync(CubeContext context, Guid tableId, string direction)
    {
         if (context == null)
            throw new ArgumentNullException(nameof(context));
        if (tableId == Guid.Empty)
            throw new ArgumentException("无效的表 ID", nameof(tableId));
        if (direction != "up" && direction != "down")
            throw new ArgumentException("无效的方向，必须是 'up' 或 'down'", nameof(direction));

        var currentTable = await context.table_definition
            .FirstOrDefaultAsync(t => t.id == tableId && t.is_valid);

        if (currentTable == null)
            throw new InvalidOperationException($"找不到要排序的有效表/节点 (ID: {tableId})。");

        var parentId = currentTable.parent_node_id;

        // 获取所有有效的同级节点并按排序索引排序
        var siblings = await context.table_definition
            .Where(td => td.parent_node_id == parentId && td.is_valid)
            .OrderBy(td => td.sorted_index)
            .ToListAsync();

        if (!siblings.Any())
             // 理论上至少应该包含 currentTable
            throw new InvalidOperationException($"节点 (ID: {tableId}) 的同级列表为空。");

        // 找到当前节点的索引
        var currentIndex = siblings.FindIndex(td => td.id == tableId);
        if (currentIndex == -1)
        {
             // 理论上不应该发生
            throw new InvalidOperationException($"在排序列表中未找到表/节点 (ID: {tableId})。");
        }
        
        // 确定目标位置
        int targetIndex = -1;
        if (direction == "up")
        {
            if (currentIndex == 0) throw new InvalidOperationException("节点已经是第一个，无法上移。");
            targetIndex = currentIndex - 1;
        }
        else // direction == "down"
        {
            if (currentIndex == siblings.Count - 1) throw new InvalidOperationException("节点已经是最后一个，无法下移。");
            targetIndex = currentIndex + 1;
        }
        
        // 重新排序列表
        siblings.RemoveAt(currentIndex);
        siblings.Insert(targetIndex, currentTable);
        
        // 重新分配连续的 sorted_index 值，从 1 开始
        for (int i = 0; i < siblings.Count; i++)
        {
            // 只有当索引实际改变时才更新
            if (siblings[i].sorted_index != i + 1)
            {
                siblings[i].sorted_index = i + 1;
                 // EF Core 会跟踪已加载实体的更改
            }
        }
    }

    #endregion
}