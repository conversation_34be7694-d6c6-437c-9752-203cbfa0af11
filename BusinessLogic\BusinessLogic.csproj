﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Bio.AI" Version="0.1.526" />
    <PackageReference Include="BitzArt.Blazor.Auth.Server" Version="1.0.2" />
    <PackageReference Include="NLog.Web.AspNetCore" Version="5.3.14" />
    <PackageReference Include="Npgsql" Version="8.0.6" />
    <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="8.0.11" />
    <PackageReference Include="OllamaSharp" Version="4.0.11" />
    <PackageReference Include="Polly" Version="8.5.2" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\DataAccess\DataAccess.csproj" />
    <ProjectReference Include="..\Infrastructure\Infrastructure.csproj" />
  </ItemGroup>

</Project>
