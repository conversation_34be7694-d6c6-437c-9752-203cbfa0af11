﻿@implements IAsyncDisposable

@code {
    [Inject]
    protected IDbContextFactory<CubeContext> ContextFactory { get; set; } = default!;
        
    protected CubeContext? context { get; private set; }

    protected override async Task OnInitializedAsync()
    {
        context = await ContextFactory.CreateDbContextAsync();
        await OnPageInitializedAsync();
        await base.OnInitializedAsync();
    }

    // 提供一个虚方法供子类重写，实现自己的初始化逻辑
    protected virtual Task OnPageInitializedAsync() => Task.CompletedTask;

    // 提供刷新 Context 的方法
    protected async Task RefreshContextAsync()
    {
        if (context != null)
        {
            await context.DisposeAsync();
        }
        context = await ContextFactory.CreateDbContextAsync();
    }

    public async ValueTask DisposeAsync()
    {
        if (context != null)
        {
            await context.DisposeAsync();
        }
    }
}
