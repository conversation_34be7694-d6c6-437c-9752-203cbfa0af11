﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace DataAccess.Models;

/// <summary>
/// 功能权限
/// </summary>
public partial class sys_permission
{
    /// <summary>
    /// ID
    /// </summary>
    public Guid id { get; set; }

    /// <summary>
    /// 权限名称
    /// </summary>
    public string name { get; set; }

    /// <summary>
    /// 权限描述
    /// </summary>
    public string describe { get; set; }

    /// <summary>
    /// 权限类型
    /// </summary>
    public string type { get; set; }

    /// <summary>
    /// 权限Code
    /// </summary>
    public string code { get; set; }

    public virtual ICollection<sys_role> role { get; set; } = new List<sys_role>();
}