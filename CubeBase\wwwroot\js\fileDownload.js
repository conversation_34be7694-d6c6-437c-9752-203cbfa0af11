// 文件下载辅助函数
window.fileDownloadInterop = {
    // 从内容创建并下载文件的函数，改进版本会尝试触发浏览器的保存对话框
    downloadFromContent: function (content, filename, contentType) {
        try {
            // 使用FileSaver API (如果支持)
            if (window.navigator.msSaveOrOpenBlob) {
                // For IE/Edge
                const blob = new Blob([content], { type: contentType || 'application/json' });
                window.navigator.msSaveOrOpenBlob(blob, filename);
                console.log("使用IE/Edge特定的保存方法");
                return true;
            }

            // 创建Blob对象
            const blob = new Blob([content], { type: contentType || 'application/json' });
            
            // 创建临时URL
            const url = window.URL.createObjectURL(blob);
            
            // 创建临时下载链接，并修改属性以尝试触发保存对话框
            const link = document.createElement('a');
            link.href = url;
            link.setAttribute('download', filename);
            link.setAttribute('target', '_blank');  // 添加目标属性
            
            // 一些特定于浏览器的属性，可能有助于触发保存对话框
            if (link.dataset) {
                link.dataset.downloadurl = [contentType || 'application/json', filename, url].join(':');
            }
            
            // 确保使用特定的事件，可能会增加显示保存对话框的几率
            if (document.createEvent) {
                const event = document.createEvent('MouseEvents');
                event.initMouseEvent('click', true, true, window, 0, 0, 0, 0, 0, false, false, false, false, 0, null);
                link.dispatchEvent(event);
            } else {
                // 添加到文档并触发点击
                link.style.visibility = 'hidden';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            }
            
            // 延迟释放URL，给浏览器一些时间来处理下载
            setTimeout(function() {
                window.URL.revokeObjectURL(url);
            }, 100);
            
            console.log("文件下载请求已发送");
            return true;
        } catch (e) {
            console.error("文件下载失败:", e);
            return false;
        }
    },
    
    // 添加一个新方法，先提示用户输入文件名，然后再下载
    downloadWithPrompt: function (content, defaultFilename, contentType) {
        // 提示用户输入文件名
        const userFilename = prompt("请输入保存的文件名:", defaultFilename);
        
        // 如果用户取消或输入空文件名，则使用默认文件名
        const filename = userFilename || defaultFilename;
        
        // 调用下载函数
        return this.downloadFromContent(content, filename, contentType);
    }
}; 