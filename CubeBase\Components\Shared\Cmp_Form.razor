﻿@inject ILogger<Cmp_Form> inj_logger
@inject IDbContextFactory<CubeContext> ContextFactory
@using System.Diagnostics

<MudPaper Style="width: 100%;box-sizing: border-box;padding:0px 8px;">
    @if(Form!=null && _columnDefinitionList!=null)
    {
        <div class="d-flex justify-space-between align-center pa-2">
            <MudText Typo="Typo.h5">@Form.name</MudText>
            <MudButton 
                Variant="Variant.Outlined" 
                Color="Color.Primary" 
                StartIcon="@Icons.Material.Filled.HealthAndSafety"
                OnClick="@(() => OpenCardIntegrityCheckDialog(Form.id))">
                检查链表完整性
            </MudButton>
        </div>
        <MudDivider />
        
        <CascadingValue Value="@_columnDefinitionList">
            @foreach (var card in GetOrderedCards(Form.form_card.Where(c => c.parent_id == null)))
            {
                <MudGrid Class="pa-0" Spacing="1">
                    <Cmp_RecursiveCards @key="card.id" _context="_context" Card="card" OnParentRefresh="@OnRefreshThis" />
                    <!-- <Cmp_CardView Card="@card" /> -->
                </MudGrid>
            }

            @* 显示链外对象 *@
            @if (_chainOutObjects.Any())
            {
                <MudDivider Class="my-4" />
                <MudText Typo="Typo.h6" Color="Color.Warning" Class="ma-2">
                    <MudIcon Icon="@Icons.Material.Filled.LinkOff" /> 
                    发现 @_chainOutObjects.Count 个链外对象
                </MudText>
                
                <div style="display: flex; justify-content: center; width: 100%; margin-bottom: 16px;">
                    <div style="display: flex; width: 90%; gap: 16px;">
                        <MudButton Style="width: 50%;" Variant="Variant.Filled" Color="Color.Primary" 
                                  OnClick="@RecoverAllChainOutCards">
                            恢复所有链外卡片
                        </MudButton>
                        <MudButton Style="width: 50%;" Variant="Variant.Filled" Color="Color.Error" 
                                  OnClick="@DeleteAllChainOutQuestions">
                            删除所有链外问题
                        </MudButton>
                    </div>
                </div>
                
                @foreach (var obj in _chainOutObjects)
                {
                    <MudGrid Class="ma-1 pa-0" Spacing="1">
                        <MudItem xl="12">
                            <MudPaper Elevation="3" Class="pa-2 ma-1" 
                                     Style="border: 2px dashed #ff9800; background-color: #fff8e1;">
                                <div style="display: flex; justify-content: space-between; align-items: center;">
                                    <div>
                                        <MudText Typo="Typo.body1">
                                            <MudIcon Icon="@(obj.Type == "Card" ? Icons.Material.Filled.LinkOff : Icons.Material.Filled.QuestionMark)" 
                                                    Color="Color.Warning" />
                                            @obj.Name (ID: @obj.Id)
                                        </MudText>
                                        <MudText Typo="Typo.caption">
                                            类型：@obj.Type · 所属：@(obj.ParentName ?? "无") 
                                            @if(obj.ParentId != null){
                                                <span>(ID: @obj.ParentId)</span>
                                            }
                                        </MudText>
                                    </div>
                                    <div style="display: flex; gap: 8px;">
                                        @if (obj.Type == "Card")
                                        {
                                            <MudButton Variant="Variant.Filled" Color="Color.Success" 
                                                      OnClick="@(() => RecoverChainOutCard(obj.Entity as form_card))">
                                                恢复
                                            </MudButton>
                                        }
                                        <MudButton Variant="Variant.Filled" Color="Color.Error" 
                                                  OnClick="@(() => DeleteChainOutObject(obj))">
                                            删除
                                        </MudButton>
                                    </div>
                                </div>
                            </MudPaper>
                        </MudItem>
                    </MudGrid>
                }
            }
        </CascadingValue>
        <div style="display: flex; justify-content: center; width: 100%; gap: 16px; padding: 8px;">
            <MudButton Style="width: 45%;" Variant="Variant.Filled" Color="Color.Success" OnClick="@(() => AddCard())">添加Card</MudButton>
            <MudButton Style="width: 45%;" Variant="Variant.Filled" Color="Color.Success" OnClick="@(() => ImportCard())" Disabled="@_isImporting">
                @if (_isImporting)
                {
                    <MudProgressCircular Size="Size.Small" Indeterminate="true" Class="mr-2" />
                    <span>导入中...</span>
                }
                else
                {
                    <span>导入Card</span>
                }
            </MudButton>
        </div>
        
        <!-- 隐藏的文件上传控件 -->
        <InputFile id="@($"fileUpload-form-{Form.id}")" OnChange="HandleFileSelected" hidden accept=".json" />
    }
</MudPaper>
@code {
    [Parameter]
    public required CubeContext _context { get; set; }
    [Parameter]
    public required form_form Form { get; set; }
    [Parameter]
    public EventCallback OnParentRefresh { get; set; }
    [CascadingParameter]
    public bool IsReadOnlyMode { get; set; } = false;
    [CascadingParameter]
    public EventCallback OnFormChanged { get; set; }
    private List<column_definition> _columnDefinitionList { get; set; } = new();
    
    // 存储链外对象
    private List<ChainOutObject> _chainOutObjects = new List<ChainOutObject>();
    private bool _isImporting = false;

    // 新增链外对象定义类
    private class ChainOutObject
    {
        public Guid Id { get; set; }
        public string Name { get; set; }
        public string Type { get; set; } // "Card" 或 "Question"
        public Guid? ParentId { get; set; }
        public string ParentName { get; set; }
        public object Entity { get; set; } // 原始实体对象
    }

    // 统一对象模型，用于混合排序
    private class UnifiedElement
    {
        public Guid Id { get; set; }
        public string Name { get; set; }
        public string Type { get; set; } // "Card" 或 "Question"
        public Guid? PreUid { get; set; }
        public Guid? NextUid { get; set; }
        public Guid? ParentId { get; set; }
        public object Entity { get; set; } // 原始实体对象
        public List<UnifiedElement> Children { get; set; } = new List<UnifiedElement>();
    }

    private bool _isProcessingGhostElements = false;
    private Guid? _lastProcessedFormId = null;

    protected override async Task OnInitializedAsync()
    {
        var stopwatch = Stopwatch.StartNew();
        inj_logger.LogWarning("[性能调试] Cmp_Form.OnInitializedAsync 开始");
        
        // 移除不必要的列定义加载，改为按需加载
        // 原来：加载所有3647个列定义（1405ms）
        // 现在：延迟加载，只在需要时加载相关的列定义
        
        stopwatch.Stop();
        inj_logger.LogWarning("[性能调试] Cmp_Form.OnInitializedAsync 完成 - 耗时: {ElapsedMs}ms (优化后，移除了全量列定义加载)", 
            stopwatch.ElapsedMilliseconds);
    }

    protected override async Task OnParametersSetAsync()
    {
        var stopwatch = Stopwatch.StartNew();
        inj_logger.LogWarning("[性能调试] Cmp_Form.OnParametersSetAsync 开始 - FormID: {FormID}, FormName: {FormName}", 
            Form?.id, Form?.name);
        
        await base.OnParametersSetAsync();

        // 只有当Form真正变化时才执行
        if (Form != null && (_lastProcessedFormId == null || _lastProcessedFormId != Form.id))
        {
            var changeStopwatch = Stopwatch.StartNew();
            inj_logger.LogWarning("[性能调试] Form发生变化，开始处理");
            
            _lastProcessedFormId = Form.id;
            
            // 按需加载该表单相关的列定义
            await LoadFormRelatedColumnDefinitionsAsync();
            
            await FindChainOutObjects();
            
            changeStopwatch.Stop();
            inj_logger.LogWarning("[性能调试] Form变化处理完成 - 耗时: {ElapsedMs}ms", changeStopwatch.ElapsedMilliseconds);
            
            // 由于Form数据变化，需要通知UI更新
            var stateChangeStopwatch = Stopwatch.StartNew();
            StateHasChanged();
            stateChangeStopwatch.Stop();
            inj_logger.LogWarning("[性能调试] Form StateHasChanged 耗时: {ElapsedMs}ms", stateChangeStopwatch.ElapsedMilliseconds);
        }
        
        stopwatch.Stop();
        inj_logger.LogWarning("[性能调试] Cmp_Form.OnParametersSetAsync 总耗时: {ElapsedMs}ms", stopwatch.ElapsedMilliseconds);
    }

    /// <summary>
    /// 按需加载该表单相关的列定义，而不是加载全部3647个列定义
    /// </summary>
    private async Task LoadFormRelatedColumnDefinitionsAsync()
    {
        var stopwatch = Stopwatch.StartNew();
        inj_logger.LogWarning("[性能调试] LoadFormRelatedColumnDefinitionsAsync 开始 - FormID: {FormID}", Form.id);
        
        // 收集该表单所有问题关联的列定义ID
        var columnDefinitionIds = Form.form_card
            .SelectMany(c => c.form_question)
            .Where(q => q.column_definition_id != Guid.Empty)
            .Select(q => q.column_definition_id)
            .Distinct()
            .ToList();
            
        if (columnDefinitionIds.Any())
        {
            await using var scope = await ContextFactory.CreateDbContextAsync();
            _columnDefinitionList = await scope.column_definition
                .AsNoTracking()
                .Where(cd => columnDefinitionIds.Contains(cd.id))
                .ToListAsync();
        }
        else
        {
            _columnDefinitionList = new List<column_definition>();
        }
        
        stopwatch.Stop();
        inj_logger.LogWarning("[性能调试] LoadFormRelatedColumnDefinitionsAsync 完成 - 耗时: {ElapsedMs}ms, 加载列定义数量: {ColumnCount} (原来加载全部3647个)", 
            stopwatch.ElapsedMilliseconds, _columnDefinitionList.Count);
    }

    /// <summary>
    /// 获取统一排序的元素列表，包括卡片和问题，并处理嵌套关系
    /// </summary>
    private List<UnifiedElement> GetUnifiedOrderedElements(IEnumerable<form_card> cards, IEnumerable<form_question> questions, Guid? parentCardId = null)
    {
        var result = new List<UnifiedElement>();
        
        // 转换卡片和问题为统一元素模型
        var elements = new List<UnifiedElement>();
        
        // 添加当前层级的卡片
        var currentLevelCards = cards.Where(c => c.parent_id == parentCardId).ToList();
        foreach (var card in currentLevelCards)
        {
            elements.Add(new UnifiedElement
            {
                Id = card.id,
                Name = card.name,
                Type = "Card",
                PreUid = card.pre_uid,
                NextUid = card.next_uid,
                ParentId = card.parent_id,
                Entity = card
            });
        }
        
        // 如果是处理卡片内部，添加该卡片的问题
        if (parentCardId.HasValue)
        {
            var cardQuestions = questions.Where(q => q.card_id == parentCardId).ToList();
            foreach (var question in cardQuestions)
            {
                elements.Add(new UnifiedElement
                {
                    Id = question.id,
                    Name = question.display_name,
                    Type = "Question",
                    PreUid = question.pre_uid,
                    NextUid = question.next_uid,
                    ParentId = question.card_id,
                    Entity = question
                });
            }
        }
        
        // 如果没有元素，直接返回空列表
        if (!elements.Any())
        {
            return result;
        }
        
        // 查找起始元素（pre_uid为null的元素）
        var startElement = elements.FirstOrDefault(e => e.PreUid == null);
        if (startElement == null)
        {
            return result;
        }
        
        // 添加起始元素
        result.Add(startElement);
        
        // 使用集合记录已处理过的元素ID，防止循环引用
        var processedIds = new HashSet<Guid> { startElement.Id };
        var currentElement = startElement;
        
        // 按照next_uid指针遍历链表
        while (currentElement.NextUid != null)
        {
            // 防止循环引用
            if (processedIds.Contains(currentElement.NextUid.Value))
            {
                break;
            }
            
            var nextElement = elements.FirstOrDefault(e => e.Id == currentElement.NextUid);
            if (nextElement == null)
            {
                break;
            }
            
            result.Add(nextElement);
            processedIds.Add(nextElement.Id);
            currentElement = nextElement;
        }
        
        // 递归处理每个卡片的子元素
        foreach (var element in result.Where(e => e.Type == "Card").ToList())
        {
            var card = element.Entity as form_card;
            var childElements = GetUnifiedOrderedElements(cards, questions, card.id);
            element.Children.AddRange(childElements);
        }
        
        return result;
    }
    
    /// <summary>
    /// 将嵌套的统一元素列表展平为单一列表
    /// </summary>
    private List<UnifiedElement> FlattenUnifiedElements(List<UnifiedElement> elements)
    {
        var result = new List<UnifiedElement>();
        
        foreach (var element in elements)
        {
            result.Add(element);
            
            if (element.Children.Any())
            {
                result.AddRange(FlattenUnifiedElements(element.Children));
            }
        }
        
        return result;
    }

    /// <summary>
    /// 查找表单中的链外对象
    /// </summary>
    private async Task FindChainOutObjects()
    {
        var stopwatch = Stopwatch.StartNew();
        inj_logger.LogWarning("[性能调试] FindChainOutObjects 开始 - FormID: {FormID}", Form.id);
        
        _chainOutObjects.Clear();
        
        // 获取所有卡片和问题的统一有序列表
        var sw1 = Stopwatch.StartNew();
        var unifiedElements = GetUnifiedOrderedElements(Form.form_card, Form.form_question);
        sw1.Stop();
        inj_logger.LogWarning("[性能调试] GetUnifiedOrderedElements 耗时: {ElapsedMs}ms", sw1.ElapsedMilliseconds);
        
        // 展平嵌套结构，获取所有有效的链内元素
        var sw2 = Stopwatch.StartNew();
        var flattenedElements = FlattenUnifiedElements(unifiedElements);
        sw2.Stop();
        inj_logger.LogWarning("[性能调试] FlattenUnifiedElements 耗时: {ElapsedMs}ms", sw2.ElapsedMilliseconds);
        
        // 收集所有有效的卡片ID和问题ID
        var sw3 = Stopwatch.StartNew();
        var validCardIds = flattenedElements
            .Where(e => e.Type == "Card")
            .Select(e => e.Id)
            .ToHashSet();
            
        var validQuestionIds = flattenedElements
            .Where(e => e.Type == "Question")
            .Select(e => e.Id)
            .ToHashSet();
        sw3.Stop();
        inj_logger.LogWarning("[性能调试] 收集有效ID 耗时: {ElapsedMs}ms", sw3.ElapsedMilliseconds);
        
        // 查找链外卡片（存在于表单但不在有序链表中）
        var sw4 = Stopwatch.StartNew();
        var outCards = Form.form_card
            .Where(c => !validCardIds.Contains(c.id))
            .Select(c => new ChainOutObject 
            {
                Id = c.id,
                Name = c.name,
                Type = "Card",
                ParentId = c.parent_id,
                ParentName = Form.form_card.FirstOrDefault(p => p.id == c.parent_id)?.name,
                Entity = c
            });
        
        // 查找链外问题
        var outQuestions = Form.form_question
            .Where(q => !validQuestionIds.Contains(q.id))
            .Select(q => new ChainOutObject 
            {
                Id = q.id,
                Name = q.display_name,
                Type = "Question",
                ParentId = q.card_id,
                ParentName = Form.form_card.FirstOrDefault(c => c.id == q.card_id)?.name,
                Entity = q
            });
        
        _chainOutObjects.AddRange(outCards.Concat(outQuestions));
        sw4.Stop();
        inj_logger.LogWarning("[性能调试] 查找链外对象 耗时: {ElapsedMs}ms", sw4.ElapsedMilliseconds);
        
        stopwatch.Stop();
        inj_logger.LogWarning("[性能调试] FindChainOutObjects 完成 - 总耗时: {ElapsedMs}ms, 链外对象数: {ChainOutCount}, 卡片: {OutCardCount}, 问题: {OutQuestionCount}", 
            stopwatch.ElapsedMilliseconds, _chainOutObjects.Count, outCards.Count(), outQuestions.Count());
    }

    public async Task OnRefreshThis()
    {        
        //await LoadDataAsync();
        await FindChainOutObjects();
        // 由于链外对象状态变化，需要更新UI
        StateHasChanged();
        
        // 通知父组件有更改
        if (OnParentRefresh.HasDelegate)
        {
            await OnParentRefresh.InvokeAsync();
        }
    }

    private async Task AddCard()
    {
        if (Form == null) return;

        inj_logger.LogWarning(Form.name + ":添加卡片");

        await inj_cardService.AddCardToRootAsync(_context, Form);

        // 添加卡片后需要刷新链外对象列表
        await FindChainOutObjects();
        
        // 通知父组件有更改
        if (OnParentRefresh.HasDelegate)
        {
            await OnParentRefresh.InvokeAsync();
        }
        
        // 数据变化会自动触发子组件更新，这里只需要更新当前组件的UI
        StateHasChanged();
    }

    private IEnumerable<form_card> GetOrderedCards(IEnumerable<form_card> cards)
    {
        var stopwatch = Stopwatch.StartNew();
        var inputCount = cards.Count();
        inj_logger.LogWarning("[性能调试] GetOrderedCards 开始 - 输入卡片数: {InputCount}", inputCount);
        
        // 使用字典优化查找性能
        var cardDict = cards.ToDictionary(c => c.id);
        var orderedCards = new List<form_card>();
        
        // 查找起始卡片（pre_uid为null的卡片）
        var currentCard = cards.FirstOrDefault(c => c.pre_uid == null);
        
        // 按链表顺序遍历
        while (currentCard != null)
        {
            orderedCards.Add(currentCard);
            
            // 使用字典快速查找下一张卡片
            currentCard = currentCard.next_uid.HasValue ? 
                cardDict.GetValueOrDefault(currentCard.next_uid.Value) : null;
        }
        
        stopwatch.Stop();
        inj_logger.LogWarning("[性能调试] GetOrderedCards 完成 - 耗时: {ElapsedMs}ms, 输入: {InputCount}, 输出: {OutputCount}", 
            stopwatch.ElapsedMilliseconds, inputCount, orderedCards.Count);
        
        return orderedCards;
    }
    
    /// <summary>
    /// 获取有序的问题列表
    /// </summary>
    private IEnumerable<form_question> GetOrderedQuestions(IEnumerable<form_question> questions)
    {
        var orderedQuestions = new List<form_question>();
        var firstQuestion = questions.FirstOrDefault(q => q.pre_uid == null);
        
        if (firstQuestion == null) return orderedQuestions;
        
        orderedQuestions.Add(firstQuestion);
        var currentQuestion = firstQuestion;
        
        while (currentQuestion.next_uid != null)
        {
            var nextQuestion = questions.FirstOrDefault(q => q.id == currentQuestion.next_uid);
            if (nextQuestion == null) break;
            
            orderedQuestions.Add(nextQuestion);
            currentQuestion = nextQuestion;
        }
        
        return orderedQuestions;
    }

    /// <summary>
    /// 打开链表完整性检查对话框
    /// </summary>
    private async Task OpenCardIntegrityCheckDialog(Guid formId)
    {
        var parameters = new DialogParameters();
        parameters.Add("FormId", formId);
        
        var options = new DialogOptions
        {
            CloseOnEscapeKey = true,
            MaxWidth = MaxWidth.Large,
            FullWidth = true,
            CloseButton = true
        };

        var dialog = await inj_dialogService.ShowAsync<Cmp_CardIntegrityCheck>("链表完整性检查", parameters, options);
        var result = await dialog.Result;
        
        if (result.Canceled)
            return;
            
        // 对话框关闭后刷新数据
        await OnRefreshThis();
    }

    /// <summary>
    /// 恢复所有链外卡片
    /// </summary>
    private async Task RecoverAllChainOutCards()
    {
        var cardObjects = _chainOutObjects.Where(o => o.Type == "Card").ToList();
        if (!cardObjects.Any()) return;
        
        try
        {
            inj_logger.LogWarning($"开始恢复所有链外卡片，共 {cardObjects.Count} 个");
            
            int successCount = 0;
            foreach (var obj in cardObjects)
            {
                try
                {
                    // 恢复链外卡片
                    var card = obj.Entity as form_card;
                    await RecoverChainOutCard(card);
                    successCount++;
                }
                catch (Exception ex)
                {
                    inj_logger.LogError(ex, $"恢复链外卡片失败: {obj.Name} (ID: {obj.Id})");
                }
            }
            
            inj_logger.LogWarning($"链外卡片恢复完成，成功: {successCount}，失败: {cardObjects.Count - successCount}");
            inj_snackbar.Add($"已成功恢复 {successCount} 个链外卡片", Severity.Success);
            
            // 刷新UI
            await FindChainOutObjects();
            await InvokeAsync(StateHasChanged);
        }
        catch (Exception ex)
        {
            inj_logger.LogError(ex, "恢复所有链外卡片失败");
            inj_snackbar.Add($"恢复所有链外卡片失败: {ex.Message}", Severity.Error);
        }
    }

    /// <summary>
    /// 恢复单个链外卡片
    /// </summary>
    private async Task RecoverChainOutCard(form_card card)
    {
        if (card == null) return;

        try
        {
            inj_logger.LogWarning($"开始恢复链外卡片: {card.name} (ID: {card.id})");
            
            // 确保卡片是根级卡片
            card.parent_id = null;
            
            // 使用CardService将链外卡片添加到根级链表末尾
            await inj_cardService.AddCardToRootAsync(_context, Form, card);
            
            inj_logger.LogWarning($"链外卡片恢复成功: {card.name} (ID: {card.id})");
            inj_snackbar.Add($"链外卡片 {card.name} 已成功恢复到根级", Severity.Success);
            
            // 刷新UI
            await FindChainOutObjects();
            await InvokeAsync(StateHasChanged);
        }
        catch (Exception ex)
        {
            inj_logger.LogError(ex, $"恢复链外卡片失败: {card.name} (ID: {card.id})");
            inj_snackbar.Add($"恢复链外卡片失败: {ex.Message}", Severity.Error);
        }
    }

    /// <summary>
    /// 删除所有链外问题
    /// </summary>
    private async Task DeleteAllChainOutQuestions()
    {
        var questionObjects = _chainOutObjects.Where(o => o.Type == "Question").ToList();
        if (!questionObjects.Any())
        {
            inj_snackbar.Add("没有可删除的链外问题。", Severity.Info);
            return;
        }
        
        try
        {
            // 弹出确认对话框
            bool? result = await inj_dialogService.ShowMessageBox(
                "确认删除",
                $"确定要删除所有 {questionObjects.Count} 个链外问题吗？此操作不可恢复！",
                yesText: "确认删除", 
                noText: "取消",
                options: new DialogOptions { CloseButton = true, MaxWidth = MaxWidth.ExtraSmall }
            );
            
            if (result != true) return;
            
            inj_logger.LogWarning($"用户确认删除所有 {questionObjects.Count} 个链外问题。");
            
            var questionsToRemove = new List<form_question>();
            foreach (var obj in questionObjects)
            {
                if (obj.Entity is form_question question)
                {
                    questionsToRemove.Add(question);
                }
                else
                {
                    inj_logger.LogWarning($"链外对象 (ID: {obj.Id}, Name: {obj.Name}) 不是有效的 form_question 类型，跳过删除。");
                }
            }

            if (questionsToRemove.Any())
            {
                _context.form_question.RemoveRange(questionsToRemove);
                try
                {
                    await _context.SaveChangesAsync();
                    inj_logger.LogWarning($"成功批量删除 {questionsToRemove.Count} 个链外问题。");
                    inj_snackbar.Add($"已成功删除 {questionsToRemove.Count} 个链外问题", Severity.Success);
                }
                catch (Exception ex)
                {
                    inj_logger.LogError(ex, $"批量删除 {questionsToRemove.Count} 个链外问题时保存更改失败。");
                    inj_snackbar.Add($"批量删除链外问题失败: {ex.Message}", Severity.Error);
                }
            }
            else
            {
                inj_logger.LogWarning("在链外对象中没有找到符合条件的 form_question 实体进行删除。");
                inj_snackbar.Add("没有可删除的链外问题。", Severity.Info);
            }
            
            // 刷新UI
            await FindChainOutObjects();
            await InvokeAsync(StateHasChanged);
        }
        catch (Exception ex) // Catch exceptions from dialog or FindChainOutObjects
        {
            inj_logger.LogError(ex, "删除所有链外问题操作过程中发生意外错误。");
            inj_snackbar.Add($"删除所有链外问题失败: {ex.Message}", Severity.Error);
        }
    }

    /// <summary>
    /// 删除单个链外对象
    /// </summary>
    private async Task DeleteChainOutObject(ChainOutObject obj)
    {
        if (obj == null) return;
        
        try
        {
            // 弹出确认对话框
            bool? result = await inj_dialogService.ShowMessageBox(
                "确认删除",
                $"确定要删除链外对象 \"{obj.Name}\" 吗？此操作不可恢复！",
                yesText: "确认删除", 
                noText: "取消"
            );
            
            if (result != true) return;
            
            inj_logger.LogWarning($"开始删除链外对象: {obj.Name} (ID: {obj.Id})");
            
            // 从数据库中删除对象
            if (obj.Type == "Card")
            {
                var card = obj.Entity as form_card;
                _context.form_card.Remove(card);
            }
            else if (obj.Type == "Question")
            {
                var question = obj.Entity as form_question;
                _context.form_question.Remove(question);
            }
            await _context.SaveChangesAsync();
            
            inj_logger.LogWarning($"链外对象删除完成: {obj.Name} (ID: {obj.Id})");
            inj_snackbar.Add($"已成功删除链外对象: {obj.Name}", Severity.Success);
            
            // 刷新UI
            await FindChainOutObjects();
            await InvokeAsync(StateHasChanged);
        }
        catch (Exception ex)
        {
            inj_logger.LogError(ex, $"删除链外对象失败: {obj.Name} (ID: {obj.Id})");
            inj_snackbar.Add($"删除链外对象失败: {ex.Message}", Severity.Error);
        }
    }

    private async Task ImportCard()
    {
        // 弹出确认对话框
        var confirm = await inj_dialogService.ShowMessageBox(
            "确定要导入卡片到表单根层级吗？",
            "导入的卡片将被添加到表单的最顶层", 
            yesText: "确定", 
            cancelText: "取消"
        );

        if (confirm == true)
        {
            try
            {
                // 触发隐藏的文件上传控件
                await inj_jsRuntime.InvokeVoidAsync("domInterop.clickElement", $"fileUpload-form-{Form.id}");
            }
            catch (Exception ex)
            {
                inj_logger.LogError(ex, "触发文件选择对话框失败");
                inj_snackbar.Add("打开文件选择对话框失败，请重试", Severity.Error);
            }
        }
    }

    private async Task HandleFileSelected(InputFileChangeEventArgs e)
    {
        try
        {
            if (e.FileCount == 0) return;

            var file = e.File;
            _isImporting = true;
            await InvokeAsync(StateHasChanged);

            inj_logger.LogInformation($"准备导入文件到表单根层级: {file.Name}, 大小: {file.Size} 字节");
            inj_snackbar.Add($"正在读取文件：{file.Name}", Severity.Info);

            // 读取文件内容
            using var stream = file.OpenReadStream(maxAllowedSize: 1024 * 1024 * 10); // 允许最大10MB
            using var reader = new StreamReader(stream);
            var jsonContent = await reader.ReadToEndAsync();

            if (string.IsNullOrWhiteSpace(jsonContent))
            {
                inj_snackbar.Add("导入失败：文件内容为空", Severity.Error);
                _isImporting = false;
                await InvokeAsync(StateHasChanged);
                return;
            }

            // 调用导入服务，传入null作为targetCardId，表示导入到表单根层级
            inj_snackbar.Add($"正在导入卡片到表单根层级...", Severity.Info);
            var importResult = await inj_cardImportService.ImportCardFromJsonAsync(_context, Form, null, jsonContent);

            if (importResult.Success)
            {
                inj_snackbar.Add($"导入成功：{importResult.Message}", Severity.Success);
                // 刷新表单
                await OnRefreshThis();
            }
            else
            {
                inj_snackbar.Add($"导入失败：{importResult.Message}", Severity.Error);
            }
        }
        catch (Exception ex)
        {
            inj_logger.LogError(ex, "导入文件过程中发生错误");
            inj_snackbar.Add($"导入过程中发生错误：{ex.Message}", Severity.Error);
        }
        finally
        {
            _isImporting = false;
            await InvokeAsync(StateHasChanged);
        }
    }
}
