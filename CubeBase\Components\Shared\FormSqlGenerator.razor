@inherits BasePage
@inject ILogger<FormSqlGenerator> Logger

<MudExpansionPanels Elevation="2" Outlined="true" Class="mt-1 pa-0" Style="background-color: transparent;">
    <MudExpansionPanel Text="表单SQL生成" Expanded="false">
        <TitleContent>
            <div class="d-flex align-items-center">
                <span class="mr-2">选择表单集</span>
                <Cascader TValue="string" OnSelectedItemChanged="@OnProjectChanged" Items="@_projectItems" ParentSelectable="false" style="width: 800px;padding: 2px; z-index: 1200;" />
                <MudButton Class="ml-4" Variant="Variant.Filled" Color="Color.Primary"
                Disabled="@(!_isGenerateButtonEnabled)"
                OnClick="@(async ()=>await UpdateSelectedItemsTextAsync())">生成SQL</MudButton>
            </div>
        </TitleContent>
        <ChildContent>
            <MudTextField T="string" Label="创建表单集的SQL" Value="@_sqlScript" Variant="Variant.Outlined" Lines="5" AutoGrow MaxLines="120" FullWidth Margin="Margin.Dense" />
        </ChildContent>
    </MudExpansionPanel>
</MudExpansionPanels>

@code {
    // 表定义字典
    private Dictionary<string, string> _tableDefinitionDict = new();
    // 列定义字典
    private Dictionary<string, string> _columnDefinitionDict = new();

    [Parameter]
    public List<IDataDictLLMService.ParsedDataItem> Items { get; set; } = new();

    private string _sqlScript = string.Empty;
    private bool _isGenerateButtonEnabled = false;

    // 级联选择器相关变量
    private List<CascaderItem> _projectItems = [];
    private List<sys_hospital> _contextHospitalList = new();
    private form_form_set? _selectedFormSet;

    // 统计待分析的项目数量（ID中有3个'-'且LlmAnalysis为null或IsProcessed为false）
    private int PendingAnalysisCount => Items.Count(item => CountDashes(item.Id) == 3 && (item.LlmAnalysis == null || !item.LlmAnalysis.IsProcessed));

    // 统计已匹配的项目数量（ID中有3个'-'且LlmAnalysis.IsProcessed为true且MatchFound为true）
    private int MatchedCount => Items.Count(item => CountDashes(item.Id) == 3 && item.LlmAnalysis != null && item.LlmAnalysis.IsProcessed && item.LlmAnalysis.MatchFound);

    // 统计未匹配的项目数量（ID中有3个'-'且LlmAnalysis.IsProcessed为true且MatchFound为false）
    private int UnmatchedCount => Items.Count(item => CountDashes(item.Id) == 3 && item.LlmAnalysis != null && item.LlmAnalysis.IsProcessed && !item.LlmAnalysis.MatchFound);

    // 辅助方法：计算字符串中'-'的数量
    private int CountDashes(string id) => id?.Count(c => c == '-') ?? 0;

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();
        await LoadProjectData();
        await LoadTableDefinitions();
    }

    protected override void OnParametersSet()
    {
        base.OnParametersSet();
        CheckButtonState();
    }

    private void CheckButtonState() => _isGenerateButtonEnabled = PendingAnalysisCount == 0 && UnmatchedCount == 0 && _selectedFormSet != null;

    // 加载表定义和列定义数据
    private async Task LoadTableDefinitions()
    {
        using var context = await ContextFactory.CreateDbContextAsync();
        
        var tableDefinitions = await context.table_definition.ToListAsync();
        _tableDefinitionDict = tableDefinitions.ToDictionary(t => t.id.ToString(), t => t.unique_name);
        
        var columnDefinitions = await context.column_definition.ToListAsync();
        _columnDefinitionDict = columnDefinitions.ToDictionary(c => c.id.ToString(), c => c.unique_name);
    }

    private async Task LoadProjectData()
    {
        using var context = await ContextFactory.CreateDbContextAsync();

        _contextHospitalList = await context.sys_hospital
            .Include(td => td.sys_department)
                .ThenInclude(d => d.sys_ward)
                    .ThenInclude(w => w.form_project)
                        .ThenInclude(p => p.form_form_set)
            .ToListAsync();

        _projectItems.Clear();
        // 构建级联下拉菜单
        foreach (var hospital in _contextHospitalList.Where(h => h.form_project.Any()).OrderBy(h => h.name))
        {
            var hospitalItem = new CascaderItem(hospital.id.ToString(), $" {hospital.name} ");
            foreach (var department in hospital.sys_department.Where(d => d.form_project.Any()).OrderBy(d => d.name))
            {
                var departmentItem = new CascaderItem(department.id.ToString(), $" {department.name} ");
                foreach (var ward in department.sys_ward.Where(w => w.form_project.Any()).OrderBy(w => w.name))
                {
                    var wardItem = new CascaderItem(ward.id.ToString(), $" {ward.name} ");
                    foreach (var project in ward.form_project.OrderBy(p => p.name))
                    {
                        var projectItem = new CascaderItem(project.id.ToString(), $" {project.name} ");
                        foreach (var formSet in project.form_form_set.OrderBy(f => f.name))
                        {
                            var formSetItem = new CascaderItem(formSet.id.ToString(), $" {formSet.name} ");
                            projectItem.AddItem(formSetItem);
                        }
                        wardItem.AddItem(projectItem);
                    }
                    departmentItem.AddItem(wardItem);
                }
                hospitalItem.AddItem(departmentItem);
            }
            _projectItems.Add(hospitalItem);
        }
    }

    private async Task OnProjectChanged(CascaderItem[] items)
    {
        _selectedFormSet = null;

        if (items != null && items.Length > 0)
        {
            var selectedId = items[^1].Value;
            using var context = await ContextFactory.CreateDbContextAsync();

            // 只处理选择了表单集的情况
            if (items.Length == 5) // 选择了表单集
            {
                _selectedFormSet = await context.form_form_set
                    .Include(fs => fs.form_form)
                        .ThenInclude(f => f.form_card)
                            .ThenInclude(c => c.form_question)
                    .FirstOrDefaultAsync(fs => fs.id.ToString() == selectedId);
            }
        }

        CheckButtonState();
        await InvokeAsync(StateHasChanged);
    }

    private async Task UpdateSelectedItemsTextAsync()
    {
        if (_selectedFormSet == null)
        {
            inj_snackbar.Add("请选择表单集", Severity.Error);
            return;
        }

        // get form_form_set 实体对象
        using var context = await ContextFactory.CreateDbContextAsync();
        var formSet = await context.form_form_set.FindAsync(_selectedFormSet.id);
        if (formSet == null)
        {
            inj_snackbar.Add("选择的表单集不存在", Severity.Error);
            return;
        }

        var _groupItems = Items.Where(i => i.Id.Count(c => c == '-') == 0).ToList();
        var _formItems = Items.Where(i => i.Id.Count(c => c == '-') == 1).ToList();

        var sb = new System.Text.StringBuilder();

        var formSortedIndex = 0;
        // 生成表单SQL
        foreach (var formItem in _formItems)
        {
            // 在selectedGroupItems中找到formItem对应的groupItem
            var groupItem = _groupItems.FirstOrDefault(g => g.Id.Split(')')[0] == formItem.Id.Split('-')[0]);
            if (groupItem == null)
            {
                inj_snackbar.Add("请选择表单集", Severity.Error);
                return;
            }

            var formId = SequentialGuidGenerator.NewGuid();
            sb.Append("INSERT INTO form.form_form (id, name, group_name, created_at, updated_at, form_set_id, form_set_name, project_id, project_name, ward_id, ward_name, department_id, department_name, hospital_id, hospital_name, sort_index)");
            sb.AppendLine($" VALUES ('{formId}', '{inj_dataDictLLMService.CleanDisplayName(formItem.DisplayName)}', '{inj_dataDictLLMService.CleanDisplayName(formItem.DisplayName)}', NOW(), NOW(), '{formSet.id}', '{formSet.name}', '{formSet.project_id}', '{formSet.project_name}', '{formSet.ward_id}', '{formSet.ward_name}', '{formSet.department_id}', '{formSet.department_name}', '{formSet.hospital_id}', '{formSet.hospital_name}', {formSortedIndex});");

            sb.AppendLine();
            
            // 为当前表单生成所有卡片和问题的SQL
            ProcessAndGenerateSqlForChildrenOf(
                formItem.Id,      // 表单ID作为逻辑父ID
                null,             // 表单根级卡片没有父卡片ID
                formId,           // 当前表单的数据库ID
                inj_dataDictLLMService.CleanDisplayName(formItem.DisplayName),  // 表单名称
                formSet,          // 表单集详情
                sb,               // SQL构建器
                Items             // 所有解析项
            );

            formSortedIndex += 10;
        }

        _sqlScript = sb.ToString().TrimEnd();
    }
    
    // 根据ID分析字符串，获取父ID
    private string GetParentIdFromItemId(string itemIdWithParentheses) // itemId is the core ID, e.g., "(1-1-1)" or "(1-2.1)"
    {
        if (string.IsNullOrEmpty(itemIdWithParentheses) || itemIdWithParentheses.Length <= 2 || !itemIdWithParentheses.StartsWith("(") || !itemIdWithParentheses.EndsWith(")"))
            return null; // Invalid format or too short

        string coreId = itemIdWithParentheses.Substring(1, itemIdWithParentheses.Length - 2);

        int lastDashIndex = coreId.LastIndexOf('-');

        if (lastDashIndex == -1)
            return null;

        int lastDotIndex = coreId.LastIndexOf('.');

        string parentCoreId;
        
        if (lastDotIndex > lastDashIndex)
        {
            parentCoreId = coreId.Substring(0, lastDotIndex);
        }
        else
        {
            parentCoreId = coreId.Substring(0, lastDashIndex);
        }

        if (string.IsNullOrEmpty(parentCoreId))
            return null;

        return $"({parentCoreId})";
    }
    
    // 根据ID判断项类型（卡片或问题）
    private string GetItemTypeFromId(string itemId)
    {
        if (string.IsNullOrEmpty(itemId))
            return null;
            
        int dashCount = itemId.Count(c => c == '-');
        
        // 无破折号或只有一个破折号的是分组或表单，不属于卡片或问题
        if (dashCount < 2)
            return null;
            
        return IsQuestionByDashCount(dashCount) ? "question" : "card";
    }
    
    // 根据破折号数量判断是否为问题
    private bool IsQuestionByDashCount(int dashCount)
    {
        // 问题的ID格式为: i-j-k-l 或 i-j-k.n-l，有4个部分（3个破折号）
        return dashCount >= 3;
    }
    
    // 判断卡片类型（'default'或'multiple'）
    private string GetCardTypeFromParsedItem(IDataDictLLMService.ParsedDataItem item)
    {
        // 根据DisplayName判断卡片类型（方括号中包含的信息）
        // 例如 "科研基本信息 [复选目录] (1-1-1)"
        string displayName = item.DisplayName ?? "";
        
        // 查找是否包含"复选目录"标记
        if (displayName.Contains("[复选目录]"))
            return "multiple";
            
        // 默认为普通目录
        return "default";
    }
    
    private class ProcessedChildInfo
    {
        public IDataDictLLMService.ParsedDataItem OriginalItem { get; set; }
        public Guid GeneratedDbId { get; set; }
        public Guid? PreUidForDb { get; set; }
        public Guid? NextUidForDb { get; set; }
        public string ItemType { get; set; } // "card" 或 "question"
        public string CardUiType { get; set; } // "default" 或 "multiple"
    }
    
    // 递归处理子项并生成SQL
    private void ProcessAndGenerateSqlForChildrenOf(
        string parentIdFromItems, // This is item.Id from Items list, like "(ID_PART)"
        Guid? dbParentCardId,
        Guid dbFormId,
        string dbFormName,
        form_form_set formSetDetails,
        System.Text.StringBuilder sb,
        List<IDataDictLLMService.ParsedDataItem> allItems)
    {        
        // 1. 收集直接子项
        var directChildren = allItems.Where(item => {
            string childsParentIdInItemsFormat = GetParentIdFromItemId(item.Id);
            return childsParentIdInItemsFormat == parentIdFromItems;
        }).ToList();
        
        if (!directChildren.Any())
            return;
            
        // 2. 预分配数据库ID和链接
        var processedChildren = new List<ProcessedChildInfo>();
        
        foreach (var child in directChildren)
        {
            var childType = GetItemTypeFromId(child.Id);
            if (childType == null)
                continue; // 跳过不是卡片或问题的项
                
            processedChildren.Add(new ProcessedChildInfo
            {
                OriginalItem = child,
                GeneratedDbId = SequentialGuidGenerator.NewGuid(),
                ItemType = childType,
                CardUiType = childType == "card" ? GetCardTypeFromParsedItem(child) : null
            });
        }
        
        // 设置pre_uid和next_uid
        for (int i = 0; i < processedChildren.Count; i++)
        {
            // 第一个项的pre_uid为null
            processedChildren[i].PreUidForDb = i > 0 ? processedChildren[i - 1].GeneratedDbId : null;
            
            // 最后一个项的next_uid为null，其他项的next_uid指向下一个项
            processedChildren[i].NextUidForDb = i < processedChildren.Count - 1 ? processedChildren[i + 1].GeneratedDbId : null;
        }
        
        // 3. 为每个子项生成INSERT SQL，并递归处理卡片的子项
        foreach (var processedChild in processedChildren)
        {
            if (processedChild.ItemType == "card")
            {
                // 生成卡片的INSERT SQL
                sb.AppendLine("INSERT INTO form.form_card (id, parent_id, type, name, is_hidden, pre_uid, next_uid, form_id, form_name, form_set_id, form_set_name, project_id, project_name, ward_id, ward_name, department_id, department_name, hospital_id, hospital_name, created_at, updated_at)");
                sb.AppendLine($" VALUES ('{processedChild.GeneratedDbId}', " +
                    $"{(dbParentCardId.HasValue ? $"'{dbParentCardId}'" : "NULL")}, " +
                    $"'{processedChild.CardUiType}', " +
                    $"'{inj_dataDictLLMService.CleanDisplayName(processedChild.OriginalItem.DisplayName)}', " +
                    $"false, " +
                    $"{(processedChild.PreUidForDb.HasValue ? $"'{processedChild.PreUidForDb}'" : "NULL")}, " +
                    $"{(processedChild.NextUidForDb.HasValue ? $"'{processedChild.NextUidForDb}'" : "NULL")}, " +
                    $"'{dbFormId}', " +
                    $"'{dbFormName}', " +
                    $"'{formSetDetails.id}', " +
                    $"'{formSetDetails.name}', " +
                    $"'{formSetDetails.project_id}', " +
                    $"'{formSetDetails.project_name}', " +
                    $"'{formSetDetails.ward_id}', " +
                    $"'{formSetDetails.ward_name}', " +
                    $"'{formSetDetails.department_id}', " +
                    $"'{formSetDetails.department_name}', " +
                    $"'{formSetDetails.hospital_id}', " +
                    $"'{formSetDetails.hospital_name}', " +
                    $"NOW(), NOW());");
                
                sb.AppendLine();
                
                // 递归处理这个卡片的子项
                ProcessAndGenerateSqlForChildrenOf(
                    processedChild.OriginalItem.Id, // Pass the Id from Items, like "(ID_PART)"
                    processedChild.GeneratedDbId,
                    dbFormId,
                    dbFormName,
                    formSetDetails,
                    sb,
                    allItems
                );
            }
            else if (processedChild.ItemType == "question" && processedChild.OriginalItem.LlmAnalysis != null && processedChild.OriginalItem.LlmAnalysis.MatchFound)
            {
                // 确保问题有匹配的列定义
                var llmAnalysis = processedChild.OriginalItem.LlmAnalysis;

                if (llmAnalysis.MatchedExistingItemId == null || processedChild.OriginalItem.CategoryId == null)
                {
                    continue;
                }
                if (!_tableDefinitionDict.ContainsKey(processedChild.OriginalItem.CategoryId) || !_columnDefinitionDict.ContainsKey(llmAnalysis.MatchedExistingItemId))
                {
                    continue;
                }
                
                // 解析attributes来填充form_question字段
                var parsedAttrs = ParseQuestionAttributes(processedChild.OriginalItem);
                var itemType = processedChild.OriginalItem.ItemType?.Trim() ?? "";
                
                // 根据ItemType设置一些字段
                string displayComponent = null;
                bool? numberIsInteger = null;
                bool? textIsMultipleLine = null;
                bool? selectIsMultipleChoice = null;
                
                if (itemType.Equals("单选", StringComparison.OrdinalIgnoreCase))
                {
                    displayComponent = "radio-row";
                }
                else if (itemType.Equals("多选", StringComparison.OrdinalIgnoreCase))
                {
                    displayComponent = "checkbox-row";
                    selectIsMultipleChoice = true;
                }
                else if (itemType.Equals("长文本", StringComparison.OrdinalIgnoreCase))
                {
                    textIsMultipleLine = true;
                }
                else if (itemType.Equals("整数", StringComparison.OrdinalIgnoreCase))
                {
                    numberIsInteger = true;
                }
                
                // 生成问题的INSERT SQL
                sb.AppendLine("INSERT INTO form.form_question (id, card_id, display_name, column_definition_id, table_definition_id, " +
                    "table_name, column_name, data_type, pre_uid, next_uid, form_id, form_name, form_set_id, form_set_name, project_id, " +
                    "project_name, ward_id, ward_name, department_id, department_name, hospital_id, hospital_name, created_at, updated_at" +
                    (displayComponent != null ? ", display_component" : "") +
                    (parsedAttrs.ContainsKey("dimension_text") ? ", dimension_text" : "") +
                    (parsedAttrs.ContainsKey("is_required") ? ", is_required" : "") +
                    (parsedAttrs.ContainsKey("number_min") ? ", number_min" : "") +
                    (parsedAttrs.ContainsKey("number_max") ? ", number_max" : "") +
                    (parsedAttrs.ContainsKey("select_sorted_option_subset") ? ", select_sorted_option_subset" : "") + 
                    (parsedAttrs.ContainsKey("select_default_option") ? ", select_default_option, has_default_value" : "") +
                    (parsedAttrs.ContainsKey("select_no_others_allowed") ? ", select_no_others_allowed" : "") +
                    (selectIsMultipleChoice.HasValue ? ", select_is_multiple_choice" : "") +
                    (parsedAttrs.ContainsKey("date_format") ? ", date_format" : "") +
                    (numberIsInteger.HasValue ? ", number_is_integer" : "") +
                    (textIsMultipleLine.HasValue ? ", text_is_multiple_line" : "") +
                    ")");
                
                sb.Append($" VALUES ('{processedChild.GeneratedDbId}', " +
                    $"'{dbParentCardId}', " +
                    $"'{inj_dataDictLLMService.CleanDisplayName(processedChild.OriginalItem.DisplayName)}', " +
                    $"'{llmAnalysis.MatchedExistingItemId}', " +
                    $"'{processedChild.OriginalItem.CategoryId}', " +
                    $"'{_tableDefinitionDict[processedChild.OriginalItem.CategoryId]}', " +
                    $"'{_columnDefinitionDict[llmAnalysis.MatchedExistingItemId]}', " +
                    $"'{TypeTranslateGenerator.ConvertQuestionDataType(processedChild.OriginalItem.ItemType)}', " +
                    $"{(processedChild.PreUidForDb.HasValue ? $"'{processedChild.PreUidForDb}'" : "NULL")}, " +
                    $"{(processedChild.NextUidForDb.HasValue ? $"'{processedChild.NextUidForDb}'" : "NULL")}, " +
                    $"'{dbFormId}', " +
                    $"'{dbFormName}', " +
                    $"'{formSetDetails.id}', " +
                    $"'{formSetDetails.name}', " +
                    $"'{formSetDetails.project_id}', " +
                    $"'{formSetDetails.project_name}', " +
                    $"'{formSetDetails.ward_id}', " +
                    $"'{formSetDetails.ward_name}', " +
                    $"'{formSetDetails.department_id}', " +
                    $"'{formSetDetails.department_name}', " +
                    $"'{formSetDetails.hospital_id}', " +
                    $"'{formSetDetails.hospital_name}', " +
                    $"NOW(), NOW()");
                
                // 添加解析出的属性值到SQL中
                if (displayComponent != null)
                {
                    sb.Append($", '{displayComponent}'");
                }
                
                if (parsedAttrs.ContainsKey("dimension_text"))
                {
                    sb.Append($", '{EscapeSqlString(parsedAttrs["dimension_text"] as string)}'");
                }
                
                if (parsedAttrs.ContainsKey("is_required"))
                {
                    sb.Append($", {parsedAttrs["is_required"].ToString().ToLower()}");
                }
                
                if (parsedAttrs.ContainsKey("number_min"))
                {
                    sb.Append($", {parsedAttrs["number_min"]}");
                }
                
                if (parsedAttrs.ContainsKey("number_max"))
                {
                    sb.Append($", {parsedAttrs["number_max"]}");
                }
                
                if (parsedAttrs.ContainsKey("select_sorted_option_subset"))
                {
                    sb.Append($", {FormatStringListToSqlArray(parsedAttrs["select_sorted_option_subset"] as List<string>)}");
                }
                
                if (parsedAttrs.ContainsKey("select_default_option"))
                {
                    sb.Append($", {FormatStringListToSqlArray(parsedAttrs["select_default_option"] as List<string>)}, true");
                }
                
                if (parsedAttrs.ContainsKey("select_no_others_allowed"))
                {
                    sb.Append($", {FormatStringListToSqlArray(parsedAttrs["select_no_others_allowed"] as List<string>)}");
                }
                
                if (selectIsMultipleChoice.HasValue)
                {
                    sb.Append($", {selectIsMultipleChoice.Value.ToString().ToLower()}");
                }
                
                if (parsedAttrs.ContainsKey("date_format"))
                {
                    sb.Append($", '{EscapeSqlString(parsedAttrs["date_format"] as string)}'");
                }
                
                if (numberIsInteger.HasValue)
                {
                    sb.Append($", {numberIsInteger.Value.ToString().ToLower()}");
                }
                
                if (textIsMultipleLine.HasValue)
                {
                    sb.Append($", {textIsMultipleLine.Value.ToString().ToLower()}");
                }
                
                sb.AppendLine(");");
                sb.AppendLine();
            }
        }
    }
    
    // 解析问题属性并返回对应的字段值字典
    private Dictionary<string, object> ParseQuestionAttributes(IDataDictLLMService.ParsedDataItem item)
    {
        var result = new Dictionary<string, object>();
        
        if (item?.Attributes == null || !item.Attributes.Any())
            return result;
        
        foreach (var attr in item.Attributes)
        {
            if (string.IsNullOrWhiteSpace(attr))
                continue;
            
            // 处理"选项:A###B###C"形式的属性
            if (attr.StartsWith("选项:", StringComparison.OrdinalIgnoreCase))
            {
                var options = attr.Substring("选项:".Length).Split("###", StringSplitOptions.RemoveEmptyEntries)
                    .Select(o => o.Trim()).ToList();
                
                if (options.Any())
                {
                    result["select_sorted_option_subset"] = options;
                }
            }
            
            // 处理"默认项:A"形式的属性
            else if (attr.StartsWith("默认项:", StringComparison.OrdinalIgnoreCase))
            {
                var defaultOptions = attr.Substring("默认项:".Length).Split("###", StringSplitOptions.RemoveEmptyEntries)
                    .Select(o => o.Trim()).ToList();
                
                if (defaultOptions.Any())
                {
                    result["select_default_option"] = defaultOptions;
                }
            }
            
            // 处理"排除:A"形式的属性
            else if (attr.StartsWith("排除:", StringComparison.OrdinalIgnoreCase))
            {
                var excludeOptions = attr.Substring("排除:".Length).Split("###", StringSplitOptions.RemoveEmptyEntries)
                    .Select(o => o.Trim()).ToList();
                
                if (excludeOptions.Any())
                {
                    result["select_no_others_allowed"] = excludeOptions;
                }
            }
            
            // 处理"单位:cm"或"单位:天###月###年"形式的属性
            else if (attr.StartsWith("单位:", StringComparison.OrdinalIgnoreCase))
            {
                var units = attr.Substring("单位:".Length).Split("###", StringSplitOptions.RemoveEmptyEntries);
                
                if (units.Length > 0)
                {
                    // 只取第一个单位
                    result["dimension_text"] = units[0].Trim();
                }
            }
            
            // 处理"校验:[0~20]"形式的属性
            else if (attr.StartsWith("校验:", StringComparison.OrdinalIgnoreCase))
            {
                var validationText = attr.Substring("校验:".Length).Trim();
                
                // 解析"[min~max]"格式
                if (validationText.StartsWith("[") && validationText.EndsWith("]") && validationText.Contains("~"))
                {
                    validationText = validationText.Substring(1, validationText.Length - 2); // 去掉[]
                    var parts = validationText.Split('~');
                    
                    if (parts.Length == 2)
                    {
                        if (decimal.TryParse(parts[0].Trim(), out var min))
                        {
                            result["number_min"] = min;
                        }
                        
                        if (decimal.TryParse(parts[1].Trim(), out var max))
                        {
                            result["number_max"] = max;
                        }
                    }
                }
            }
            
            // 处理"格式:YYYY-MM-DD"形式的属性
            else if (attr.StartsWith("格式:", StringComparison.OrdinalIgnoreCase))
            {
                var format = attr.Substring("格式:".Length).Trim();
                
                if (!string.IsNullOrEmpty(format))
                {
                    // 处理日期格式
                    if (format.Contains("YYYY") || format.Contains("yyyy") || 
                        format.Contains("MM") || format.Contains("DD") || format.Contains("dd"))
                    {
                        result["date_format"] = format;
                    }
                }
            }
            
            // 处理"必填"形式的属性
            else if (attr.Equals("必填", StringComparison.OrdinalIgnoreCase))
            {
                result["is_required"] = true;
            }
        }
        
        return result;
    }
    
    // 将字符串列表格式化为SQL数组
    private string FormatStringListToSqlArray(List<string> list)
    {
        if (list == null || !list.Any())
            return "NULL";
            
        var escapedItems = list.Select(item => $"'{EscapeSqlString(item)}'");
        return $"ARRAY[{string.Join(", ", escapedItems)}]::text[]";
    }
    
    // SQL字符串转义
    private string EscapeSqlString(string input)
    {
        if (string.IsNullOrEmpty(input))
            return "";
            
        // 替换单引号为两个单引号（PostgreSQL标准转义）
        return input.Replace("'", "''");
    }
}