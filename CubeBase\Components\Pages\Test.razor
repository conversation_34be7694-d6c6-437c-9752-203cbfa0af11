﻿@page "/Test"
@inject ILogger<Test> Logger

<h3>Bio.AI 流式测试</h3>

<div class="mb-3">
    <button class="btn btn-primary me-2" @onclick="RunGeminiProStreamTestAsync" disabled="@isGeminiProRunning">运行 Gemini 2.5 Flash (Stream)</button>
    <button class="btn btn-secondary me-2" @onclick="RunGeminiFlashStreamTestAsync" disabled="@isGeminiFlashRunning">运行 Gemini Flash 05-20 (Stream)</button>
</div>

@if (isGeminiProRunning || isGeminiFlashRunning)
{
    <div class="spinner-border text-primary mb-3" role="status">
        <span class="visually-hidden">Loading...</span>
    </div>
}

@if (!string.IsNullOrEmpty(errorMessage))
{
    <div class="alert alert-danger">
        <strong>错误:</strong> @errorMessage
    </div>
}

<hr />

<h4>Gemini 2.5 Flash 流式测试结果</h4>
@if (!string.IsNullOrEmpty(geminiProStreamOutput))
{
    <h5>流式输出:</h5>
    <pre>@geminiProStreamOutput</pre>
}
else
{
    <p>尚未运行 Gemini 2.5 Flash 流式测试。</p>
}

<hr />

<h4>Gemini Flash 05-20 流式测试结果</h4>
@if (!string.IsNullOrEmpty(geminiFlashStreamOutput))
{
    <h5>流式输出:</h5>
    <pre>@geminiFlashStreamOutput</pre>
}
else
{
    <p>尚未运行 Gemini Flash 05-20 流式测试。</p>
}

@code {
    private bool isGeminiProRunning = false;
    private bool isGeminiFlashRunning = false;
    private string? errorMessage = null;

    private string? geminiProStreamOutput = null;
    private string? geminiFlashStreamOutput = null;

    private async Task RunGeminiProStreamTestAsync()
    {
        isGeminiProRunning = true;
        errorMessage = null;
        var outputBuilder = new StringBuilder();
        geminiProStreamOutput = ""; // 清空旧输出
        StateHasChanged();

        try
        {
            Logger.LogInformation("开始 Gemini 2.5 Flash 流式测试");

            await foreach (var result in BioAI.ThinkStreamAsync(
                prompt: "快速解释一下什么是API路由？用中文回答。",
                model: LLModel.OpenRouter.Gemini_25_Flash,
                systemPrompt: "你是一个乐于助人的AI助手。",
                onTokenReceived: token =>
                {
                    outputBuilder.Append(token.Content);
                    geminiProStreamOutput = outputBuilder.ToString();
                    InvokeAsync(StateHasChanged);
                }))
            {
                // 流式处理完成
            }
            
            geminiProStreamOutput = outputBuilder.Append("\n[Gemini 2.5 Flash 流式接收完毕]").ToString();
            Logger.LogInformation("Gemini 2.5 Flash 流式测试完成。响应长度: {Length}", outputBuilder.Length);
        }
        catch (Exception ex)
        {            
             Logger.LogError(ex, "Gemini 2.5 Flash 流式测试失败: {ErrorMessage}", ex.Message);
            errorMessage = $"Gemini 2.5 Flash 流式测试失败: {ex.Message}\n{ex.StackTrace}";
        }
        finally
        {
            isGeminiProRunning = false;
            StateHasChanged();
        }
    }

    private async Task RunGeminiFlashStreamTestAsync()
    {
        isGeminiFlashRunning = true;
        errorMessage = null;
        var outputBuilder = new StringBuilder();
        geminiFlashStreamOutput = ""; // 清空旧输出
        StateHasChanged();

        try
        {
            Logger.LogInformation("开始 Gemini Flash 05-20 流式测试");

            await foreach (var result in BioAI.ThinkStreamAsync(
                prompt: "快速解释一下什么是API路由？用中文回答。",
                model: LLModel.OpenRouter.Gemini_25_Flash_05_20,
                systemPrompt: "你是一个简洁明了的AI助手。",
                onTokenReceived: token =>
                {
                    outputBuilder.Append(token.Content);
                    geminiFlashStreamOutput = outputBuilder.ToString();
                    InvokeAsync(StateHasChanged);
                }))
            {
                // 流式处理完成
            }
            
            geminiFlashStreamOutput = outputBuilder.Append("\n[Gemini Flash 05-20 流式接收完毕]").ToString();
             Logger.LogInformation("Gemini Flash 05-20 流式测试完成。响应长度: {Length}", outputBuilder.Length);
        }
        catch (Exception ex)
        {
             Logger.LogError(ex, "Gemini Flash 05-20 流式测试失败: {ErrorMessage}", ex.Message);
            errorMessage = $"Gemini Flash 05-20 流式测试失败: {ex.Message}\n{ex.StackTrace}";
        }
        finally
        {
            isGeminiFlashRunning = false;
            StateHasChanged();
        }
    }
}
