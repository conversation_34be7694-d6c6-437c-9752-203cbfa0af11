using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text.Encodings.Web;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Text.Unicode;
using System.Threading.Tasks;
using DataAccess.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.JSInterop;

namespace BusinessLogic.Cube;

/// <summary>
/// 卡片导出服务类
/// </summary>
public class CardExportService
{
    private readonly ILogger<CardExportService> _logger;
    private readonly IJSRuntime _jsRuntime;
    
    // 需要排除的属性名称
    private readonly HashSet<string> _excludedProperties = new HashSet<string> 
    { 
        "id", "form_id", "parent_id", "card_id", "pre_uid", "next_uid", 
        "created_at", "updated_at", "form_name", "form_set_id", "form_set_name",
        "project_id", "project_name", "ward_id", "ward_name", "department_id", "department_name",
        "hospital_id", "hospital_name", "card_id"
    };

    public CardExportService(ILogger<CardExportService> logger, IJSRuntime jsRuntime)
    {
        _logger = logger;
        _jsRuntime = jsRuntime;
    }

    /// <summary>
    /// 导出卡片到JSON文件
    /// </summary>
    /// <param unique_name="context">数据库上下文</param>
    /// <param unique_name="card">要导出的卡片</param>
    /// <returns>导出是否成功</returns>
    public async Task<bool> ExportCardAsync(CubeContext context, form_card card)
    {
        try
        {
            _logger.LogInformation($"开始导出卡片: {card.name}(ID: {card.id})");

            // 获取完整的卡片信息以确保所有导航属性都已加载
            var fullCard = await LoadFullCardAsync(context, card.id);
            
            if (fullCard == null)
            {
                _logger.LogWarning($"找不到要导出的卡片: {card.id}");
                return false;
            }

            // 创建卡片导出对象
            var exportObject = ConvertToExportObject(fullCard);
            
            // 添加导出元数据
            var metadata = new Dictionary<string, object>
            {
                { "export_time", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss") },
                { "exporter", "CubeSystem" },
                { "version", "2.0" }
            };
            exportObject["metadata"] = metadata;

            // 添加统计信息
            int cardCount = 0;
            int questionCount = 0;
            CountItems(fullCard, ref cardCount, ref questionCount);
            
            exportObject["statistics"] = new Dictionary<string, object>
            {
                { "total_cards", cardCount },
                { "total_questions", questionCount }
            };

            // 序列化为JSON
            var jsonOptions = new JsonSerializerOptions
            {
                WriteIndented = true,
                DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull,
                // 使用不转义中文字符的编码器
                Encoder = JavaScriptEncoder.Create(UnicodeRanges.All)
            };
            var jsonContent = JsonSerializer.Serialize(exportObject, jsonOptions);

            _logger.LogInformation($"已创建卡片导出内容，总大小: {jsonContent.Length} 字节，包含 {cardCount} 个卡片和 {questionCount} 个问题");

            // 生成默认文件名
            var safeFileName = new string(fullCard.name.Where(c => !char.IsWhiteSpace(c) && !char.IsPunctuation(c)).ToArray());
            var defaultFileName = $"卡片_{safeFileName}_{DateTime.Now:yyyyMMdd_HHmmss}.json";
            
            bool result;
            
            try
            {
                // 尝试使用新的对话框方法
                _logger.LogInformation("尝试使用downloadWithPrompt方法导出文件...");
                result = await _jsRuntime.InvokeAsync<bool>(
                    "fileDownloadInterop.downloadWithPrompt",
                    jsonContent,
                    defaultFileName,
                    "application/json");
            }
            catch (JSException ex)
            {
                // 如果新方法不可用，回退到旧方法
                _logger.LogWarning($"downloadWithPrompt方法不可用，回退到downloadFromContent: {ex.Message}");
                
                result = await _jsRuntime.InvokeAsync<bool>(
                    "fileDownloadInterop.downloadFromContent",
                    jsonContent,
                    defaultFileName,
                    "application/json");
            }

            if (result)
            {
                _logger.LogInformation($"卡片导出成功: {fullCard.name}");
            }
            else
            {
                _logger.LogWarning($"文件下载可能未成功完成: {fullCard.name}");
            }
            
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"导出卡片失败: {card.id}");
            return false;
        }
    }

    /// <summary>
    /// 加载完整的卡片数据，包括所有子卡片和问题
    /// </summary>
    private async Task<form_card> LoadFullCardAsync(CubeContext context, Guid cardId)
    {
        // 首先加载顶层卡片及其直接相关的问题
        var card = await context.form_card
            .Include(c => c.form_question)
            .FirstOrDefaultAsync(c => c.id == cardId);

        if (card == null)
        {
            _logger.LogWarning($"找不到卡片: {cardId}");
            return null;
        }

        _logger.LogInformation($"开始递归加载卡片 {card.name} (ID: {card.id}) 的所有子卡片");
        
        // 递归加载所有子卡片及其问题，最大深度设置为20层，防止潜在的无限递归
        const int maxDepth = 20;
        await LoadChildCardsRecursivelyAsync(context, card, 1, maxDepth);

        _logger.LogInformation($"完成加载卡片 {card.name} 的所有子卡片");
        
        return card;
    }

    /// <summary>
    /// 递归加载所有子卡片及其问题
    /// </summary>
    /// <param unique_name="context">数据库上下文</param>
    /// <param unique_name="parentCard">父卡片</param>
    /// <param unique_name="currentDepth">当前深度</param>
    /// <param unique_name="maxDepth">最大深度</param>
    private async Task LoadChildCardsRecursivelyAsync(CubeContext context, form_card parentCard, int currentDepth, int maxDepth)
    {
        if (parentCard == null) return;
        
        // 如果超过最大深度，则不再继续加载
        if (currentDepth > maxDepth)
        {
            _logger.LogWarning($"卡片 {parentCard.name} (ID: {parentCard.id}) 达到最大加载深度 {maxDepth}，停止递归加载");
            return;
        }

        // 加载子卡片
        var childCards = await context.form_card
            .Include(c => c.form_question)
            .Where(c => c.parent_id == parentCard.id)
            .ToListAsync();

        if (childCards.Count > 0)
        {
            _logger.LogInformation($"深度 {currentDepth}: 为卡片 {parentCard.name} 加载了 {childCards.Count} 个子卡片");
        }

        // 设置子卡片集合
        parentCard.Inverseparent = childCards;

        // 递归加载每个子卡片的子卡片
        foreach (var childCard in childCards)
        {
            await LoadChildCardsRecursivelyAsync(context, childCard, currentDepth + 1, maxDepth);
        }
    }

    /// <summary>
    /// 将卡片转换为导出对象
    /// </summary>
    private Dictionary<string, object> ConvertToExportObject(form_card card)
    {
        return ConvertToExportObjectInternal(card, new List<string>(), 1);
    }

    /// <summary>
    /// 将卡片转换为导出对象 - 内部实现，支持层级路径和深度信息
    /// </summary>
    /// <param unique_name="card">要导出的卡片</param>
    /// <param unique_name="path">当前层级路径</param>
    /// <param unique_name="depth">当前深度</param>
    private Dictionary<string, object> ConvertToExportObjectInternal(form_card card, List<string> path, int depth)
    {
        // 创建导出对象
        var exportObject = new Dictionary<string, object>();

        // 添加类型标识和路径信息
        exportObject["type"] = "card";
        exportObject["name"] = card.name;
        
        // 添加深度和路径信息，方便调试和分析
        exportObject["depth"] = depth;
        
        // 添加当前路径
        var currentPath = new List<string>(path);
        currentPath.Add(card.name);
        exportObject["path"] = string.Join(" > ", currentPath);
        
        // 使用反射获取所有基本属性
        var properties = new Dictionary<string, object>();
        GetAllProperties(card, properties);
        
        // 如果有属性，添加到导出对象
        if (properties.Count > 0)
        {
            exportObject["properties"] = properties;
        }
        
        // 获取此卡片容器中的所有项目并按照链表顺序排列
        var orderedItems = GetOrderedItems(card);
        
        // 只有在有子项目时才添加items数组
        if (orderedItems.Count > 0)
        {
            var items = new List<Dictionary<string, object>>();
            
            // 递归处理子卡片和问题
            foreach (var item in orderedItems)
            {
                if (item is form_card childCard)
                {
                    items.Add(ConvertToExportObjectInternal(childCard, currentPath, depth + 1));
                }
                else if (item is form_question question)
                {
                    items.Add(ConvertQuestionToExportObject(question, currentPath, depth));
                }
            }
            
            exportObject["items"] = items;
        }
        
        return exportObject;
    }
    
    /// <summary>
    /// 将问题转换为导出对象
    /// </summary>
    private Dictionary<string, object> ConvertQuestionToExportObject(form_question question)
    {
        return ConvertQuestionToExportObject(question, new List<string>(), 1);
    }
    
    /// <summary>
    /// 将问题转换为导出对象 - 内部实现，支持层级路径和深度信息
    /// </summary>
    private Dictionary<string, object> ConvertQuestionToExportObject(form_question question, List<string> path, int depth)
    {
        // 创建问题导出对象
        var exportObject = new Dictionary<string, object>();
        
        // 添加类型标识和显示名称
        exportObject["type"] = "question";
        exportObject["display_name"] = question.display_name;
        
        // 添加深度信息
        exportObject["depth"] = depth;
        
        // 添加当前路径
        var currentPath = new List<string>(path);
        currentPath.Add(question.display_name);
        exportObject["path"] = string.Join(" > ", currentPath);
        
        // 使用反射获取所有基本属性
        var properties = new Dictionary<string, object>();
        GetAllProperties(question, properties);
        
        // 如果有属性，添加到导出对象
        if (properties.Count > 0)
        {
            exportObject["properties"] = properties;
        }
        
        return exportObject;
    }

    /// <summary>
    /// 获取卡片容器中的有序项目（卡片和问题）
    /// </summary>
    private List<object> GetOrderedItems(form_card container)
    {
        var orderedItems = new List<object>();
        
        // 获取第一个项目（pre_uid 为 null 的项目）
        object firstItem = GetFirstItem(container);
        if (firstItem == null)
        {
            return orderedItems;
        }
        
        orderedItems.Add(firstItem);
        object currentItem = firstItem;
        
        // 根据链表关系获取后续项目
        while (true)
        {
            object nextItem = GetNextItem(container, currentItem);
            if (nextItem == null)
            {
                break;
            }
            
            orderedItems.Add(nextItem);
            currentItem = nextItem;
        }
        
        return orderedItems;
    }

    /// <summary>
    /// 获取容器中的第一个项目
    /// </summary>
    private object GetFirstItem(form_card container)
    {
        var firstCard = container.Inverseparent.FirstOrDefault(c => c.pre_uid == null);
        var firstQuestion = container.form_question.FirstOrDefault(q => q.pre_uid == null);
        
        if (firstCard == null && firstQuestion == null) return null;
        if (firstCard == null) return firstQuestion;
        if (firstQuestion == null) return firstCard;
        
        // 如果卡片和问题都存在，说明存在异常，返回第一个卡片作为起点
        return firstCard;
    }

    /// <summary>
    /// 获取指定项目的下一个项目
    /// </summary>
    private object GetNextItem(form_card container, object currentItem)
    {
        if (currentItem is form_card card)
        {
            if (card.next_uid == null) return null;

            // 先尝试查找下一个卡片
            var nextCard = container.Inverseparent.FirstOrDefault(c => c.id == card.next_uid);
            if (nextCard != null) return nextCard;
            
            // 如果没有找到卡片，查找问题
            return container.form_question.FirstOrDefault(q => q.id == card.next_uid);
        }
        else if (currentItem is form_question question)
        {
            if (question.next_uid == null) return null;
            
            // 尝试查找下一个卡片
            var nextCard = container.Inverseparent.FirstOrDefault(c => c.id == question.next_uid);
            if (nextCard != null) return nextCard;
            
            // 如果没有找到卡片，查找问题
            return container.form_question.FirstOrDefault(q => q.id == question.next_uid);
        }
        
        return null;
    }

    /// <summary>
    /// 使用反射获取对象的所有属性
    /// </summary>
    private void GetAllProperties(object obj, Dictionary<string, object> properties)
    {
        if (obj == null) return;

        // 获取所有公共属性
        var propertyInfos = obj.GetType().GetProperties(BindingFlags.Public | BindingFlags.Instance);
        
        foreach (var propertyInfo in propertyInfos)
        {
            var propertyName = propertyInfo.Name.ToLower();
            
            // 跳过被排除的属性
            if (_excludedProperties.Contains(propertyName))
                continue;
                
            // 特殊处理List<string>类型，这是Postgres数组类型在C#中的表示
            bool isListOfString = propertyInfo.PropertyType.IsGenericType && 
                                 propertyInfo.PropertyType.GetGenericTypeDefinition() == typeof(List<>) &&
                                 propertyInfo.PropertyType.GetGenericArguments()[0] == typeof(string);
            
            // 跳过导航属性和集合属性，但保留List<string>类型
            if (!isListOfString && ((propertyInfo.PropertyType.IsClass && 
                propertyInfo.PropertyType != typeof(string) && 
                !propertyInfo.PropertyType.IsValueType) ||
                propertyInfo.PropertyType.IsGenericType && 
                propertyInfo.PropertyType.GetGenericTypeDefinition() == typeof(ICollection<>)))
                continue;
                
            // 获取属性值
            var value = propertyInfo.GetValue(obj);
            
            // 如果值不为null，添加到字典
            if (value != null)
            {
                // 保持属性的原始名称格式
                properties[propertyName] = value;
            }
        }
    }

    /// <summary>
    /// 统计卡片中包含的子卡片和问题数量
    /// </summary>
    private void CountItems(form_card card, ref int cardCount, ref int questionCount)
    {
        if (card == null) return;
        
        // 计算当前卡片
        cardCount++;
        
        // 计算问题数量
        questionCount += card.form_question?.Count ?? 0;
        
        // 递归计算子卡片
        if (card.Inverseparent != null)
        {
            foreach (var childCard in card.Inverseparent)
            {
                CountItems(childCard, ref cardCount, ref questionCount);
            }
        }
    }
} 