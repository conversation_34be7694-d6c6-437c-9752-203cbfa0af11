﻿@inherits Modal_Base

<MudDialog>
    <TitleContent>
        <MudText Typo="Typo.h6">@MudDialog.Title</MudText>
    </TitleContent>
    <DialogContent>
        <MudForm @ref="form" @bind-IsValid="@success" @bind-Errors="@errors">
            <MudStack Orientation="Orientation.Vertical" Spacing="2">
                <MudTextField T="string" Label="医院名称" Value="@_selectedProject.hospital_name" Disabled />
                <MudTextField T="string" Label="科室名称" Value="@_selectedProject.department_name" Disabled />
                <MudTextField T="string" Label="病区名称" Value="@_selectedProject.ward_name" Disabled />
                <MudTextField T="string" Label="项目名称" Value="@_selectedProject.name" Disabled />
                <MudTextField T="string" Label="表单集名称" Required="true" RequiredError="表单集名称不能为空" @bind-Value="FormsetName" />
                <MudTextField T="string" Label="表单集描述" @bind-Value="FormsetDescription" />
                <MudSelect T="string" Label="表单集类型" @bind-Value="FormsetType" Required="true" RequiredError="表单集类型不能为空">
                    <MudSelectItem Value="@("care")">Care</MudSelectItem>
                    <MudSelectItem Value="@("followup")">病患随访</MudSelectItem>
                </MudSelect>
            </MudStack>
        </MudForm>
    </DialogContent>
    <DialogActions>
        <MudButton Variant="Variant.Filled" OnClick="Cancel">取消</MudButton>
        <MudButton Variant="Variant.Filled" Color="Color.Primary" OnClick="@(async () => await Submit())">保存</MudButton>
    </DialogActions>
</MudDialog>

@code {
    [CascadingParameter]
    private IMudDialogInstance MudDialog { get; set; }
    [Parameter]
    public form_project? _selectedProject { get; set; }
    [Parameter]
    public form_form_set? _selectedFormSet { get; set; }
    [Parameter]
    public EventCallback OnFormSetModalClosed { get; set; }
    private MudForm? form;
    private bool success = false;
    private string[] errors = [];
    private string FormsetName = "";
    private string FormsetDescription = "";
    private string FormsetType = "";

    private void Cancel() => MudDialog.Cancel();

    private async Task Submit()
    {
        await form.Validate();

        if (form.IsValid)
        {
            _selectedFormSet = new form_form_set
            {
                id = SequentialGuidGenerator.NewGuid(),
                name = FormsetName,
                description = FormsetDescription,
                project_id = _selectedProject.id,
                project_name = _selectedProject.name,
                hospital_id = _selectedProject.hospital_id,
                hospital_name = _selectedProject.hospital_name,
                department_id = _selectedProject.department_id,
                department_name = _selectedProject.department_name,
                ward_id = _selectedProject.ward_id,
                ward_name = _selectedProject.ward_name,
                type = FormsetType
            };
            await context.form_form_set.AddAsync(_selectedFormSet);
            await context.SaveChangesAsync();
            
            if (OnFormSetModalClosed.HasDelegate)
            {
                await OnFormSetModalClosed.InvokeAsync();
            }
            
            MudDialog.Close(MudBlazor.DialogResult.Ok(true));
        }
    }
}