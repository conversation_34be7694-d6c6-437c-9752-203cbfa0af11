@inherits BasePage
@inject ILogger<DataDictFormGrid> Logger

<MudGrid Spacing="0" Class="table-grid" Style="flex: 0 0 auto;">
    <MudItem xs="12" sm="12" md="6" lg="6" xl="6" Class="pa-1">
        <div style="border: 1px solid #e0e0e0; margin: 0; padding: 0;">
            <div style="background-color: #f5f5f5; padding: 2px 5px; font-size: 16px; border-bottom: 1px solid #e0e0e0;">
                表单结构输入框
            </div>
            <MudTextField T="string"
            @bind-Value="FormStructureInputText"
            Lines="12"
            Variant="Variant.Outlined"
            Placeholder="请在此处输入或粘贴表单结构文本..."
            Immediate="true"
            Multiline="true"
            Style="font-size: 11px; margin: 0; padding: 0; border-radius: 0;"/>
        </div>
    </MudItem>
    <MudItem xs="12" sm="12" md="6" lg="6" xl="6" Class="pa-1">
        <div style="border: 1px solid #e0e0e0; margin: 0; padding: 0;">
            <div style="background-color: #f5f5f5; padding: 2px 5px; font-size: 16px; border-bottom: 1px solid #e0e0e0; display: flex; justify-content: space-between; align-items: center;">
                <span>数据项匹配结果输入框</span>
                <div>
                    <MudButton Variant="Variant.Filled"
                    Color="Color.Primary"
                    OnClick="RunLlmFieldAnalysisAsync"
                    Size="Size.Small"
                    Style="margin-right: 5px;"
                    Disabled="@_isLlmAnalysisInProgress">
                        @if (_isLlmAnalysisInProgress)
                        {
                            <MudProgressCircular Class="ms-n1" Size="Size.Small" Indeterminate="true" />
                            <span class="ms-2">分析中...</span>
                        }
                        else
                        {
                            <span>开始LLM分析</span>
                        }
                    </MudButton>
                    <MudButton Variant="Variant.Filled"
                    Color="Color.Success"
                    OnClick="HandleProcessMatchResults"
                    Size="Size.Small"
                    Style="margin-right: 5px;">
                        解析并更新
                    </MudButton>
                </div>
            </div>
            <MudTextField T="string"
            @bind-Value="MatchResultInputText"
            Lines="12"
            Variant="Variant.Outlined"
            Placeholder="请输入文本... "
            Immediate="true"
            Multiline="true"
            Style="font-size: 11px; margin: 0; padding: 0; border-radius: 0;"/>
        </div>
    </MudItem>
</MudGrid>

<DataDictItemsTable Items="ParsedItems" OnShowCategoryData="ShowCategoryData" />

@code {
    private string _formStructureInputText = string.Empty;

    public string FormStructureInputText
    {
        get => _formStructureInputText;
        set
        {
            if (_formStructureInputText != value)
            {
                _formStructureInputText = value;
                ParsedItems = ParseData(_formStructureInputText);
                StateHasChanged();
            }
        }
    }

    private string _matchResultInputText = string.Empty;

    public string MatchResultInputText
    {
        get => _matchResultInputText;
        set
        {
            if (_matchResultInputText != value)
            {
                _matchResultInputText = value;
                StateHasChanged();
            }
        }
    }

    private bool _isLlmAnalysisInProgress = false;

    private string _categoryDataDialogContent = string.Empty;
    private bool _categoryDataDialogVisible = false;

    protected override async Task OnInitializedAsync()
    {
        await LoadInitialDataAsync();
    }

    private async Task LoadInitialDataAsync()
    {
        try
        {
            if (inj_Env is not null)
            {
                string list1Path = Path.Combine(inj_Env.ContentRootPath, "TempFiles", "list1.txt");
                string list2Path = Path.Combine(inj_Env.ContentRootPath, "TempFiles", "list2.txt");

                string list1Content = await File.ReadAllTextAsync(list1Path);
                string list2Content = await File.ReadAllTextAsync(list2Path);

                _formStructureInputText = list1Content;
                _matchResultInputText = list2Content;

                ParsedItems = ParseData(_formStructureInputText);
                ProcessMatchResults(_matchResultInputText);

                StateHasChanged();
                Logger.LogInformation("成功加载并处理了初始数据 list1.txt 和 list2.txt");
            }
            else
            {
                Logger.LogWarning("inj_Env 为空，无法加载初始数据");
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "加载初始数据时发生错误");
            inj_snackbar.Add("加载初始数据失败", Severity.Error);
        }
    }

    private void HandleProcessMatchResults()
    {
        ProcessMatchResults(MatchResultInputText);
        StateHasChanged();

        try
        {
            int originalCount = ParsedItems.Count;
            int matchedCount = ParsedItems.Count(item => !string.IsNullOrEmpty(item.MatchResult));
            inj_snackbar.Add($"匹配结果处理完成，共 {originalCount} 个数据项，成功匹配 {matchedCount} 项", Severity.Success);
            Logger.LogInformation($"匹配结果处理完成，共 {originalCount} 个数据项，成功匹配 {matchedCount} 项");
        }
        catch (Exception ex)
        {
            inj_snackbar.Add($"处理匹配结果通知时发生错误: {ex.Message}", Severity.Error);
            Logger.LogError(ex, "处理匹配结果通知时发生错误");
        }
    }

    // 使用 IDataDictLLMService 中定义的类型
    public List<IDataDictLLMService.ParsedDataItem> ParsedItems { get; set; } = new List<IDataDictLLMService.ParsedDataItem>();

    /// <summary>
    /// 解析表单结构输入文本，提取表单数据项
    /// </summary>
    public List<IDataDictLLMService.ParsedDataItem> ParseData(string inputText)
    {
        var items = new List<IDataDictLLMService.ParsedDataItem>();
        if (string.IsNullOrWhiteSpace(inputText)) return items;

        var lines = inputText.Split(new[] { "\r\n", "\r", "\n" }, StringSplitOptions.RemoveEmptyEntries);

        // 使用静态正则表达式以提高性能
        var regex = new System.Text.RegularExpressions.Regex(@"^(?<name_indent>.*?)\s*\[(?<type>[^\]]+?)\]\s*\((?<id>[^)]+?)\)(?<attrs_str>.*)$");
        var attrRegex = new System.Text.RegularExpressions.Regex(@"\[([^\]]+?)\]");

        foreach (var line in lines)
        {
            if (string.IsNullOrWhiteSpace(line)) continue;

            var match = regex.Match(line);

            if (match.Success)
            {
                // 使用对象初始化器简化代码
                var item = new IDataDictLLMService.ParsedDataItem
                {
                    DisplayName = match.Groups["name_indent"].Value.TrimEnd(),
                    ItemType = match.Groups["type"].Value,
                    Id = $"({match.Groups["id"].Value})"
                };

                // 提取属性
                string attributesString = match.Groups["attrs_str"].Value.Trim();
                if (!string.IsNullOrEmpty(attributesString))
                {
                    var attrMatches = attrRegex.Matches(attributesString);
                    foreach (System.Text.RegularExpressions.Match attrMatch in attrMatches)
                    {
                        item.Attributes.Add(attrMatch.Groups[1].Value);
                    }
                }
                items.Add(item);
            }
            else
            {
                Logger.LogWarning($"无法解析此行数据，已跳过: {line}");
            }
        }
        return items;
    }

    /// <summary>
    /// 格式化属性列表，使属性名称加粗，使用全角冒号
    /// </summary>
    private string FormatAttributes(List<string> attributes)
    {
        if (attributes == null || !attributes.Any())
            return string.Empty;

        var result = new System.Text.StringBuilder();

        foreach (var attr in attributes)
        {
            // 检查属性是否包含冒号
            int colonIndex = attr.IndexOf(':');
            if (colonIndex > 0)
            {
                // 提取属性名称和值
                string name = attr.Substring(0, colonIndex).Trim();
                string value = attr.Substring(colonIndex + 1).Trim();

                // 添加带有粗体的属性名称和全角冒号
                result.AppendFormat("<span class=\"attr-name\">{0}</span>：{1}<br />", name, value);
            }
            else
            {
                // 如果没有冒号，直接添加原始属性
                result.AppendFormat("{0}<br />", attr);
            }
        }

        return result.ToString();
    }

    /// <summary>
    /// 匹配结果中间对象，用于存储解析后的匹配结果数据
    /// </summary>
    private class MatchResultItem
    {
        public string FormPath { get; set; } = string.Empty;
        public string MatchType { get; set; } = string.Empty;
        public string NodeId { get; set; } = string.Empty;
        public string ChineseName { get; set; } = string.Empty;
        public string EnglishName { get; set; } = string.Empty;
        public string SuggestPath { get; set; } = string.Empty;
        public string SuggestEnglishName { get; set; } = string.Empty;
        public string SuggestChineseName { get; set; } = string.Empty;
    }

    // 用于提取基本字段的正则表达式
    private static readonly System.Text.RegularExpressions.Regex BasicFieldRegex =
        new System.Text.RegularExpressions.Regex(
            @"([^|]+)\|([^|]*)\|([^|]+)\|(?:T_NODE:)?T:\(([^)]+)\)([^|]*)\|([^|]*)",
            System.Text.RegularExpressions.RegexOptions.Compiled);

    // 用于提取SUGGEST_NEW_T路径和英文名称的正则表达式
    private static readonly System.Text.RegularExpressions.Regex SuggestNewTRegex =
        new System.Text.RegularExpressions.Regex(
            @"SUGGEST_NEW_T:PATH:([^|]+)(?:\|([^|]+))+\(([^)]+)\)",
            System.Text.RegularExpressions.RegexOptions.Compiled);

    /// <summary>
    /// 处理匹配结果输入文本，更新ParsedDataItem对象
    /// </summary>
    private void ProcessMatchResults(string inputText)
    {
        if (string.IsNullOrWhiteSpace(inputText))
        {
            Logger.LogWarning("匹配结果输入文本为空，无法处理");
            return;
        }

        // 添加调试日志
        Logger.LogInformation($"开始处理匹配结果，输入文本长度: {inputText.Length}");

        // 解析匹配结果输入文本，提取匹配结果信息
        var matchResults = ParseMatchResults(inputText);
        Logger.LogInformation($"解析出 {matchResults.Count} 条匹配结果");

        if (matchResults.Count == 0)
        {
            Logger.LogWarning("未能从输入文本中解析出有效的匹配结果");
            return;
        }

        // 创建一个字典，用于快速查找匹配结果
        var matchResultDict = new Dictionary<string, MatchResultItem>(StringComparer.OrdinalIgnoreCase);

        // 添加匹配结果到字典
        foreach (var result in matchResults)
        {
            // 记录每个结果的关键信息
            Logger.LogInformation($"处理匹配结果: FormPath={result.FormPath}, MatchType={result.MatchType}, NodeId={result.NodeId}");

            if (!string.IsNullOrEmpty(result.FormPath) && !matchResultDict.ContainsKey(result.FormPath))
            {
                matchResultDict.Add(result.FormPath, result);
                Logger.LogInformation($"添加匹配结果到字典，路径: {result.FormPath}");
            }
            else if (string.IsNullOrEmpty(result.FormPath))
            {
                Logger.LogWarning($"匹配结果的FormPath为空，无法添加到字典");
            }
            else
            {
                Logger.LogWarning($"字典中已存在路径为 {result.FormPath} 的匹配结果，忽略重复项");
            }
        }

        // 更新ParsedDataItem对象的匹配结果
        int matchCount = 0;
        foreach (var item in ParsedItems)
        {
            // 从ID中提取编号（去掉括号）
            string itemId = item.Id.Trim('(', ')').Replace(" ","");
            Logger.LogInformation($"处理表单项 ID: {itemId}, 原始ID: {item.Id}");

            // 检查ID中的'-'个数是否等于3个
            int dashCount = itemId.Count(c => c == '-');

            // 只有当ID中的'-'个数等于3个时才进行匹配
            if (dashCount == 3)
            {
                // 查找对应的匹配结果
                if (matchResultDict.TryGetValue(itemId, out var matchResult))
                {
                    // 根据匹配类型生成不同的匹配结果HTML
                    item.MatchResult = FormatMatchResult(matchResult);
                    
                    // 设置匹配类型
                    item.MatchType = matchResult.MatchType;

                    // 设置CategoryId，用于后续LLM分析
                    if (!string.IsNullOrEmpty(matchResult.NodeId))
                    {
                        item.CategoryId = matchResult.NodeId;
                        Logger.LogInformation($"设置表单项 {itemId} 的分类ID: {matchResult.NodeId}");
                    }

                    Logger.LogInformation($"为表单项 {itemId} 找到匹配结果: {matchResult.MatchType}");
                    matchCount++;
                }
                else
                {
                    Logger.LogWarning($"未找到表单项 {itemId} 的匹配结果");
                }
            }
            else
            {
                Logger.LogInformation($"表单项 {itemId} 的ID中'-'个数不等于3个（实际为{dashCount}个），跳过匹配");
            }
        }

        Logger.LogInformation($"匹配结果处理完成，共处理 {ParsedItems.Count} 个表单项，成功匹配 {matchCount} 项");

        // 强制更新UI
        StateHasChanged();
    }

    /// <summary>
    /// 解析匹配结果输入文本，提取匹配结果信息
    /// </summary>
    private List<MatchResultItem> ParseMatchResults(string inputText)
    {
        var results = new List<MatchResultItem>();
        var lines = inputText.Split(new[] { "\r\n", "\r", "\n" }, StringSplitOptions.RemoveEmptyEntries);

        foreach (var line in lines)
        {
            if (string.IsNullOrWhiteSpace(line)) continue;

            // 解析匹配结果行
            var matchResultItem = ParseMatchResultLine(line);
            if (matchResultItem != null)
            {
                results.Add(matchResultItem);
            }
            else
            {
                Logger.LogWarning($"无法解析匹配结果行: {line}");
            }
        }

        return results;
    }

    /// <summary>
    /// 解析单行匹配结果文本
    /// </summary>
    private MatchResultItem? ParseMatchResultLine(string line)
    {
        try
        {
            // 记录原始输入行
            Logger.LogInformation($"开始解析匹配结果行: {line}");

            // 首先提取基本字段
            var basicMatch = BasicFieldRegex.Match(line);
            if (!basicMatch.Success)
            {
                // 尝试检查是否是SUGGEST_NEW_T格式
                if (line.Contains("SUGGEST_NEW_T:PATH:"))
                {
                    // 创建一个特殊的SUGGEST_NEW_T结果项
                    var suggestItem = new MatchResultItem { MatchType = "SUGGEST_NEW_T" };

                    // 尝试提取表单路径（第一个竖线之前的内容）
                    int firstPipeIndex = line.IndexOf('|');
                    if (firstPipeIndex > 0)
                    {
                        string rawFormPath = line.Substring(0, firstPipeIndex).Trim();
                        // 从rawFormPath中提取formPath（处理带括号和不带括号的情况）
                        string extractedPath = System.Text.RegularExpressions.Regex.Match(rawFormPath, @"\(([^)]+)\)").Success
                            ? System.Text.RegularExpressions.Regex.Match(rawFormPath, @"\(([^)]+)\)").Groups[1].Value.Trim().Replace(" ", "")
                            : rawFormPath.Replace(" ", "");

                        suggestItem.FormPath = extractedPath;
                        Logger.LogInformation($"从SUGGEST_NEW_T行中提取表单路径: {extractedPath}");
                    }

                    // 提取SUGGEST_NEW_T:PATH:后面的内容
                    var suggestNewTPath = line.IndexOf("SUGGEST_NEW_T:PATH:");
                    if (suggestNewTPath > 0)
                    {
                        // 提取路径部分（从"SUGGEST_NEW_T:PATH:"之后开始）
                        var startIndex = suggestNewTPath + "SUGGEST_NEW_T:PATH:".Length;
                        var fullPath = line.Substring(startIndex).Trim();

                        var parts = fullPath.Split('|');
                        
                        // Initialize to empty strings, these will be overridden if parts are found
                        suggestItem.SuggestPath = string.Empty;
                        suggestItem.SuggestEnglishName = string.Empty;
                        suggestItem.SuggestChineseName = string.Empty;
                        
                        // parts.Length will be at least 1 if fullPath is not null (e.g. "" gives [""])
                        // So we primarily care if the content of parts is meaningful.

                        // Last element is SuggestChineseName
                        string rawChineseName = parts.Last().Trim();
                        var chineseNameMatch = System.Text.RegularExpressions.Regex.Match(rawChineseName, @"^\((.*)\)$");
                        suggestItem.SuggestChineseName = chineseNameMatch.Success ? chineseNameMatch.Groups[1].Value.Trim() : rawChineseName;

                        if (parts.Length >= 2)
                        {
                            // Second to last is SuggestEnglishName
                            string rawEnglishName = parts[parts.Length - 2].Trim();
                            var englishNameMatch = System.Text.RegularExpressions.Regex.Match(rawEnglishName, @"^\((.*)\)$");
                            suggestItem.SuggestEnglishName = englishNameMatch.Success ? englishNameMatch.Groups[1].Value.Trim() : rawEnglishName;
                        }
                        
                        if (parts.Length > 2) 
                        {
                            suggestItem.SuggestPath = string.Join("|", parts.Take(parts.Length - 2).Select(p => p.Trim())).Trim();
                        }
                        
                        Logger.LogInformation($"提取SUGGEST_NEW_T字段: 路径='{suggestItem.SuggestPath}', 英文名='{suggestItem.SuggestEnglishName}', 中文名='{suggestItem.SuggestChineseName}' from fullPath='{fullPath}'");
                    }

                    return suggestItem;
                }

                // 尝试检查是否是REQUIRE_NEW_T格式
                if (line.Contains("REQUIRE_NEW_T:PATH:"))
                {
                    // 创建一个特殊的REQUIRE_NEW_T结果项
                    var requireItem = new MatchResultItem { MatchType = "REQUIRE_NEW_T" };

                    // 尝试提取表单路径（第一个竖线之前的内容）
                    int firstPipeIndex = line.IndexOf('|');
                    if (firstPipeIndex > 0)
                    {
                        string rawFormPath = line.Substring(0, firstPipeIndex).Trim();
                        // 从rawFormPath中提取formPath（处理带括号和不带括号的情况）
                        string extractedPath = System.Text.RegularExpressions.Regex.Match(rawFormPath, @"\(([^)]+)\)").Success
                            ? System.Text.RegularExpressions.Regex.Match(rawFormPath, @"\(([^)]+)\)").Groups[1].Value.Trim().Replace(" ", "")
                            : rawFormPath.Replace(" ", "");

                        requireItem.FormPath = extractedPath;
                        Logger.LogInformation($"从REQUIRE_NEW_T行中提取表单路径: {extractedPath}");
                    }

                    // 提取第二个字段作为中文名称
                    var parts = line.Split('|');
                    if (parts.Length >= 2)
                    {
                        requireItem.ChineseName = parts[1].Trim().TrimEnd('*'); // 移除末尾的*号
                        Logger.LogInformation($"从REQUIRE_NEW_T行中提取中文名称: {requireItem.ChineseName}");
                    }

                    // 提取REQUIRE_NEW_T:PATH:后面的内容
                    var requireNewTPath = line.IndexOf("REQUIRE_NEW_T:PATH:");
                    if (requireNewTPath >= 0)
                    {
                        // 提取路径部分（从"REQUIRE_NEW_T:PATH:"之后开始）
                        var startIndex = requireNewTPath + "REQUIRE_NEW_T:PATH:".Length;
                        var fullPath = line.Substring(startIndex).Trim();

                        var pathParts = fullPath.Split('|');
                        
                        // 设置建议路径和名称
                        requireItem.SuggestPath = string.Empty;
                        requireItem.SuggestEnglishName = string.Empty;
                        requireItem.SuggestChineseName = string.Empty;
                        
                        // Last element is SuggestChineseName
                        if (pathParts.Length > 0)
                        {
                            string rawChineseName = pathParts.Last().Trim();
                            requireItem.SuggestChineseName = rawChineseName;
                        }

                        if (pathParts.Length >= 2)
                        {
                            // Second to last is SuggestEnglishName
                            string rawEnglishName = pathParts[pathParts.Length - 2].Trim();
                            requireItem.SuggestEnglishName = rawEnglishName;
                        }
                        
                        if (pathParts.Length > 2) 
                        {
                            requireItem.SuggestPath = string.Join("|", pathParts.Take(pathParts.Length - 2).Select(p => p.Trim())).Trim();
                        }
                        
                        Logger.LogInformation($"提取REQUIRE_NEW_T字段: 路径='{requireItem.SuggestPath}', 英文名='{requireItem.SuggestEnglishName}', 中文名='{requireItem.SuggestChineseName}' from fullPath='{fullPath}'");
                    }

                    return requireItem;
                }

                Logger.LogWarning($"基本字段提取失败: {line}");
                return null;
            }

            // 提取基本字段
            string formPathRaw = basicMatch.Groups[1].Value.Trim();
            string matchType = basicMatch.Groups[3].Value.Trim();
            string nodeId = basicMatch.Groups[4].Value.Trim().Replace(" ", ""); // 确保节点ID中不包含空格
            string englishName = basicMatch.Groups[5].Value.Trim();
            string chineseName = basicMatch.Groups[6].Value.Trim();

            // 记录提取的字段
            Logger.LogInformation($"提取的基本字段: formPathRaw={formPathRaw}, matchType={matchType}, nodeId={nodeId}, englishName={englishName}, chineseName={chineseName}");

            // 从formPathRaw中提取formPath（处理带括号和不带括号的情况）
            string formPath = System.Text.RegularExpressions.Regex.Match(formPathRaw, @"\(([^)]+)\)").Success
                ? System.Text.RegularExpressions.Regex.Match(formPathRaw, @"\(([^)]+)\)").Groups[1].Value.Trim().Replace(" ", "")
                : formPathRaw.Replace(" ", "");

            var resultItem = new MatchResultItem
            {
                FormPath = formPath,
                MatchType = matchType,
                NodeId = nodeId,
                EnglishName = englishName,
                ChineseName = chineseName
            };

            // 处理SUGGEST_NEW_T特定字段
            if (matchType == "SUGGEST_NEW_T")
            {
                // 直接匹配完整的字符串以提取SUGGEST_NEW_T:PATH:之后的所有内容和括号中的英文名称
                var suggestNewTPath = line.IndexOf("SUGGEST_NEW_T:PATH:");
                if (suggestNewTPath > 0)
                {
                    // 提取路径部分（从"SUGGEST_NEW_T:PATH:"之后开始）
                    var startIndex = suggestNewTPath + "SUGGEST_NEW_T:PATH:".Length;
                    var fullPath = line.Substring(startIndex).Trim();

                    var parts = fullPath.Split('|');
                    
                    // Initialize to empty strings, these will be overridden if parts are found
                    resultItem.SuggestPath = string.Empty;
                    resultItem.SuggestEnglishName = string.Empty;
                    resultItem.SuggestChineseName = string.Empty;
                    
                    // parts.Length will be at least 1 if fullPath is not null (e.g. "" gives [""])
                    // So we primarily care if the content of parts is meaningful.

                    // Last element is SuggestChineseName
                    string rawChineseName = parts.Last().Trim();
                    var chineseNameMatch = System.Text.RegularExpressions.Regex.Match(rawChineseName, @"^\((.*)\)$");
                    resultItem.SuggestChineseName = chineseNameMatch.Success ? chineseNameMatch.Groups[1].Value.Trim() : rawChineseName;

                    if (parts.Length >= 2)
                    {
                        // Second to last is SuggestEnglishName
                        string rawEnglishName = parts[parts.Length - 2].Trim();
                        var englishNameMatch = System.Text.RegularExpressions.Regex.Match(rawEnglishName, @"^\((.*)\)$");
                        resultItem.SuggestEnglishName = englishNameMatch.Success ? englishNameMatch.Groups[1].Value.Trim() : rawEnglishName;
                    }
                    
                    if (parts.Length > 2) 
                    {
                        resultItem.SuggestPath = string.Join("|", parts.Take(parts.Length - 2).Select(p => p.Trim())).Trim();
                    }
                    
                    Logger.LogInformation($"提取SUGGEST_NEW_T字段: 路径='{resultItem.SuggestPath}', 英文名='{resultItem.SuggestEnglishName}', 中文名='{resultItem.SuggestChineseName}' from fullPath='{fullPath}'");
                }
                else
                {
                    Logger.LogWarning($"未能从SUGGEST_NEW_T行中提取出建议路径: {line}");
                }
            }

            Logger.LogInformation($"成功解析匹配结果行: FormPath={resultItem.FormPath}, MatchType={resultItem.MatchType}, NodeId={resultItem.NodeId}");
            return resultItem;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, $"解析匹配结果行时发生错误: {line}");
            return null;
        }
    }

    /// <summary>
    /// 根据匹配类型格式化匹配结果HTML
    /// </summary>
    private string FormatMatchResult(MatchResultItem matchResult)
    {
        var sb = new System.Text.StringBuilder();

        // 记录正在格式化的匹配结果
        Logger.LogInformation($"格式化匹配结果: MatchType={matchResult.MatchType}, NodeId={matchResult.NodeId}");

        // 节点ID行 - 直接显示ID，不再显示匹配类型和T:前缀
        sb.Append("<div class=\"match-type-line\">");

        // 显示匹配类型
        string typeClass = matchResult.MatchType == "MATCH_T" ? "match-type" :
                          matchResult.MatchType == "SUGGEST_NEW_T" ? "suggest-type" :
                          matchResult.MatchType == "REQUIRE_T" ? "require-type" :
                          matchResult.MatchType == "REQUIRE_NEW_T" ? "require-type" : "match-type";

        // 添加节点ID
        if (!string.IsNullOrEmpty(matchResult.NodeId))
        {
            // 确保显示的节点ID不包含空格
            string displayNodeId = matchResult.NodeId.Replace(" ", "");
            sb.Append($"<span class=\"node-id\">{displayNodeId}</span>");
        }
        sb.Append("</div>");

        // 名称行 - 对所有类型都相同
        if (!string.IsNullOrEmpty(matchResult.ChineseName) || !string.IsNullOrEmpty(matchResult.EnglishName))
        {
            sb.Append("<div class=\"match-name-line\">");
            if (!string.IsNullOrEmpty(matchResult.ChineseName) && !string.IsNullOrEmpty(matchResult.EnglishName))
            {
                sb.Append($"{matchResult.ChineseName} | {matchResult.EnglishName}");
            }
            else if (!string.IsNullOrEmpty(matchResult.ChineseName))
            {
                sb.Append(matchResult.ChineseName);
            }
            else if (!string.IsNullOrEmpty(matchResult.EnglishName))
            {
                sb.Append(matchResult.EnglishName);
            }
            sb.Append("</div>");
        }

        // 建议路径行 - 对 SUGGEST_NEW_T 和 REQUIRE_NEW_T 类型
        if ((matchResult.MatchType == "SUGGEST_NEW_T" || matchResult.MatchType == "REQUIRE_NEW_T") && !string.IsNullOrEmpty(matchResult.SuggestPath))
        {
            sb.Append("<div class=\"suggest-name-line\">");
            sb.Append(matchResult.SuggestPath);

            if (!string.IsNullOrEmpty(matchResult.SuggestEnglishName))
            {
                sb.Append($" ({matchResult.SuggestEnglishName})");
            }

            sb.Append("</div>");
        }

        string result = sb.ToString();
        Logger.LogInformation($"格式化结果: {result}");
        return result;
    }

    /// <summary>
    /// 使用LLM分析表单字段是否在数据字典中已存在
    /// </summary>
    private async Task RunLlmFieldAnalysisAsync()
    {
        try
        {
            // 如果已经有分析在进行中，则返回
            if (_isLlmAnalysisInProgress)
            {
                inj_snackbar.Add("正在进行LLM分析，请稍后...", Severity.Warning);
                return;
            }

            _isLlmAnalysisInProgress = true;
            StateHasChanged();

            // 记录开始分析
            Logger.LogInformation("开始LLM表单字段分析");

            // 筛选需要LLM分析的项目（有MatchResult且CategoryId不为空的项目）
            var itemsToAnalyze = ParsedItems
                .Where(item => !string.IsNullOrEmpty(item.MatchResult) &&
                               !string.IsNullOrEmpty(item.CategoryId) &&
                               (item.LlmAnalysis == null || !item.LlmAnalysis.IsProcessed))
                .ToList();

            if (!itemsToAnalyze.Any())
            {
                inj_snackbar.Add("没有找到需要进行LLM分析的表单字段", Severity.Info);
                _isLlmAnalysisInProgress = false;
                StateHasChanged();
                return;
            }

            // 调用服务进行LLM分析
            await inj_dataDictLLMService.RunLlmFieldAnalysisAsync(itemsToAnalyze, ParsedItems, Logger);

            // 更新UI
            _isLlmAnalysisInProgress = false;
            Logger.LogInformation("所有LLM分析任务已完成");

            // 统计结果
            var totalItems = itemsToAnalyze.Count;
            var matchedItems = ParsedItems.Count(i => i.LlmAnalysis?.MatchFound == true);
            var potentialMatchItems = ParsedItems.Count(i => i.LlmAnalysis?.PotentialMatchTypeMismatch == true);
            var errorItems = ParsedItems.Count(i => i.LlmAnalysis != null && !string.IsNullOrEmpty(i.LlmAnalysis.ErrorMessage));

            inj_snackbar.Add($"LLM分析完成: 共分析{totalItems}个字段，匹配{matchedItems}个，潜在匹配但类型不同{potentialMatchItems}个，错误{errorItems}个", Severity.Success);

            // 强制刷新UI
            StateHasChanged();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "执行LLM分析过程中发生错误");
            inj_snackbar.Add($"LLM分析失败: {ex.Message}", Severity.Error);
            _isLlmAnalysisInProgress = false;
            StateHasChanged();
        }
    }

    private void ShowCategoryData(string categoryData)
    {
        _categoryDataDialogContent = categoryData;
        _categoryDataDialogVisible = true;
        StateHasChanged();
    }
}
