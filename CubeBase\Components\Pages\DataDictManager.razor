﻿@page "/DataDictManager"
@inherits BasePage
@inject ILogger<DataDictManager> Logger
    
@using CubeBase.Components.Shared

<div class="page-container" style="width: 98%; margin-left: 0;">
<MudGrid Style="margin: 0; height: 100%; overflow: visible;">
    <MudItem xl="@(_leftPanelCollapsed ? 1 : 3)">
        @if (_leftPanelCollapsed)
        {
            <div style="display: flex; justify-content: center; align-items: center; height: 100%;">
                <MudIconButton Icon="@Icons.Material.Filled.ChevronRight" OnClick="@ToggleLeftPanel" Title="展开" Size="Size.Large"/>
            </div>
        }
        else
        {
            <MudStack Spacing="1">
                <div style="display: flex; justify-content: flex-end;">
                    <MudIconButton Icon="@Icons.Material.Filled.ChevronLeft" OnClick="@ToggleLeftPanel" Title="收起" Size="Size.Small"/>
                </div>
                <MudStack Row="true" Spacing="1">
                    <MudButton Variant="Variant.Filled" Color="Color.Primary" OnClick="HandleExportFullDictionaryAsync" Disabled="@_isLoading" Size="Size.Small">
                        导出完整字典
                    </MudButton>
                    <MudButton Variant="Variant.Filled" Color="Color.Secondary" OnClick="HandleExportAllNodesAsync" Disabled="@_isLoading" Size="Size.Small">
                        导出视图字典
                    </MudButton>
                    <MudButton Variant="Variant.Filled" Color="Color.Info" OnClick="HandleExportSubtreeAsync" Disabled="@_isLoading" Size="Size.Small">
                        导出子树字典
                    </MudButton>
                </MudStack>

                @if (_showStartNodeInput)
                {
                    <MudStack Row="true" Spacing="1">
                        <MudTextField @bind-Value="_startNodeIdText" Label="请输入起始节点ID" Variant="Variant.Outlined" Size="Size.Small" />
                        <MudButton Color="Color.Success" OnClick="HandleConfirmExportSubtreeAsync" Disabled="@_isLoading" Size="Size.Small">
                            确认导出
                        </MudButton>
                    </MudStack>
                }

                <MudPaper Elevation="3" Style="height: 80vh; overflow: hidden;">
                    <div style="height: 100%; width: 100%; overflow: auto; padding: 8px;">
                        @if (_isLoading)
                        {
                            <MudProgressLinear Color="Color.Primary" Indeterminate="true" />
                            <MudText Typo="Typo.body2">正在处理，请稍候...</MudText>
                        }
                        else if (!string.IsNullOrEmpty(_outputResult))
                        {
                            <MudText Style="white-space: pre-wrap; font-family: monospace; font-size: 12px; line-height: 1.2;">@_outputResult</MudText>
                        }
                        else
                        {
                            <MudText Typo="Typo.body2">请点击上方按钮导出数据字典</MudText>
                        }
                    </div>
                </MudPaper>
            </MudStack>
        }
    </MudItem>
    <MudItem xl="@(_leftPanelCollapsed ? 11 : 9)" Style="height: 100%; overflow: visible; display: flex; flex-direction: column;">
        <DataDictFormGrid />
    </MudItem>
</MudGrid>
</div>

@code {
    private string _outputResult = string.Empty;
    private bool _isLoading = false;
    private bool _showStartNodeInput = false;
    private string _startNodeIdText = string.Empty;
    private bool _leftPanelCollapsed = true;

    private void ToggleLeftPanel()
    {
        _leftPanelCollapsed = !_leftPanelCollapsed;
        StateHasChanged();
    }

    /// <summary>
    /// 导出完整医学数据字典
    /// </summary>
    private async Task HandleExportFullDictionaryAsync()
    {
        _showStartNodeInput = false;
        await ExecuteExportOperationAsync(async (context) =>
            await inj_medDictService.ExportMedicalDataDictionaryAsync(context));
    }

    /// <summary>
    /// 导出仅包含Visual节点的数据字典
    /// </summary>
    private async Task HandleExportAllNodesAsync()
    {
        _showStartNodeInput = false;
        await ExecuteExportOperationAsync(async (context) =>
            await inj_medDictService.ExportAllNodesDataDictionaryAsync(context));
    }

    /// <summary>
    /// 显示子树导出输入框
    /// </summary>
    private void HandleExportSubtreeAsync()
    {
        _showStartNodeInput = true;
        _startNodeIdText = string.Empty;
        _outputResult = "请输入起始节点ID并点击确认按钮";
    }

    /// <summary>
    /// 确认导出子树数据字典
    /// </summary>
    private async Task HandleConfirmExportSubtreeAsync()
    {
        if (string.IsNullOrWhiteSpace(_startNodeIdText))
        {
            _outputResult = "错误：起始节点ID不能为空";
            return;
        }

        if (!Guid.TryParse(_startNodeIdText, out Guid startNodeId))
        {
            _outputResult = "错误：无效的GUID格式，请检查输入";
            return;
        }

        await ExecuteExportOperationAsync(async (context) =>
            await inj_medDictService.ExportSubtreeDataDictionaryAsync(context, startNodeId));
    }

    /// <summary>
    /// 执行导出操作的通用方法
    /// </summary>
    private async Task ExecuteExportOperationAsync(Func<CubeContext, Task<string>> exportOperation)
    {
        try
        {
            _isLoading = true;
            _outputResult = "正在处理...";
            StateHasChanged();

            // 使用BasePage提供的CubeContext工厂方法
            using var context = await ContextFactory.CreateDbContextAsync();
            _outputResult = await exportOperation(context);
        }
        catch (Exception ex)
        {
            _outputResult = $"导出过程中发生错误: {ex.Message}";
            Logger.LogError(ex, "导出数据字典时发生异常");
        }
        finally
        {
            _isLoading = false;
            StateHasChanged();
        }
    }

    protected override void OnInitialized()
    {
        base.OnInitialized(); // Ensure base is called
    }
}
