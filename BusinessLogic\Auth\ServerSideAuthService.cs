﻿using BitzArt.Blazor.Auth;
using DataAccess.Models;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System.Security.Claims;

namespace BusinessLogic.Auth;

public class ServerSideAuthService : ServerSideAuthenticationService<SignInPayload, SignUpPayload>
{
    private readonly IConfiguration _configuration;
    private readonly IHttpContextAccessor _httpContextAccessor;
    private readonly ILogger<ServerSideAuthService> _logger;

    public ServerSideAuthService(IConfiguration configuration, IHttpContextAccessor httpContextAccessor, ILogger<ServerSideAuthService> logger)
    {
        _configuration = configuration;
        _httpContextAccessor = httpContextAccessor;
        _logger = logger;
    }

    protected override async Task<AuthenticationResult> GetSignInResultAsync(SignInPayload signInPayload)
    {
        _logger.LogWarning("开始登录验证，账号: {Account}", signInPayload.Account);

        var options = new DbContextOptionsBuilder<CubeContext>()
                          .UseNpgsql(_configuration.GetConnectionString("DefaultConnection"))
                          .Options;
        CubeContext context = new CubeContext(options);

        _logger.LogWarning("正在查询用户: {Account}", signInPayload.Account);
        // 验证用户凭据
        var user = await context.sys_user.Where(u => u.account == signInPayload.Account && u.password == signInPayload.Password).Include(u => u.role).ThenInclude(r => r.permission).FirstOrDefaultAsync();

        if (user == null)
        {
            _logger.LogWarning("用户验证失败: {Account}", signInPayload.Account);
            return AuthenticationResult.Failure("凭证无效 Invalid credentials");
        }
        _logger.LogWarning("用户验证成功: {Account}, 用户ID: {UserId}", user.account, user.id);

        var jwtTokenService = new JwtTokenService();
        // 生成 JWT
        List<Claim> claimList = new List<Claim>();
        claimList.Add(new Claim(ClaimTypes.Name, user.account));
        //遍历user所有role里面的所有permisson对象        
        foreach (var role in user.role)
        {
            foreach (var permission in role.permission)
            {
                claimList.Add(new Claim(ClaimTypes.Role, permission.name));
            }
        }
        _logger.LogWarning("开始为用户 {Account} 生成 JWT...", user.account);
        var jwtPair = jwtTokenService.BuildJwtPair(new ClaimsIdentity(claimList));
        _logger.LogWarning("JWT 生成成功，AccessToken ExpiresAt: {AccessTokenExpiresAt}, RefreshToken ExpiresAt: {RefreshTokenExpiresAt}", jwtPair.AccessTokenExpiresAt, jwtPair.RefreshTokenExpiresAt);

        //获得登录者的IP地址
        var ipAddress = _httpContextAccessor.HttpContext?.Connection?.RemoteIpAddress?.MapToIPv4().ToString();
        _logger.LogWarning("获取到登录 IP 地址: {IpAddress}", ipAddress ?? "未知");

        user.last_login_time = DateTime.Now;
        user.last_login_ip = ipAddress;
        context.Update(user);
        await context.SaveChangesAsync();
        _logger.LogWarning("已更新用户 {Account} 的最后登录时间和 IP", user.account);

        _logger.LogWarning("登录流程成功完成，返回 AuthenticationResult.Success，用户: {Account}", user.account);
        return AuthenticationResult.Success(jwtPair);
    }

    //protected override async Task<AuthenticationResult> GetSignUpResultAsync(SignUpPayload signUpPayload)
    //{
    //    var user = new User
    //    {
    //        Account = signUpPayload.Account,
    //        Password = signUpPayload.Password
    //    };

    //    await _userService.CreateUserAsync(user);

    //    var jwtPair = _jwtService.BuildJwtPair();
    //    return AuthenticationResult.Success(jwtPair);
    //}

    public override async Task<AuthenticationResult> RefreshJwtPairAsync(string refreshToken)
    {
        _logger.LogWarning("开始刷新 RefreshToken: {RefreshToken}", refreshToken);

        // TODO: 验证 refreshToken 的有效性
        // 1. 检查 refreshToken 是否为空或无效格式
        if (string.IsNullOrWhiteSpace(refreshToken))
        {
            _logger.LogWarning("提供的 RefreshToken 为空或无效。");
            return AuthenticationResult.Failure("无效的 RefreshToken。");
        }

        // 2. 从数据库或其他存储中查找与此 refreshToken 关联的用户或会话
        //    并验证其有效性（例如，是否过期，是否已被撤销）。
        //    这部分逻辑需要你自己根据 UserRefreshTokens 表或类似机制实现。
        //    假设你有一个方法 `ValidateAndGetUserFromRefreshTokenAsync(string token)`
        //    它返回用户信息或者 null (如果token无效)

        // --- 伪代码开始 ---
        // var userAssociatedWithToken = await ValidateAndGetUserFromRefreshTokenAsync(refreshToken);
        // if (userAssociatedWithToken == null)
        // {
        //     _logger.LogWarning("RefreshToken验证失败或未找到关联用户: {RefreshToken}", refreshToken);
        //     return AuthenticationResult.Failure("RefreshToken 无效或已过期。");
        // }
        // _logger.LogWarning("RefreshToken 验证成功，关联用户: {UserAccount}", userAssociatedWithToken.account);
        // --- 伪代码结束 ---

        // 模拟：为了让代码能跑通，我们暂时假设refreshToken总是有效的，并直接为admin用户生成新token
        // 在实际应用中，你必须替换下面的代码为真正的refreshToken验证和用户信息获取逻辑
        var options = new DbContextOptionsBuilder<CubeContext>()
                          .UseNpgsql(_configuration.GetConnectionString("DefaultConnection"))
                          .Options;
        using var context = new CubeContext(options);
        // 这里的 "admin" 只是一个占位符，你需要通过refreshToken找到真正的用户
        var user = await context.sys_user.Where(u => u.account == "admin").Include(u => u.role).ThenInclude(r => r.permission).FirstOrDefaultAsync();
        if (user == null)
        {
            _logger.LogError("在刷新令牌时未能找到用户'admin'（这是一个占位逻辑，需要修正）");
            return AuthenticationResult.Failure("刷新令牌时内部服务器错误。");
        }
         _logger.LogWarning("（占位逻辑）为用户 {Account} 重新生成JWT，因为 RefreshToken 被认为是有效的。", user.account);


        var jwtTokenService = new JwtTokenService();
        List<Claim> claimList = new List<Claim>
        {
            new Claim(ClaimTypes.Name, user.account)
            // 添加其他必要的 Claims，与登录时保持一致
        };
        foreach (var role in user.role)
        {
            foreach (var permission in role.permission)
            {
                claimList.Add(new Claim(ClaimTypes.Role, permission.name));
            }
        }
        _logger.LogWarning("开始为用户 {Account} 生成新的 JWT (通过刷新)...", user.account);
        var newJwtPair = jwtTokenService.BuildJwtPair(new ClaimsIdentity(claimList));
        _logger.LogWarning("新的 JWT 生成成功 (通过刷新)，AccessToken ExpiresAt: {AccessTokenExpiresAt}, RefreshToken ExpiresAt: {RefreshTokenExpiresAt}", newJwtPair.AccessTokenExpiresAt, newJwtPair.RefreshTokenExpiresAt);

        // （可选）如果你的设计中 RefreshToken 是一次性的，或者每次刷新都会生成新的 RefreshToken，
        // 你需要在这里更新数据库中存储的 RefreshToken。

        return AuthenticationResult.Success(newJwtPair);
    }
}
