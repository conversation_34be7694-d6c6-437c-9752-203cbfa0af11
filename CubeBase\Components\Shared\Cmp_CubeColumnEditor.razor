@inherits Cmp_Base
@inject ILogger<Cmp_CubeColumnEditor> inj_logger

@if (_tempColumnEntity != null)
{
    <div class="form-wrapper">
        <MudText Typo="Typo.h5" Class="pa-2">编辑 - @_parentTableEntity?.display_name - @_tempColumnEntity.display_name</MudText>
        <div class="scrollable-form">
            <MudForm Model="_tempColumnEntity">
                <MudTextField @bind-Value="_tempColumnEntity.id" Label="主键" ReadOnly Margin="Margin.Dense" Variant="Variant.Outlined" />
                <MudTextField T="string" @bind-Value="_tempColumnEntity.unique_name" Label="唯一编码" Required Variant="Variant.Outlined" Counter="60" Immediate="true"
                Validation="@(new Func<string, IEnumerable<string>>(MaxCharacters))" Margin="Margin.Dense" />
                <MudTextField @bind-Value="_tempColumnEntity.display_name" Label="显示名称" Required Variant="Variant.Outlined" Margin="Margin.Dense" />
                <div>
                    <div class="d-flex">
                        <MudButton Class="ma-1 ml-1 py-2 px-4" OnClick="@GetEnglishColumnName" Color="Color.Primary" Variant="Variant.Filled"> ↑↑ AI 自动翻译 ↑↑ </MudButton>
                        <MudButton Class="ma-2 pa-2" Color="Color.Primary" Variant="Variant.Filled" OnClick="@(() => ChangeColumnDefSortIndex("up"))"> UP </MudButton>
                        <MudButton Class="ma-2 pa-2" Color="Color.Primary" Variant="Variant.Filled" OnClick="@(() => ChangeColumnDefSortIndex("down"))"> Down </MudButton>
                        <RadioList @bind-Value="_tempColumnEntity.data_type" Items="_dataTypeItems" IsButton="true" Color="BColor.Success"></RadioList>
                    </div>
                    <span hidden="@(!_isVisible)"><Spinner Color="BColor.Success" style="padding-top:16px;" /></span>
                </div>
                @if (_tempColumnEntity.data_type == "选择")
                {
                    <MudField Label="选项内容" Margin="Margin.Dense" Variant="Variant.Outlined">
                        <Textarea @bind-Value="@_optionsSet" rows="8" IsAutoScroll></Textarea>
                    </MudField>
                }
                <MudSelect T="Guid" Label="所属表" @bind-Value="_tempColumnEntity.table_id" Variant="Variant.Outlined" Dense>
                    @foreach (var tableDef in _tableList.Where(td => td.is_valid && td.node_type == "Table").OrderBy(td => td.display_name))
                    {
                        <MudSelectItem Value="@tableDef.id"><i class="bi bi-door-open"></i> @tableDef.display_name</MudSelectItem>
                    }
                </MudSelect>
                <AutoComplete Items="@_autoCompleteItems" ValueChanged="AutoCompleteParentTableID" IsLikeMatch="true" IgnoreCase="false" />
                <MudTextField @bind-Value="_tempColumnEntity.note_cn" Label="备注" Variant="Variant.Outlined" Margin="Margin.Dense" Lines="3" />
                @if (_columnQuestions.Count > 0)
                {
                    <MudExpansionPanel Dense="true" Class="mt-1" Expanded>
                        <TitleContent>
                            <MudText Typo="Typo.subtitle2"><i class="bi bi-list-ul me-2"></i>相关记录 (@_columnQuestions.Count)</MudText>
                        </TitleContent>
                        <ChildContent>
                            <div class="pa-1" style="overflow-x: auto;">
                                <MudTable Items="@_columnQuestions" Dense="true" Hover="true" Striped="true" Style="font-size: 0.8rem;">
                                    <HeaderContent>
                                        <MudTh Style="width: 30%">ID</MudTh>
                                        <MudTh Style="width: 20%">项目</MudTh>
                                        <MudTh Style="width: 20%">表单</MudTh>
                                        <MudTh Style="width: 30%">显示名称</MudTh>
                                    </HeaderContent>
                                    <RowTemplate>
                                        <MudTd DataLabel="ID" Style="word-break: break-all;">@context.id</MudTd>
                                        <MudTd DataLabel="项目">@context.project_name</MudTd>
                                        <MudTd DataLabel="表单">@context.form_name</MudTd>
                                        <MudTd DataLabel="显示名称">@context.display_name</MudTd>
                                    </RowTemplate>
                                </MudTable>
                            </div>
                        </ChildContent>
                    </MudExpansionPanel>
                }
                <div class="d-flex">
                    <MudTextField @bind-Value="_mergeToID" Label="迁移Question到" Variant="Variant.Outlined" Margin="Margin.Dense" Lines="1" />
                    <MudButton Class="ma-1 pa-2" Variant="Variant.Filled" Color="Color.Primary" OnClick="OnDoMergeColumn" Style="width:80px"> Move </MudButton>
                </div>
                <div class="d-flex">
                    <MudButton Class="ma-1 pa-2" Variant="Variant.Filled" Color="Color.Primary" OnClick="OnColumnSubmit" Style="width:80px"> Submit </MudButton>
                    @if (ColumnId.HasValue)
                    {
                        <MudButton Class="ma-1 pa-2" Variant="Variant.Filled" Color="Color.Error" OnClick="DeleteColumn" Style="width:80px"> Delete </MudButton>
                    }
                </div>
            </MudForm>
            <div class="d-flex justify-content-end">
                <MudButton Class="ma-1 pa-2" Variant="Variant.Filled" Color="Color.Secondary" OnClick="@(() => BelongToAnalysis(_tempColumnEntity))" Style="width:80px"> 归属分析 </MudButton>
                <div class="flex-grow-1">
                    <MudMarkdown Value="@_aiAnswer" />
                </div>
            </div>
        </div>
    </div>
}
else if (_isLoading)
{
    <MudProgressCircular Indeterminate="true" />
}
else if (_loadError)
{
    <MudAlert Severity="Severity.Error" Variant="Variant.Filled">
        加载数据时出错：@_errorMessage
    </MudAlert>
}

@code {
    [Parameter] public Guid? ColumnId { get; set; }
    [Parameter] public Guid TableId { get; set; }
    [Parameter] public EventCallback OnSuccess { get; set; }
    [Parameter] public EventCallback OnDataChanged { get; set; }

    private bool _isVisible = false;
    private bool _isLoading = true;
    private bool _loadError = false;
    private string _errorMessage = string.Empty;
    private string _aiAnswer = string.Empty;
    private string _mergeToID = string.Empty;

    // 当前编辑的列和其所属表
    private column_definition _tempColumnEntity { get; set; } = null!;
    private table_definition _parentTableEntity { get; set; } = null!;

    // 数据库查询结果缓存
    private List<table_definition> _tableList { get; set; } = new();
    private List<form_question> _columnQuestions { get; set; } = new();
    private List<string> _autoCompleteItems { get; set; } = new();

    private readonly IEnumerable<SelectedItem> _dataTypeItems = new SelectedItem[]
    {
        new ("选择", "选择"),
        new ("数值", "数值"),
        new ("文本", "文本"),
        new ("日期", "日期"),
        new ("布尔", "布尔"),
        new ("文件", "文件")
    };

    private string _optionsSet
    {
        get => _tempColumnEntity?.option_set.Count > 0 ? string.Join("\n", _tempColumnEntity.option_set) : string.Empty;
        set
        {
            if (_tempColumnEntity == null) return;

            value = value.Replace("###", "\n");
            _tempColumnEntity.option_set = value.Split(new[] { "\n" }, StringSplitOptions.RemoveEmptyEntries)
                                               .Select(option => option.Trim())
                                               .Where(option => !string.IsNullOrWhiteSpace(option))
                                               .Distinct()
                                               .ToList();
        }
    }

    protected override async Task OnParametersSetAsync()
    {
        _isLoading = true;
        _loadError = false;
        _errorMessage = string.Empty;

        try
        {
            await LoadDataAsync();
        }
        catch (Exception ex)
        {
            HandleError(ex, "加载列编辑器数据时发生错误");
        }
        finally
        {
            _isLoading = false;
            StateHasChanged();
        }
    }

    private async Task LoadDataAsync()
    {
        using var context = await ContextFactory.CreateDbContextAsync();

        // 加载所有表定义信息
        _tableList = await context.table_definition
            .AsNoTracking()
            .Where(t => t.is_valid)
            .ToListAsync();

        // 加载当前表的详细信息
        _parentTableEntity = await context.table_definition
            .AsNoTracking()
            .FirstOrDefaultAsync(t => t.id == TableId && t.is_valid)
            ?? throw new Exception($"找不到指定的表 (ID: {TableId})");

        if (ColumnId.HasValue)
        {
            await LoadExistingColumnAsync(context);
        }
        else
        {
            await CreateNewColumnAsync(context);
        }
    }

    private async Task LoadExistingColumnAsync(CubeContext context)
    {
        // 编辑现有列
        var columnToEdit = await context.column_definition
            .AsNoTracking()
            .FirstOrDefaultAsync(c => c.id == ColumnId.Value && c.is_valid)
            ?? throw new Exception($"找不到指定的列 (ID: {ColumnId})");

        _tempColumnEntity = inj_mapper.Map<column_definition>(columnToEdit);

        // 加载关联的问题
        var questionCount = await context.form_question
            .CountAsync(q => q.column_definition_id == ColumnId.Value);

        _columnQuestions = questionCount > 0
            ? await context.form_question
                .AsNoTracking()
                .Where(q => q.column_definition_id == ColumnId.Value)
                .ToListAsync()
            : new List<form_question>();
    }

    private async Task CreateNewColumnAsync(CubeContext context)
    {
        // 不再需要手动计算索引或创建完整实体
        _tempColumnEntity = new column_definition
            {
            // id 在服务中生成
                data_type = "数值", // 默认值
                table_id = TableId, // 确保table_id已设置
                is_valid = true, // 默认值
            // sorted_index 在服务中计算
                option_set = new List<string>() // 初始化选项集
            };

        _columnQuestions.Clear();
        await Task.CompletedTask; // 保持方法为 async
    }

    private async Task OnColumnSubmit()
    {
        if (_tempColumnEntity == null) return;

        _tempColumnEntity.unique_name = (_tempColumnEntity.unique_name ?? "").Trim().ToLower();

        try
        {
            using var context = await ContextFactory.CreateDbContextAsync();
            using var transaction = await context.Database.BeginTransactionAsync();

            try
            {
                bool isNew = !ColumnId.HasValue;

                if (isNew)
                {
                    // 调用服务添加新列
                    await inj_medDictService.AddColumnDefinitionAsync(context, _tempColumnEntity);
                    inj_snackbar.Add($"字段 [{_tempColumnEntity.display_name}] 添加成功！", Severity.Success);
                }
                else
                {
                    // 调用服务更新现有列
                    // 验证逻辑已移至服务内部
                    // 表变更处理逻辑已移至服务内部
                    await inj_medDictService.UpdateColumnDefinitionAsync(context, _tempColumnEntity);
                    inj_snackbar.Add($"字段 [{_tempColumnEntity.display_name}] 信息更新成功！", Severity.Success);
                }

                await context.SaveChangesAsync(); // 保存由服务添加/修改的实体
                await transaction.CommitAsync();
                await OnSuccess.InvokeAsync(); // 通知父组件成功
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                // 统一错误处理会记录和显示错误
                HandleError(ex, "保存 ColumnDefinition 时出错");
            }
        }
        catch (Exception ex)
        {
            // 统一的错误处理
            HandleError(ex, "保存字段信息时出错");
        }
    }

    private async Task DeleteColumn()
    {
        if (!ColumnId.HasValue || _tempColumnEntity == null) return; // 添加 _tempColumnEntity 检查

        var confirm = await inj_dialogService.ShowMessageBox(
            $"确定要删除字段【{_tempColumnEntity.display_name}】吗？",
            "",
            yesText: "确定",
            cancelText: "取消"
        );

        if (confirm != true) return;

        try
        {
            using var context = await ContextFactory.CreateDbContextAsync();
            using var transaction = await context.Database.BeginTransactionAsync();

            try
            {
                // 调用服务删除列
                await inj_medDictService.DeleteColumnDefinitionAsync(context, ColumnId.Value);

                await context.SaveChangesAsync(); // 保存更改
                await transaction.CommitAsync();

                inj_snackbar.Add($"字段 [{_tempColumnEntity.display_name}] 删除成功！", Severity.Success);
                await OnSuccess.InvokeAsync(); // 通知父组件成功
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                HandleError(ex, "执行删除操作时出错");
            }
        }
        catch (Exception ex)
        {
            HandleError(ex, "删除字段信息时出错");
        }
    }

    private async Task ChangeColumnDefSortIndex(string direction)
    {
        if (_tempColumnEntity == null || !ColumnId.HasValue) return;

        try
        {
            using var context = await ContextFactory.CreateDbContextAsync();
            using var transaction = await context.Database.BeginTransactionAsync();

            try
            {
                // 调用服务调整排序
                await inj_medDictService.ChangeColumnSortOrderAsync(context, _tempColumnEntity.id, direction);

                await context.SaveChangesAsync(); // 保存更改
                await transaction.CommitAsync();

                // 刷新当前编辑的实体（如果需要显示最新的排序）或重新加载数据
                // 为了简单起见，这里只通知父组件数据已变动
                await OnDataChanged.InvokeAsync();
                inj_snackbar.Add("列排序已更新。", Severity.Success); // 添加成功提示
            }
            catch (DbUpdateConcurrencyException ex) // 保留并发异常处理
            {
                await transaction.RollbackAsync();
                HandleError(ex, $"调整列排序时发生并发冲突: ColumnId={_tempColumnEntity.id}, Direction={direction}");
                inj_snackbar.Add("保存排序时出现冲突，数据已被其他操作修改，请重试。", Severity.Warning);
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                HandleError(ex, $"调整列排序时发生未知错误: ColumnId={_tempColumnEntity.id}, Direction={direction}");
            }
        }
        catch (Exception ex)
        {
            HandleError(ex, $"调整列排序时出错: ColumnId={_tempColumnEntity.id}, Direction={direction}");
        }
    }

    private void AutoCompleteParentTableID(string value)
    {
        if (_tempColumnEntity == null) return;

        _autoCompleteItems = _tableList
            .Where(td => td.display_name.Contains(value) && td.node_type == "Table")
            .OrderBy(td => td.display_name)
            .Select(td => td.display_name)
            .ToList();

        //当只有一个匹配项时，自动计算出对应的TableId
        if (_autoCompleteItems.Count == 1)
        {
            _tempColumnEntity.table_id = _tableList
                .First(td => td.display_name == _autoCompleteItems[0]).id;
        }
    }

    private async Task GetEnglishColumnName()
    {
        if (_tempColumnEntity == null) return;

        var systemPrompt = "你是一个精通中英双语的临床医学专家，擅长将中文医学术语翻译成适合作为数据库字段名的英文单词。";

        var q = $@"
目标：这是一个从中文到英文的翻译任务，旨在将用于表示临床医学概念的中文名称转换为适合作为数据库字段名称的英文单词。

任务背景：我正在设计一个表达临床医学概念及其逻辑关系的树状结构。树的每个节点都有一个中文名称，现在需要将这些中文名称翻译成英文，以便将来直接作为数据库的字段名称使用。

任务描述：请将中文词汇'{_tempColumnEntity.display_name}'翻译成一个适合作为数据库字段名称的英文单词。

注意，翻译出的英文单词应当符合以下要求：
必须符合临床医学专业要求；
必须是一个英文单词，或者由下划线连接的多个英文单词，不能是短语或句子；
只允许包含小写英文字符和下划线；
不允许超过60个字符；不允许有空格；
不允许有数字；
应尽量与其它同级节点和父节点保持一致的命名风格；
容易被程序员理解和识别；
直接给出翻译结果，无需任何分析或解释。

背景信息：
当前字段的中文名称：{_tempColumnEntity.display_name}
节点层级关系：{await GetParentNodeChainPathString()}
同表下的其它字段：{await GetSiblingColumnInfoString()}";
        inj_logger.LogDebug("LLM Query: " + q);

        _isVisible = true;
        // 使用Bio.AI替代原有的LLM服务
        var result = await BioAI.ThinkAsync(
            prompt: q,
            model: LLModel.OpenRouter.Gemini_25_Flash_05_20,
            systemPrompt: systemPrompt
        );
        _tempColumnEntity.unique_name = result.Answer;
        _isVisible = false;
        StateHasChanged();
    }

    //获得一个TableDefinition的从当前节点上溯到根节点，经过的所有级联节点的UniqueName和DisplayName
    private async Task<string> GetParentNodeChainPathString(bool hasEN = true)
    {
        using var context = await ContextFactory.CreateDbContextAsync();

        var tableEntity = await context.table_definition
            .AsNoTracking()
            .FirstOrDefaultAsync(t => t.id == _tempColumnEntity.table_id);

        if (tableEntity == null) return string.Empty;

        var allTables = await context.table_definition
            .AsNoTracking()
            .Where(t => t.is_valid)
            .ToListAsync();

        var currentNode = tableEntity;
        string parentNodeChainPathString = $"{currentNode.display_name}";

        while (currentNode?.parent_node_id != null &&
              currentNode.parent_node_id != Guid.Empty &&
              allTables.Any(td => td.id == currentNode.parent_node_id))
        {
            currentNode = allTables.FirstOrDefault(td => td.id == currentNode.parent_node_id);
            if (currentNode == null) break;

            if (hasEN)
                parentNodeChainPathString = $"{currentNode.unique_name}({currentNode.display_name}) -> " + parentNodeChainPathString;
            else
                parentNodeChainPathString = $"{currentNode.display_name} -> " + parentNodeChainPathString;
        }
        return parentNodeChainPathString;
    }

    //获得一个ColumnDefinition的的同级节点（拥有相同的父节点）的信息
    private async Task<string> GetSiblingColumnInfoString()
    {
        using var context = await ContextFactory.CreateDbContextAsync();

        var siblingColumns = await context.column_definition
            .AsNoTracking()
            .Where(cd => cd.table_id == _tempColumnEntity.table_id &&
                        cd.id != _tempColumnEntity.id &&
                        cd.is_valid)
            .ToListAsync();

        var siblingInfo = siblingColumns
            .Select(cd => $"{cd.unique_name}({cd.display_name})")
            .ToList();

        return string.Join("; ", siblingInfo);
    }

    // 验证方法
    private IEnumerable<string> MaxCharacters(string ch)
    {
        if (!string.IsNullOrEmpty(ch) && 60 < ch?.Length)
            yield return "不要超过60个字符";
    }

    private void HandleError(Exception ex, string message)
    {
        _loadError = true;
        _errorMessage = ex.Message;
        inj_logger.LogError(ex, message);
        inj_snackbar.Add(message, Severity.Error);
    }

    /// <summary>
    /// 分析列定义应该归属于哪个分类
    /// </summary>
    /// <param name="columnDefinition">列定义实例</param>
    /// <returns>异步任务</returns>
    private async Task BelongToAnalysis(column_definition columnDefinition)
    {
        if (columnDefinition == null) return;

        try
        {
            _isVisible = true;
            // 清空文本区域，准备接收流式输出
            _aiAnswer = "正在分析中，请稍候...";
            StateHasChanged();

            using var context = await ContextFactory.CreateDbContextAsync();

            // 创建一个StringBuilder来收集完整的响应
            var responseBuilder = new StringBuilder();

            // 调用MedDictService中的BelongToAnalysisAsync方法，使用流式输出
            await inj_medDictService.BelongToAnalysisAsync(
                context,
                columnDefinition,
                inj_logger,
                (partialResponse) => {
                    // 将部分响应添加到StringBuilder
                    responseBuilder.Append(partialResponse);
                    // 更新文本区域内容
                    _aiAnswer = responseBuilder.ToString();
                    // 通知UI更新
                    InvokeAsync(StateHasChanged);
                });

            inj_snackbar.Add("归属分析完成", Severity.Success);
        }
        catch (Exception ex)
        {
            HandleError(ex, "执行归属分析时出错");
        }
        finally
        {
            _isVisible = false;
            StateHasChanged();
        }
    }

    private async Task OnDoMergeColumn()
    {
        if (_tempColumnEntity == null) return;
        if (!Guid.TryParse(_mergeToID, out var destColumnId))
        {
            inj_snackbar.Add("目标列ID格式不正确", Severity.Error);
            return;
        }
        string sql = $@"
        UPDATE form.form_question
        SET column_definition_id = '{destColumnId}'
        WHERE column_definition_id = '{_tempColumnEntity.id}'
        ";
        using var context = await ContextFactory.CreateDbContextAsync();
        await context.Database.ExecuteSqlRawAsync(sql);
        await context.SaveChangesAsync();
        await OnSuccess.InvokeAsync();
        inj_snackbar.Add("合并成功", Severity.Success);
        StateHasChanged();
    }
}
