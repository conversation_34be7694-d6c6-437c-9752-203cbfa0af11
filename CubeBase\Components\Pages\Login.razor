﻿@page "/login"
@layout BlankLayout
@attribute [AllowAnonymous]
@inject ILogger<Login> _logger
@inject IServiceProvider inj_hostEnvServices
@inject AuthenticationStateProvider LocalAuthenticationStateProvider

<PageTitle>Login</PageTitle>
<style>
    .login-container {
        display: flex;
        justify-content: center; /* 水平居中 */
        align-items: center; /* 垂直居中 */
        height: 100vh; /* 视口高度 */
        background-color: #f5f5f5; /* 可选背景色 */
    }
</style>
<div Class="login-container">
    <MudPaper Class="pa-4" Elevation="3" Width="360px">
        <MudForm @bind-IsValid="success" @bind-Errors="errors">
            <MudTextField @bind-Value="_account" MaxLength="12" Variant="Variant.Outlined" Immediate="true" Label="账号" Margin="Margin.Dense" Required="true" />
            <MudTextField @bind-Value="_password" InputType="@_passwordInput" MaxLength="20" Variant="Variant.Outlined" Immediate="true" Label="密码"
                          Adornment="Adornment.End" AdornmentIcon="@_passwordInputIcon" OnAdornmentClick="SwitchPasswordVisibility" Margin="Margin.Dense" Required="true" OnKeyDown="CheckPressEnterKey" />
            @if (errors.Length > 0)
            {
                <MudAlert Severity="Severity.Error" Color="Color.Error">
                    @foreach (var error in errors)
                    {
                        <div>@error</div>
                    }
                </MudAlert>
            }
            <MudButton OnClick="Signin" Variant="Variant.Filled" Color="MudBlazor.Color.Primary" Class="my-2">登录</MudButton>
        </MudForm>

    </MudPaper>
</div>

@code {
    [Parameter]
    public string returnUrl { get; set; } = string.Empty;

    string _account { get; set; } = "admin";
    string _password { get; set; } = "Bioo123456";
    bool success;
    string[] errors = new string[0];

    private async Task Signin()
    {
        if (inj_userService != null)
        {
            _logger.LogWarning("inj_userService 的实际类型是: {UserServiceType}", inj_userService.GetType().FullName);
        }
        else
        {
            _logger.LogWarning("inj_userService 为 null");
        }
        _logger.LogWarning("用户尝试登录，账号: {Account}", _account);
        var signInPayload = new SignInPayload(_account, _password);
        var result = await inj_userService.SignInAsync(signInPayload);
        _logger.LogWarning("SignInAsync 调用结果: IsSuccess={IsSuccess}, Error={Error}", result.IsSuccess, result.ErrorMessage);
        if (result.IsSuccess)
        {
            _logger.LogWarning("登录成功，等待2秒后导航到 /Home");
            await Task.Delay(2000); // 等待2秒，希望Cookie已设置并传播
            _logger.LogWarning("等待结束，执行导航到 /Home");
            inj_navigationManager.NavigateTo("/Home");
        }
        else
        {
            _logger.LogWarning("登录失败，显示错误信息");
            errors = new[] { "账号或密码错误" };
            StateHasChanged();
        }
    }

    private async Task CheckPressEnterKey(KeyboardEventArgs KeyboardEventArgs)
    {
        if (KeyboardEventArgs.Key == "Enter")
        {
            _logger.LogWarning("用户按下回车键，触发登录");
            await Signin();
        }
    }

    //password visibility
    bool _isShow;
    InputType _passwordInput = InputType.Password;
    string _passwordInputIcon = Icons.Material.Filled.VisibilityOff;

    void SwitchPasswordVisibility()
    {
        if (_isShow)
        {
            _isShow = false;
            _passwordInputIcon = Icons.Material.Filled.VisibilityOff;
            _passwordInput = InputType.Password;
        }
        else
        {
            _isShow = true;
            _passwordInputIcon = Icons.Material.Filled.Visibility;
            _passwordInput = InputType.Text;
        }
    }
}