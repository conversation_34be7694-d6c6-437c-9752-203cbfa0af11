﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace DataAccess.Models;

/// <summary>
/// 角色
/// </summary>
public partial class sys_role
{
    /// <summary>
    /// 角色ID
    /// </summary>
    public Guid id { get; set; }

    /// <summary>
    /// 角色名称
    /// </summary>
    public string name { get; set; }

    /// <summary>
    /// 角色描述
    /// </summary>
    public string describe { get; set; }

    /// <summary>
    /// 角色类型
    /// </summary>
    public string type { get; set; }

    /// <summary>
    /// 是否有效
    /// </summary>
    public bool is_valid { get; set; }

    /// <summary>
    /// 拥有者用户ID
    /// </summary>
    public Guid? owner_user_id { get; set; }

    public virtual ICollection<sys_permission_data> sys_permission_data { get; set; } = new List<sys_permission_data>();

    public virtual ICollection<sys_license> license { get; set; } = new List<sys_license>();

    public virtual ICollection<sys_permission> permission { get; set; } = new List<sys_permission>();

    public virtual ICollection<sys_user> user { get; set; } = new List<sys_user>();
}