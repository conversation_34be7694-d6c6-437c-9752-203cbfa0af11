﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace DataAccess.Models;

/// <summary>
/// 许可证表
/// </summary>
public partial class sys_license
{
    /// <summary>
    /// 主键
    /// </summary>
    public Guid id { get; set; }

    /// <summary>
    /// 许可证代码
    /// </summary>
    public string code { get; set; }

    /// <summary>
    /// 许可证名称
    /// </summary>
    public string name { get; set; }

    /// <summary>
    /// 许可证描述
    /// </summary>
    public string describe { get; set; }

    /// <summary>
    /// 许可证类型
    /// </summary>
    public string type { get; set; }

    /// <summary>
    /// 是否有效
    /// </summary>
    public bool is_valid { get; set; }

    /// <summary>
    /// 有效开始时间
    /// </summary>
    public DateTime? valid_start_time { get; set; }

    /// <summary>
    /// 有效结束时间
    /// </summary>
    public DateTime? valid_end_time { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime? create_time { get; set; }

    /// <summary>
    /// 授权项目ID
    /// </summary>
    public Guid? project_id { get; set; }

    /// <summary>
    /// 授权项目名称
    /// </summary>
    public string project_name { get; set; }

    public virtual form_project project { get; set; }

    public virtual ICollection<sys_role> role { get; set; } = new List<sys_role>();
}