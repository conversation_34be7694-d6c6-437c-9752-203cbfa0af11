﻿@implements IAsyncDisposable

@code {
    [Inject]
    protected IDbContextFactory<CubeContext> ContextFactory { get; set; } = default!;
        
    protected CubeContext? _context { get; private set; }

    protected override async Task OnInitializedAsync()
    {
        _context = await ContextFactory.CreateDbContextAsync();
        await OnPageInitializedAsync();
        await base.OnInitializedAsync();
    }

    // 提供一个虚方法供子类重写，实现自己的初始化逻辑
    protected virtual Task OnPageInitializedAsync() => Task.CompletedTask;

    public virtual async ValueTask DisposeAsync()
    {
        if (_context != null)
        {
            await _context.DisposeAsync();
        }
        await ValueTask.CompletedTask;
    }
}
