请理解以下文本数据的格式和层级结构。每一行代表数据的一个条目或分组，通过其在文本中的位置和包含的特定标记来确定其层级和属性。

主要标识和层级关系解析规则：

1.  **层级结构**：文本数据通过以下标记表示层级关系：
    *   **最顶层 (表单分组)**: 以 "表单分组" 结尾的行，例如 "科研基本信息 表单分组 1 - - -"。后面的数字代表该分组的编号。
    *   **第二层 (表单名称)**: 在一个表单分组下，紧随其后，以 "表单名称" 结尾的行，例如 "科研基本信息 表单名称 1 1 - -"。后面的两个数字分别代表所属表单分组的编号和该表单名称在其表单分组中的编号。
    *   **第三层 (目录或复选目录)**: 在一个表单名称下，紧随其后，以 "复选目录" 或 "目录" 结尾的行，例如 "科研基本信息 复选目录 1 1 1 -" 或 "入院基本信息 目录 2 1 1 -"。后面的三个数字分别代表所属表单分组的编号、表单名称的编号和该目录/复选目录在其表单名称中的编号。
    *   **第四层及以下 (字段)**: 在一个目录或复选目录下，以具体的字段名称开头，后面跟着其类型（如 "单选", "文本", "整数", "小数", "长文本", "多选"），以及连续的四位数字标识层级和顺序，例如 "入组项目 单选 1 1 1 1" 或 "身高 小数 2 1 1.1 1"。连续的四位数字非常重要，它们精确地标识了字段所在的层级路径 (例如 2.1.1.1)。前三个或四个数字对应其所在的父级目录/复选目录的编号。小数点可以出现在四位数字中，表示更细的层级。

2.  **数据项属性**：除了层级和名称，每一行可能包含关于该数据项（特别是字段）的额外属性，这些属性通常出现在类型标识之后，用破折号 "-" 分隔，其格式可能包括：
    *   **选项列表**: 对于 "单选" 或 "多选" 类型，后面会列出所有可能的选项，以 "###" 分隔。
    *   **单位**: 对于数值类型字段，可能包含计量单位。
    *   **计算公式**: 对于计算得出的字段，会提供计算公式。
    *   **校验范围**: 对于数值类型字段，会指定允许的取值范围，通常用方括号 `[]` 表示。
    *   **日期时间格式**: 对于日期时间类型字段，会指定日期时间格式。
    *   **必填**: 对于必填字段，会指定必填。

3.  **无子项的字段属性不影响层级缩进**: 字段的详细属性（如选项、单位、公式、校验范围）属于该字段本身的信息，即使这些属性有多个，如 "第几次复发 整数 2 1 1 4 次 [0~20]"，它们并不代表一个新的层级，不应该导致额外的层进缩进。只有通过上面的层级标识规则确定的父子关系才体现在层级结构中。

目标：根据上述规则，将提供的文本数据解析，并依照‘输出格式模版’还原成清晰的树状层级结构，准确反映各条目之间的归属关系，避免因字段属性而产生的错误缩进。输出要包含完整内容。

输出格式模版：
科研基本信息 [表单分组] (1)
├── 科研基本信息 [表单名称] (1 - 1)
│   └── 科研基本信息 [复选目录] (1 - 1 - 1)
│       ├── 入组项目 [单选] (1 - 1 - 1 - 1) [选项:无###D1###D2###D3###D4]
│       ├── 研究中心 [文本] (1 - 1 - 1 - 2)
│       ├── 是否签署患者知情同意书 [单选] (1 - 1 - 1 - 3) [选项:是###否]
│       └── 知情同意书签署时间 [日期] (1 - 1 - 1 - 4) [格式:YYYY-MM-DD]
└── 入院后信息 [表单分组] (2)
    └── 入院后信息 [表单名称] (2 - 1)
        └── 入院基本信息 [目录] (2 - 1 - 1)
            ├── 就诊科室 [文本] (2 - 1 - 1 - 1) [必填]
            ├── 就诊细分科室 [文本] (2 - 1 - 1 - 2)
            ├── 是否初次发病 [单选] (2 - 1 - 1 - 3) [选项:初次###复发]
            ├── 第几次复发 [整数] (2 - 1 - 1 - 4) [单位:次] [校验:0~20]
            ├── 主诊医生 [文本] (2 - 1 - 1 - 5)
            ├── 一般情况 [目录] (2 - 1 - 1.1)
            │   ├── 身高 [小数] (2 - 1 - 1.1 - 1) [单位:cm] [校验:0~300]
            │   ├── 体重 [小数] (2 - 1 - 1.1 - 2) [单位:kg] [校验:0~1000]
            │   ├── 体重指数（BMI） [小数] (2 - 1 - 1.1 - 3) [单位:kg/㎡] [公式:体重/身高/身高*10000] [校验:0~100]
            └── 入院诊断 [长文本] (2 - 1 - 1.1 - 9)
                └── 颅颈交界区畸形 [多选] (2 - 1 - 1.1 - 10) [选项:颅底凹陷###寰枢椎脱位###其他]
                └── 其他（颅颈交界区畸形） [文本] (2 - 1 - 1.1 - 11)

请根据这些规则解析提供的文本数据，严格按照上述模版生成其树状表示。