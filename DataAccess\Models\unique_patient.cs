﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace DataAccess.Models;

/// <summary>
/// 唯一患者表
/// </summary>
public partial class unique_patient
{
    /// <summary>
    /// 主键
    /// </summary>
    public Guid id { get; set; }

    /// <summary>
    /// 姓名
    /// </summary>
    public string name { get; set; }

    /// <summary>
    /// 出生日期
    /// </summary>
    public DateOnly? birthday { get; set; }

    /// <summary>
    /// 性别
    /// </summary>
    public string gender { get; set; }

    /// <summary>
    /// 身份证类型
    /// </summary>
    public string sid_type { get; set; }

    /// <summary>
    /// 身份证号码
    /// </summary>
    public string sid_number { get; set; }

    /// <summary>
    /// 身份证地址
    /// </summary>
    public List<string> sid_address { get; set; }

    /// <summary>
    /// 身份证验证
    /// </summary>
    public List<string> sid_verification { get; set; }

    /// <summary>
    /// 联系电话类型
    /// </summary>
    public List<string> phone_type { get; set; }

    /// <summary>
    /// 联系电话号码
    /// </summary>
    public List<string> phone_number { get; set; }

    /// <summary>
    /// 微信号（小程序的openid）
    /// </summary>
    public List<string> weixin_id { get; set; }

    /// <summary>
    /// 微信号绑定电话
    /// </summary>
    public List<string> weixin_phone_number { get; set; }

    /// <summary>
    /// 地址类型
    /// </summary>
    public List<string> address_type { get; set; }

    /// <summary>
    /// 地址内容
    /// </summary>
    public List<string> address_content { get; set; }

    /// <summary>
    /// 医保类型
    /// </summary>
    public string medical_type { get; set; }

    /// <summary>
    /// 医保卡号
    /// </summary>
    public string medical_card { get; set; }

    /// <summary>
    /// 曾用名
    /// </summary>
    public List<string> old_name { get; set; }

    /// <summary>
    /// 患者手机号登录密码
    /// </summary>
    public string login_password { get; set; }

    /// <summary>
    /// 默认就诊人标记
    /// </summary>
    public List<string> default_flag { get; set; }

    /// <summary>
    /// 与当前微信联系人关系
    /// </summary>
    public List<string> weixin_relation { get; set; }

    /// <summary>
    /// 微信头像
    /// </summary>
    public List<string> weixin_avatar_url { get; set; }
}