﻿<?xml version="1.0" encoding="utf-8" ?>
<nlog xmlns="http://www.nlog-project.org/schemas/NLog.xsd"
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      autoReload="true"
      throwConfigExceptions="true">

  <!-- 定义日志目标 -->
  <targets>
    <!-- 控制台日志 -->
    <target xsi:type="Console" name="console" 
            layout="${longdate} | ${level:uppercase=true} | ${logger} | ${message} ${exception:format=toString}" />

    <!-- Error日志文件 -->
    <target xsi:type="File" name="errorFile" 
            fileName="logs/${date:format=yyyy-MM-dd-HH}_error.log"
            layout="${longdate} | ${level:uppercase=true} | ${logger} | ${message} ${exception:format=toString}" />
    
    <!-- Warning日志文件 -->
    <target xsi:type="File" name="warnFile" 
            fileName="logs/${date:format=yyyy-MM-dd-HH}_warn.log"
            layout="${longdate} | ${level:uppercase=true} | ${logger} | ${message} ${exception:format=toString}" />
    
    <!-- Info日志文件 -->
    <target xsi:type="File" name="infoFile" 
            fileName="logs/${date:format=yyyy-MM-dd-HH}_info.log"
            layout="${longdate} | ${level:uppercase=true} | ${logger} | ${message} ${exception:format=toString}" />
    
    <!-- Debug日志文件 -->
    <target xsi:type="File" name="debugFile" 
            fileName="logs/${date:format=yyyy-MM-dd-HH}_debug.log"
            layout="${longdate} | ${level:uppercase=true} | ${logger} | ${message} ${exception:format=toString}" />
  </targets>

  <!-- 定义日志规则 -->
  <rules>
    <!-- 错误日志 -->
    <logger name="*" level="Error" writeTo="errorFile" />
    
    <!-- 警告日志 -->
    <logger name="*" level="Warn" writeTo="warnFile" />
    
    <!-- 信息日志 -->
    <logger name="*" level="Info" writeTo="infoFile" />
    
    <!-- 调试日志 >
    <logger name="*" level="Debug" writeTo="debugFile" /-->
    
    <!-- 控制台显示所有级别日志 -->
    <logger name="*" minlevel="Warn" writeTo="console" />
  </rules>
</nlog>