﻿@inherits Cmp_Base
@inject ILogger<Cmp_MedDictTable> inj_logger

<div class="d-flex flex-column justify-content-start align-items-stretch h-100">
    @if (_isDataReady)
    {
        <AutoComplete Items="@_searchItems" IsSelectAllTextOnFocus="true" IsLikeMatch="true" 
                    IgnoreCase="true" class="w-100 mb-2" OnSelectedItemChanged="@OnSelectedItemChanged">
            <ItemTemplate>
                <div class="d-flex align-items-center">
                    <span class="flex-fill ms-2">@context.Substring(0, context.IndexOf("]")+1)</span>
                </div>
            </ItemTemplate>
        </AutoComplete>
        @*  *@
        <div style="position:relative; flex:1; overflow:auto;">
            <Table TItem="CubeTableTreeData"
                   IndentSize="8"
                   HeaderStyle="TableHeaderStyle.Light"
                   IsBordered
                   ShowLineNo="true"
                   LineNoText="序号"
                   Items="_cubeTableTreeData"
                   IsTree="true"
                   @ref="_table"
                   TreeNodeConverter="@TreeNodeConverter">
                <TableColumns>
                    <!-- 显示名称列 -->
                    <TableColumn @bind-Field="@context.DisplayName" Text="默认名称" OnCellRender="@OnCellRenderHandler">
                        <Template Context="v">
                            @if (v.Row.NodeType == "Column")
                            {
                                <input type="checkbox" id="CubeTableTreeDataID" value="@v.Row.ID" checked="true" readonly onclick="return false;" style="accent-color: #1890ff; pointer-events: none;" />
                                <span id="@v.Row.ID">&nbsp;</span>
                                <i class="@GetDataTypeIcon(v.Row.DataType)" style="font-size: 14px;"></i>
                            }
                            <Tooltip Title="@(v.Row.OptionSet != null ? string.Join(", ", v.Row.OptionSet) : string.Empty)" CustomClass="is-valid">
                                &nbsp;@v.Value
                            </Tooltip>
                        </Template>
                    </TableColumn>
                    <!-- 操作列 -->
                    <TableColumn @bind-Field="@context.ID" Text="插入" Align="Alignment.Center" OnCellRender="@OnCellRenderHandler" Fixed="true" Width="40" >
                        <Template Context="v" >
                            @if (v.Row.NodeType == "Column")
                            {
                                <a href="javascript:void(0)"
                                   @onclick="@(()=>PublishColumnInsert(v.Row))"
                                   style="display: block; width: 100%; height: 100%; text-align: center;">
                                    <i class="mdi mdi-arrow-expand-right" style="font-size: 12px;"></i>
                                </a>
                            }
                        </Template>
                    </TableColumn>
                </TableColumns>
            </Table>
         </div> 
    }
    else
    {
        <div>数据加载中...</div>
    }
</div>

@code {
    #region 参数定义
    private List<CubeTableTreeData> _cubeTableTreeData { get; set; } = new();
    private List<string> _searchItems { get; set; } = new();
    private bool _isDataReady = false;
    private Dictionary<Guid, TableTreeNode<CubeTableTreeData>> _allTreeNodes = new();
    private Table<CubeTableTreeData> _table;
    #endregion

    #region 生命周期方法

    protected override async Task OnPageInitializedAsync()
    {
        List<table_definition> tableDefinitions = await _context.table_definition
            .AsNoTracking()
            .Where(td => td.is_valid)
            .OrderBy(td => td.sorted_index)
            .ToListAsync();

        List<column_definition> columnDefinitions = await _context.column_definition
            .AsNoTracking()
            .Where(cd => cd.is_valid)
            .OrderBy(cd => cd.sorted_index)
            .ToListAsync();

        var start = System.Diagnostics.Stopwatch.StartNew();
        BuildTree(tableDefinitions, columnDefinitions);
        inj_logger.LogWarning("构建树状结构耗时："+start.ElapsedMilliseconds+"ms");

        var tableDict = _cubeTableTreeData
    .Where(td => td.NodeType != "Column")
    .ToDictionary(td => td.ID, td => td.DisplayName);

    _searchItems = _cubeTableTreeData
        .Where(cd => cd.NodeType == "Column")
        .Select(cd => {
            string tableName = tableDict.TryGetValue(cd.ParentId, out var name) ? name : "未知表";
            return $"【{tableName}】：{cd.DisplayName} - [{cd.DataType}] - ({cd.ID})";
        })
        .ToList();
        @* _searchItems = _cubeTableTreeData
            .Where(cd => cd.NodeType == "Column")
            .Select(cd =>cd.DisplayName+" - ["+cd.DataType+"] - ("+cd.ID+")")
            .ToList(); *@

        _isDataReady = true;  // 设置数据准备完成标志
    }

    #endregion

    #region 树形结构相关方法

    private void BuildTree(List<table_definition> tables, List<column_definition> columns)
    {
        _cubeTableTreeData.Clear();

        foreach (var table in tables)
        {
            _cubeTableTreeData.Add(new CubeTableTreeData(table));
        }

        foreach (var column in columns)
        {
            _cubeTableTreeData.Add(new CubeTableTreeData(column));
        }
    }

    private Task<IEnumerable<TableTreeNode<CubeTableTreeData>>> TreeNodeConverter(IEnumerable<CubeTableTreeData> items)
    {
        var start = System.Diagnostics.Stopwatch.StartNew();
        _allTreeNodes.Clear();
        // 将列表转换为 Lookup，Key 为 ParentId
        var lookup = items.ToLookup(i => i.ParentId);
        // 初始调用 BuildTreeNodes，传入 Lookup 和根节点的 ParentId (通常是 Guid.Empty)
        var ret = BuildTreeNodes(lookup, Guid.Empty);
        inj_logger.LogWarning($"构建树状结构耗时：{start.ElapsedMilliseconds}ms"); // 移动日志位置可能更准确
        return Task.FromResult(ret);
    }

    private IEnumerable<TableTreeNode<CubeTableTreeData>> BuildTreeNodes(ILookup<Guid, CubeTableTreeData> lookup, Guid parentId)
    {
        // 使用 Lookup 高效查找子节点，接近 O(1) 复杂度
        var children = lookup[parentId];
        if (!children.Any()) // 判断是否有子节点也很快
        {
            return Enumerable.Empty<TableTreeNode<CubeTableTreeData>>();
        }

        var ret = new List<TableTreeNode<CubeTableTreeData>>();
        ret.AddRange(children.Select((node, index) =>
        {
            // 判断当前节点是否有孙子节点，同样使用 Lookup
            bool hasGrandchildren = lookup[node.ID].Any();
            var treeNode = new TableTreeNode<CubeTableTreeData>(node)
            {
                HasChildren = hasGrandchildren,
                // IsExpand 可以根据需要调整，例如只展开顶级节点: IsExpand = (parentId == Guid.Empty && hasGrandchildren)
                IsExpand = false, // 复用判断结果，或者使用其他展开逻辑
                // 递归调用，传入 Lookup 和当前节点的 ID
                Items = BuildTreeNodes(lookup, node.ID)
            };
            
            // 将创建的节点保存到字典中，以便后续查找
            _allTreeNodes[node.ID] = treeNode;
            
            return treeNode;
        }));
        return ret;
    }

    private Task<IEnumerable<TableTreeNode<CubeTableTreeData>>> OnTreeExpand(CubeTableTreeData node)
    {
        return Task.FromResult(_cubeTableTreeData.Where(i => i.ParentId == node.ID).Select(i => new TableTreeNode<CubeTableTreeData>(i)));
    }

    #endregion

    #region 样式和图标处理方法

    private string GetDataTypeIcon(string dataType) => dataType switch
    {
        "选择" => "mdi mdi-checkbox-marked-circle-outline",
        "数值" => "mdi mdi-pound",
        "文本" => "mdi mdi-text",
        "日期" => "mdi mdi-calendar",
        "布尔" => "mdi mdi-toggle-switch",
        "文件" => "mdi mdi-file",
        _ => string.Empty,
    };

    private static void OnCellRenderHandler(TableCellArgs args)
    {
        if (args.Row is not CubeTableTreeData data) return;

        args.Class = data.NodeType != "Column" ? "cell-demo" : GetDataTypeClass(data.DataType);
    }

    private static string GetDataTypeClass(string dataType) => dataType switch
    {
        "选择" => "is-selected",
        "数值" => "is-number",
        "文本" => "is-text",
        "日期" => "is-date",
        "布尔" => "is-boolean",
        "文件" => "is-file",
        _ => string.Empty
    };

    #endregion

    #region 事件处理方法

    private void PublishColumnInsert(CubeTableTreeData data)
    {
        var column = _context.column_definition
            .Include(cd => cd.table)
            .Where(cd => cd.is_valid)
            .FirstOrDefault(cd => cd.id == data.ID);   
            
        if (column != null)
        {
            inj_logger.LogWarning("插入列操作被Publish"+column.display_name);
            inj_messageHub.Publish<column_definition>(column);
        }
        else
        {
            inj_logger.LogWarning("插入列操作失败，未找到列定义"+data.ID);
        }
    }

    private Task OnSelectedItemChanged(string val)
    {
        try 
        {
            // 提取ID
            var id = val.Substring(val.IndexOf("(")+1, val.IndexOf(")")-val.IndexOf("(")-1);
            inj_snackbar.Add("选中列："+id, Severity.Info);
            
            // 尝试解析Guid
            if (Guid.TryParse(id, out Guid columnId))
            {
                // 展开选中节点及其父节点
                ExpandNodeAndParents(columnId);
                
                // 使用延迟导航，给予足够时间让DOM更新
                _ = Task.Run(async () =>
                {
                    // 等待足够的时间让UI渲染完成
                    await Task.Delay(300);
                    
                    // 使用InvokeAsync确保在UI线程上执行导航
                    await InvokeAsync(() =>
                    {
                        inj_navigationManager.NavigateTo($"/FormsetManager#{id}");
                        return Task.CompletedTask;
                    });
                });
                
                return Task.CompletedTask;
            }
            
            inj_navigationManager.NavigateTo($"/FormsetManager#{id}");
        }
        catch (Exception ex)
        {
            inj_logger.LogError($"处理选择项变更时出错: {ex.Message}");
        }
        return Task.CompletedTask;
    }
    
    private void ExpandNodeAndParents(Guid nodeId)
    {
        try
        {
            // 找到当前选中的节点
            if (!_allTreeNodes.TryGetValue(nodeId, out var currentNode))
            {
                // 如果找不到选中节点，直接返回
                inj_logger.LogWarning($"未找到节点: {nodeId}");
                return;
            }
            
            // 找到当前节点的所有父节点并展开
            var node = currentNode;
            var parentIds = new List<Guid>();
            
            // 从当前节点查找其所有父节点
            var item = _cubeTableTreeData.FirstOrDefault(i => i.ID == nodeId);
            while (item != null && item.ParentId != Guid.Empty)
            {
                parentIds.Add(item.ParentId);
                item = _cubeTableTreeData.FirstOrDefault(i => i.ID == item.ParentId);
            }
            
            // 展开所有父节点
            foreach (var parentId in parentIds)
            {
                if (_allTreeNodes.TryGetValue(parentId, out var parentNode))
                {
                    parentNode.IsExpand = true;
                }
            }
            
            // 更新状态
            StateHasChanged();
        }
        catch (Exception ex)
        {
            inj_logger.LogError($"展开节点及父节点时出错: {ex.Message}");
        }
    }
    #endregion
}


 <style scoped>
    /* 覆盖固定列的样式 */
    td.fixed,
    th.fixed {
        position: sticky !important;
        left: auto !important;
        right: 0 !important;
        z-index: 2;
    }

    /* 允许内容列自然延展 */
    .table {
        table-layout: fixed !important; /* 强制使用自动布局 */
    }

        .table td:not(.fixed),
        .table th:not(.fixed) {
            white-space: nowrap !important;
            max-width: none !important;
            width: auto !important;
        }

    /* 确保表格容器可以水平滚动 */
    div[style*="position:relative"] {
        overflow-x: auto !important;
    }

    .table-fixed-column .table th, .table-fixed-column .table td {
        border-left-width: 1px;
    }

</style>