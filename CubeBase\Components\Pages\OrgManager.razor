﻿@page "/OrgManager"
@inherits BasePage
@inject ILogger<OrgManager> inj_logger

<MudGrid Class="ma-0">
    <!-- 医院管理部分 -->
    <MudItem xs="2">
        <h5>医院管理</h5>
        <Table @ref="_pageElements_HospitalTable" TItem="sys_hospital" OnClickRowCallback="OnHospitalTableRowClick" OnQueryAsync="OnHospitalQueryAsync"
        SearchMode="SearchMode.Top" HeaderStyle="TableHeaderStyle.Light" LineNoText="序号"
        ShowToolbar ShowAddButton="false" ShowEditButton="false" ShowDeleteButton="false" ShowExtendButtons="false"
        ClickToSelect IsStriped IsBordered ShowLineNo ShowColumnList="true">
            <TableColumns>
                <!-- 医院名称列 -->
                <TableColumn @bind-Field="@context.name" Text="医院名称" Sortable="true" Filterable="true" />
            </TableColumns>
        </Table>
        <!-- 添加医院按钮 -->
        <MudButton Class="ma-2 pa-2" Color="Color.Primary" Variant="Variant.Filled" OnClick="async () => await AddNewHospital()"> 添加 </MudButton>
    </MudItem>

    <!-- 院区管理部分 -->
    <MudItem xs="3">
        <h5>院区管理</h5>
        <Table @ref="_pageElements_RegionTable" TItem="sys_region" OnClickRowCallback="OnRegionTableRowClick" OnQueryAsync="OnRegionQueryAsync"
        SearchMode="SearchMode.Top" HeaderStyle="TableHeaderStyle.Light" LineNoText="序号"
        ShowToolbar ShowAddButton="false" ShowEditButton="false" ShowDeleteButton="false" ShowExtendButtons="false"
        ClickToSelect IsStriped IsBordered ShowLineNo ShowColumnList="true">
            <TableColumns>
                <!-- 所属医院列，使用模板显示医院名称 -->
                <TableColumn @bind-Field="@context.hospital_id" Text="所属医院" Sortable="true" Filterable="true">
                    <Template Context="v">
                        @if (v?.Value != null)
                        {
                            <div style="display: flex; align-items: center;">
                                @_contextHospitalList?.FirstOrDefault(h => h.id == v.Value)?.name
                            </div>
                        }
                        else
                        {
                            <span>错误</span>
                        }
                    </Template>
                </TableColumn>
                <!-- 院区名称列 -->
                <TableColumn @bind-Field="@context.name" Text="院区名称" Sortable="true" Filterable="true" />
            </TableColumns>
        </Table>
        <!-- 添加院区按钮 -->
        <MudButton Class="ma-2 pa-2" Color="Color.Primary" Variant="Variant.Filled" OnClick="AddNewRegion"> 添加 </MudButton>
    </MudItem>

    <!-- 科室管理部分 -->
    <MudItem xs="3">
        <h5>科室管理</h5>
        <Table @ref="_pageElements_DepartmentTable" TItem="sys_department" OnClickRowCallback="OnDepartmentTableRowClick" OnQueryAsync="OnDepartmentQueryAsync"
        SearchMode="SearchMode.Top" HeaderStyle="TableHeaderStyle.Light" LineNoText="序号"
        ShowToolbar ShowAddButton="false" ShowEditButton="false" ShowDeleteButton="false" ShowExtendButtons="false"
        ClickToSelect IsStriped IsBordered ShowLineNo ShowColumnList="true">
            <TableColumns>
                <!-- 所属医院列，使用模板显示医院名称 -->
                <TableColumn @bind-Field="@context.hospital_id" Text="所属医院" Sortable="true" Filterable="true">
                    <Template Context="v">
                        @if (v?.Value != null)
                        {
                            <div style="display: flex; align-items: center;">
                                @_contextHospitalList?.FirstOrDefault(h => h.id == v.Value)?.name
                            </div>
                        }
                        else
                        {
                            <span>错误</span>
                        }
                    </Template>
                </TableColumn>
                <!-- 科室名称列 -->
                <TableColumn @bind-Field="@context.name" Text="科室名称" Sortable="true" Filterable="true" />
            </TableColumns>
        </Table>
        <!-- 添加科室按钮 -->
        <MudButton Class="ma-2 pa-2" Color="Color.Primary" Variant="Variant.Filled" OnClick="AddNewDepartment"> 添加 </MudButton>
    </MudItem>

    <!-- 病区管理部分 -->
    <MudItem xs="4">
        <h5>病区管理</h5>
        <Table @ref="_pageElements_WardTable" TItem="sys_ward" OnClickRowCallback="OnWardTableRowClick" OnQueryAsync="OnWardQueryAsync"
        SearchMode="SearchMode.Top" HeaderStyle="TableHeaderStyle.Light" LineNoText="序号"
        ShowToolbar ShowAddButton="false" ShowEditButton="false" ShowDeleteButton="false" ShowExtendButtons="false"
        ClickToSelect IsStriped IsBordered ShowLineNo ShowColumnList="true">
            <TableColumns>
                <!-- 所属医院列，使用模板显示医院名称 -->
                <TableColumn @bind-Field="@context.hospital_id" Text="所属医院" Sortable="true" Filterable="true">
                    <Template Context="v">
                        @if (v?.Value != null)
                        {
                            <div style="display: flex; align-items: center;">
                                @_contextHospitalList?.FirstOrDefault(h => h.id == v.Value)?.name
                            </div>
                        }
                        else
                        {
                            <span>错误</span>
                        }
                    </Template>
                </TableColumn>
                <!-- 所属院区列，使用模板显示院区名称 -->
                <TableColumn @bind-Field="@context.region_id" Text="所属院区" Sortable="true" Filterable="true">
                    <Template Context="v">
                        @if (v?.Value != null)
                        {
                            <div style="display: flex; align-items: center;">
                                @_contextRegionList?.FirstOrDefault(r => r.id == v.Value)?.name
                            </div>
                        }
                        else
                        {
                            <span>错误</span>
                        }
                    </Template>
                </TableColumn>
                <!-- 所属科室列，使用模板显示科室名称 -->
                <TableColumn @bind-Field="@context.department_id" Text="所属科室" Sortable="true" Filterable="true">
                    <Template Context="v">
                        @if (v?.Value != null)
                        {
                            <div style="display: flex; align-items: center;">
                                @_contextDepartmentList?.FirstOrDefault(d => d.id == v.Value)?.name
                            </div>
                        }
                        else
                        {
                            <span>错误</span>
                        }
                    </Template>
                </TableColumn>
                <!-- 病区名称列 -->
                <TableColumn @bind-Field="@context.name" Text="病区名称" Sortable="true" Filterable="true" />
            </TableColumns>
        </Table>
        <!-- 添加病区按钮 -->
        <MudButton Class="ma-2 pa-2" Color="Color.Primary" Variant="Variant.Filled" OnClick="AddNewWard"> 添加 </MudButton>
    </MudItem>
</MudGrid>

<!-- 医院添加/编辑模态框 -->
@if (_tempHospitalEntity != null)
{
    <Modal @ref="_pageElements_HospitalModal" IsBackdrop="true">
        <ModalDialog Title="添加/编辑 - 医院" Size="BSize.Medium">
            <BodyTemplate>
                <EditForm Model="_tempHospitalEntity" OnValidSubmit="OnHospitalSubmit">
                    <!-- 医院主键字段，设为只读 -->
                    <MudTextField @bind-Value="_tempHospitalEntity.id" Label="主键" ReadOnly Disabled Margin="Margin.Dense" Variant="Variant.Outlined" />
                    <!-- 医院名称字段，必填 -->
                    <MudTextField @bind-Value="_tempHospitalEntity.name" Label="医院名称" Required Variant="Variant.Outlined" />
                    <!-- 提交按钮 -->
                    <MudButton ButtonType="MudBlazor.ButtonType.Submit" Class="ma-1 pa-2" Variant="Variant.Filled" Color="Color.Primary"> 提交 </MudButton>
                </EditForm>
            </BodyTemplate>
        </ModalDialog>
    </Modal>
}

<!-- 院区添加/编辑模态框 -->
@if (_tempRegionEntity != null)
{
    <Modal @ref="_pageElements_RegionModal" IsBackdrop="true">
        <ModalDialog Title="添加/编辑 - 院区" Size="BSize.Medium">
            <BodyTemplate>
                <EditForm Model="_tempRegionEntity" OnValidSubmit="OnRegionSubmit">
                    <!-- 院区主键字段，设为只读 -->
                    <MudTextField @bind-Value="_tempRegionEntity.id" Label="主键" ReadOnly Disabled Margin="Margin.Dense" Variant="Variant.Outlined" />
                    <!-- 院区名称字段，必填 -->
                    <MudTextField @bind-Value="_tempRegionEntity.name" Label="院区名称" Required Variant="Variant.Outlined" />
                    <!-- 所属医院下拉选择 -->
                    <MudSelect T="Guid" Label="所属医院" @bind-Value="_tempRegionEntity.hospital_id" Variant="Variant.Outlined" Dense>
                        @if (_contextHospitalList != null)
                        {
                            @foreach (var hospitalItem in _contextHospitalList.OrderBy(td => td.name))
                            {
                                <MudSelectItem Value="@hospitalItem.id"><i class="bi bi-door-open"></i> @hospitalItem.name</MudSelectItem>
                            }
                        }
                    </MudSelect>
                    <!-- 提交按钮 -->
                    <MudButton ButtonType="MudBlazor.ButtonType.Submit" Class="ma-1 pa-2" Variant="Variant.Filled" Color="Color.Primary"> 提交 </MudButton>
                </EditForm>
            </BodyTemplate>
        </ModalDialog>
    </Modal>
}

<!-- 科室添加/编辑模态框 -->
@if (_tempDepartmentEntity != null)
{
    <Modal @ref="_pageElements_DepartmentModal" IsBackdrop="true">
        <ModalDialog Title="添加/编辑 - 科室" Size="BSize.Medium">
            <BodyTemplate>
                <EditForm Model="_tempDepartmentEntity" OnValidSubmit="OnDepartmentSubmit">
                    <!-- 科室主键字段，设为只读 -->
                    <MudTextField @bind-Value="_tempDepartmentEntity.id" Label="主键" ReadOnly Disabled Margin="Margin.Dense" Variant="Variant.Outlined" />
                    <!-- 科室名称字段，必填 -->
                    <MudTextField @bind-Value="_tempDepartmentEntity.name" Label="科室名称" Required Variant="Variant.Outlined" />
                    <!-- 所属医院下拉选择 -->
                    <MudSelect T="Guid" Label="所属医院" @bind-Value="_tempDepartmentEntity.hospital_id" Variant="Variant.Outlined" Dense>
                        @if (_contextHospitalList != null)
                        {
                            @foreach (var hospitalItem in _contextHospitalList.OrderBy(td => td.name))
                            {
                                <MudSelectItem Value="@hospitalItem.id"><i class="bi bi-door-open"></i> @hospitalItem.name</MudSelectItem>
                            }
                        }
                    </MudSelect>
                    <!-- 提交按钮 -->
                    <MudButton ButtonType="MudBlazor.ButtonType.Submit" Class="ma-1 pa-2" Variant="Variant.Filled" Color="Color.Primary"> 提交 </MudButton>
                </EditForm>
            </BodyTemplate>
        </ModalDialog>
    </Modal>
}

<!-- 病区添加/编辑模态框 -->
@if (_tempWardEntity != null)
{
    <Modal @ref="_pageElements_WardModal" IsBackdrop="true">
        <ModalDialog Title="添加/编辑 - 病区" Size="BSize.Medium">
            <BodyTemplate>
                <EditForm Model="_tempWardEntity" OnValidSubmit="OnWardSubmit">
                    <!-- 病区主键字段，设为只读 -->
                    <MudTextField @bind-Value="_tempWardEntity.id" Label="主键" ReadOnly Disabled Margin="Margin.Dense" Variant="Variant.Outlined" />
                    <!-- 病房名称字段，必填 -->
                    <MudTextField @bind-Value="_tempWardEntity.name" Label="病房名称" Required Variant="Variant.Outlined" />
                    <!-- 所属医院下拉选择 -->
                    <MudSelect T="Guid" Label="所属医院" @bind-Value="_tempWardEntity.hospital_id" Variant="Variant.Outlined" Dense @bind-Value:after="StateHasChanged">
                        @if (_contextHospitalList != null)
                        {
                            @foreach (var hospitalItem in _contextHospitalList.OrderBy(td => td.name))
                            {
                                <MudSelectItem Value="@hospitalItem.id"><i class="bi bi-door-open"></i> @hospitalItem.name</MudSelectItem>
                            }
                        }
                    </MudSelect>
                    <!-- 所属院区下拉选择，根据所属医院动态过滤 -->
                    <MudSelect T="Guid" Label="所属院区" @bind-Value="_tempWardEntity.region_id" Variant="Variant.Outlined" Dense @bind-Value:after="StateHasChanged">
                        @if (_contextRegionList != null)
                        {
                            @foreach (var regionItem in _contextRegionList.Where(td => td.hospital_id == _tempWardEntity.hospital_id).OrderBy(td => td.name))
                            {
                                <MudSelectItem Value="@regionItem.id"><i class="bi bi-door-open"></i> @regionItem.name</MudSelectItem>
                            }
                        }
                    </MudSelect>
                    <!-- 所属科室下拉选择，根据所属医院动态过滤 -->
                    <MudSelect T="Guid" Label="所属科室" @bind-Value="_tempWardEntity.department_id" Variant="Variant.Outlined" Dense>
                        @if (_contextDepartmentList != null)
                        {
                            @foreach (var departmentItem in _contextDepartmentList.Where(td => td.hospital_id == _tempWardEntity.hospital_id).OrderBy(td => td.name))
                            {
                                <MudSelectItem Value="@departmentItem.id"><i class="bi bi-door-open"></i> @departmentItem.name</MudSelectItem>
                            }
                        }
                    </MudSelect>
                    <!-- 提交按钮 -->
                    <MudButton ButtonType="MudBlazor.ButtonType.Submit" Class="ma-1 pa-2" Variant="Variant.Filled" Color="Color.Primary"> 提交 </MudButton>
                </EditForm>
            </BodyTemplate>
        </ModalDialog>
    </Modal>
}

@code {
    // 上下文数据列表
    private List<sys_hospital>? _contextHospitalList;
    private List<sys_region>? _contextRegionList;
    private List<sys_department>? _contextDepartmentList;
    private List<sys_ward>? _contextWardList;

    // 选中的实体
    private sys_hospital? _selectedHospitalEntity;
    private sys_region? _selectedRegionEntity;
    private sys_department? _selectedDepartmentEntity;
    private sys_ward? _selectedWardEntity;

    // 临时实体，用于编辑
    private sys_hospital? _tempHospitalEntity;
    private sys_region? _tempRegionEntity;
    private sys_department? _tempDepartmentEntity;
    private sys_ward? _tempWardEntity;

    // 表格引用
    private Table<sys_hospital>? _pageElements_HospitalTable;
    private Table<sys_region>? _pageElements_RegionTable;
    private Table<sys_department>? _pageElements_DepartmentTable;
    private Table<sys_ward>? _pageElements_WardTable;

    // 模态框引用
    private Modal? _pageElements_HospitalModal;
    private Modal? _pageElements_RegionModal;
    private Modal? _pageElements_DepartmentModal;
    private Modal? _pageElements_WardModal;

    // 新增标志位
    private bool _showHospitalModal = false;
    private bool _showRegionModal = false;
    private bool _showDepartmentModal = false;
    private bool _showWardModal = false;

    // 组件渲染后首次执行
    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            await InvokeAsync(async () => await ReloadAll());
        }

        // 检查并显示医院模态框
        if (_showHospitalModal && _pageElements_HospitalModal != null)
        {
            _showHospitalModal = false;
            await InvokeAsync(() => _pageElements_HospitalModal.Show());
        }

        // 检查并显示院区模态框
        if (_showRegionModal && _pageElements_RegionModal != null)
        {
            _showRegionModal = false;
            await InvokeAsync(() => _pageElements_RegionModal.Show());
        }

        // 检查并显示科室模态框
        if (_showDepartmentModal && _pageElements_DepartmentModal != null)
        {
            _showDepartmentModal = false;
            await InvokeAsync(() => _pageElements_DepartmentModal.Show());
        }

        // 检查并显示病区模态框
        if (_showWardModal && _pageElements_WardModal != null)
        {
            _showWardModal = false;
            await InvokeAsync(() => _pageElements_WardModal.Show());
        }
    }

    // 重新加载所有数据
    private async Task ReloadAll()
    {
        // 顺序执行数据库操作，避免并发使用 DbContext
        _contextHospitalList = await inj_cubeContext.sys_hospital
            .OrderBy(td => td.name)
            .ToListAsync();

        _contextRegionList = await inj_cubeContext.sys_region
            .Include(r => r.hospital)
            .OrderBy(td => td.name)
            .ToListAsync();

        _contextDepartmentList = await inj_cubeContext.sys_department
            .Include(d => d.hospital)
            .OrderBy(td => td.name)
            .ToListAsync();

        _contextWardList = await inj_cubeContext.sys_ward
            .Include(w => w.hospital)
            .Include(w => w.region)
            .Include(w => w.department)
            .OrderBy(td => td.name)
            .ToListAsync();

        // 顺序执行表格查询，避免并发使用 DbContext
        if (_pageElements_HospitalTable != null)
        {
            await _pageElements_HospitalTable.QueryAsync();
        }
        if (_pageElements_RegionTable != null)
        {
            await _pageElements_RegionTable.QueryAsync();
        }
        if (_pageElements_DepartmentTable != null)
        {
            await _pageElements_DepartmentTable.QueryAsync();
        }
        if (_pageElements_WardTable != null)
        {
            await _pageElements_WardTable.QueryAsync();
        }

        StateHasChanged();
    }

    /// <summary>
    /// 医院表格查询方法
    /// 使用动态 LINQ 优化过滤和排序逻辑
    /// </summary>
    private Task<QueryData<sys_hospital>> OnHospitalQueryAsync(QueryPageOptions options)
    {
        return OnQueryAsync(_contextHospitalList, options, "name", h => h.name);
    }

    /// <summary>
    /// 院区表格查询方法
    /// 使用动态 LINQ 优化过滤和排序逻辑
    /// </summary>
    private Task<QueryData<sys_region>> OnRegionQueryAsync(QueryPageOptions options)
    {
        return OnQueryAsync(_contextRegionList, options, "hospital_id", r => r.hospital.name);
    }

    /// <summary>
    /// 科室表格查询方法
    /// 使用动态 LINQ 优化过滤和排序逻辑
    /// </summary>
    private Task<QueryData<sys_department>> OnDepartmentQueryAsync(QueryPageOptions options)
    {
        return OnQueryAsync(_contextDepartmentList, options, "hospital_id", d => d.hospital.name);
    }

    /// <summary>
    /// 病区表格查询方法，包含额外排序逻辑
    /// 使用动态 LINQ 优化过滤和排序逻辑
    /// </summary>
    private Task<QueryData<sys_ward>> OnWardQueryAsync(QueryPageOptions options)
    {
        return OnQueryAsync(_contextWardList, options, "hospital_id", w => w.hospital.name)
               .ContinueWith(task =>
               {
                   var data = task.Result;
                   if (!string.IsNullOrEmpty(options.SortName))
                   {
                       switch (options.SortName)
                       {
                           case "region_id":
                               data.Items = data.Items?.OrderBy(w => w.region.name).AsEnumerable();
                               break;
                           case "department_id":
                               data.Items = data.Items?.OrderBy(w => w.department.name).AsEnumerable();
                               break;
                           default:
                               // 保持原排序
                               break;
                       }
                   }
                   return data;
               });
    }

    /// <summary>
    /// 医院表格行点击事件处理
    /// </summary>
    private async Task OnHospitalTableRowClick(sys_hospital item)
    {
        _selectedHospitalEntity = item;
        _tempHospitalEntity = inj_mapper.Map<sys_hospital>(item);

        // 设置显示标志位
        _showHospitalModal = true;

        // 刷新界面
        await InvokeAsync(StateHasChanged);
    }

    /// <summary>
    /// 院区表格行点击事件处理
    /// </summary>
    private async Task OnRegionTableRowClick(sys_region item)
    {
        _selectedRegionEntity = item;
        _tempRegionEntity = inj_mapper.Map<sys_region>(item);

        // 设置显示标志位
        _showRegionModal = true;

        // 刷新界面
        await InvokeAsync(StateHasChanged);
    }

    /// <summary>
    /// 科室表格行点击事件处理
    /// </summary>
    private async Task OnDepartmentTableRowClick(sys_department item)
    {
        _selectedDepartmentEntity = item;
        _tempDepartmentEntity = inj_mapper.Map<sys_department>(item);

        // 设置显示标志位
        _showDepartmentModal = true;

        // 刷新界面
        await InvokeAsync(StateHasChanged);
    }

    /// <summary>
    /// 病区表格行点击事件处理
    /// </summary>
    private async Task OnWardTableRowClick(sys_ward item)
    {
        _selectedWardEntity = item;
        _tempWardEntity = inj_mapper.Map<sys_ward>(item);

        // 设置显示标志位
        _showWardModal = true;

        // 刷新界面
        await InvokeAsync(StateHasChanged);
    }

    /// <summary>
    /// 添加新医院
    /// </summary>
    private async Task AddNewHospital()
    {
        _selectedHospitalEntity = new();
        _selectedHospitalEntity.id = SequentialGuidGenerator.NewGuid();
        _tempHospitalEntity = inj_mapper.Map<sys_hospital>(_selectedHospitalEntity);

        // 设置显示标志位
        _showHospitalModal = true;

        // 刷新界面
        await InvokeAsync(StateHasChanged);
    }

    /// <summary>
    /// 添加新院区
    /// </summary>
    private async Task AddNewRegion()
    {
        _selectedRegionEntity = new();
        _selectedRegionEntity.id = SequentialGuidGenerator.NewGuid();

        if (_contextHospitalList != null && _contextHospitalList.Count > 0)
            _selectedRegionEntity.hospital_id = _contextHospitalList[0].id;
        else
        {
            inj_snackbar.Add("错误, 请先添加医院信息！", Severity.Error);
            return;
        }

        _tempRegionEntity = inj_mapper.Map<sys_region>(_selectedRegionEntity);

        // 设置显示标志位
        _showRegionModal = true;

        // 刷新界面
        await InvokeAsync(StateHasChanged);
    }

    /// <summary>
    /// 添加新科室
    /// </summary>
    private async Task AddNewDepartment()
    {
        _selectedDepartmentEntity = new();
        _selectedDepartmentEntity.id = SequentialGuidGenerator.NewGuid();

        if (_contextHospitalList != null && _contextHospitalList.Count > 0)
            _selectedDepartmentEntity.hospital_id = _contextHospitalList[0].id;
        else
        {
            inj_snackbar.Add("错误, 请先添加医院信息！", Severity.Error);
            return;
        }

        _tempDepartmentEntity = inj_mapper.Map<sys_department>(_selectedDepartmentEntity);

        // 设置显示标志位
        _showDepartmentModal = true;

        // 刷新界面
        await InvokeAsync(StateHasChanged);
    }

    /// <summary>
    /// 添加新病区
    /// </summary>
    private async Task AddNewWard()
    {
        _selectedWardEntity = new();
        _selectedWardEntity.id = SequentialGuidGenerator.NewGuid();
        // 如果当前_selectedHospitalEntity为空，则默认选中第一个医院，否则选中当前选中的医院
        if (_contextHospitalList != null && _contextHospitalList.Count > 0)
            _selectedWardEntity.hospital_id = _contextHospitalList[0].id;
        else
        {
            inj_snackbar.Add("错误, 请先添加医院信息！", Severity.Error);
            return;
        }

        _selectedWardEntity.region_id = _contextRegionList?
            .FirstOrDefault(rlist => rlist.hospital_id == _selectedWardEntity.hospital_id)?.id ?? default;

        _selectedWardEntity.department_id = _contextDepartmentList?
            .FirstOrDefault(dlist => dlist.hospital_id == _selectedWardEntity.hospital_id)?.id ?? default;

        _tempWardEntity = inj_mapper.Map<sys_ward>(_selectedWardEntity);

        // 设置显示标志位
        _showWardModal = true;

        // 刷新界面
        await InvokeAsync(StateHasChanged);
    }

    // 提交医院信息
    private async Task OnHospitalSubmit()
    {
        if (_selectedHospitalEntity != null)
        {
            await SubmitEntity(_tempHospitalEntity!, _selectedHospitalEntity!, inj_cubeContext.sys_hospital,
                $"[{_selectedHospitalEntity.name}] 添加成功！",
                $"[{_selectedHospitalEntity.name}] 信息更新成功！");
        }
    }

    // 提交院区信息
    private async Task OnRegionSubmit()
    {
        if (_selectedRegionEntity != null)
        {
            await SubmitEntity(_tempRegionEntity!, _selectedRegionEntity!, inj_cubeContext.sys_region,
                $"[{_selectedRegionEntity.name}] 添加成功！",
                $"[{_selectedRegionEntity.name}] 信息更新成功！");
        }
    }

    // 提交科室信息
    private async Task OnDepartmentSubmit()
    {
        if (_selectedDepartmentEntity != null && _tempDepartmentEntity != null)
        {
            _selectedDepartmentEntity.display_name = _selectedDepartmentEntity.name;
            await SubmitEntity(_tempDepartmentEntity, _selectedDepartmentEntity, inj_cubeContext.sys_department,
                $"[{_selectedDepartmentEntity.name}] 添加成功！",
                $"[{_selectedDepartmentEntity.name}] 信息更新成功！");
        }
        else
        {
            inj_snackbar.Add("科室信息不完整，无法提交", Severity.Error);
        }
    }

    // 提交病区信息
    private async Task OnWardSubmit()
    {
        if (_selectedWardEntity != null)
        {
            await SubmitEntity(_tempWardEntity!, _selectedWardEntity!, inj_cubeContext.sys_ward,
                $"[{_selectedWardEntity.name}] 添加成功！",
                $"[{_selectedWardEntity.name}] 信息更新成功！");
        }
    }

    /// <summary>
    /// 通用的提交实体方法
    /// </summary>
    private async Task SubmitEntity<T>(T tempEntity, T selectedEntity, DbSet<T> dbSet, string successAddMessage, string successUpdateMessage) where T : class
    {
        if (tempEntity != null)
        {
            inj_mapper.Map(tempEntity, selectedEntity);

            if (selectedEntity == null)
            {
                inj_snackbar.Add($"_selected{typeof(T).Name} 为空！", Severity.Error);
                return;
            }

            using (var transaction = await inj_cubeContext.Database.BeginTransactionAsync())
            {
                try
                {
                    bool isNew = !_contextList<T>().Any(td => GetEntityId(td).Equals(GetEntityId(selectedEntity)));

                    if (isNew)
                    {
                        dbSet.Add(selectedEntity);
                        inj_snackbar.Add(successAddMessage, Severity.Success);
                    }
                    else
                    {
                        dbSet.Update(selectedEntity);
                        inj_snackbar.Add(successUpdateMessage, Severity.Success);
                    }

                    await inj_cubeContext.SaveChangesAsync();
                    await transaction.CommitAsync();
                    await ReloadAll();
                }
                catch (Exception ex)
                {
                    await transaction.RollbackAsync();
                    inj_logger.LogError(ex, $"保存 {typeof(T).Name} 时出错: {ex.Message}");
                    // 提供更具体的错误信息给用户
                    inj_snackbar.Add($"保存 {typeof(T).Name} 信息时发生错误: {ex.Message}", Severity.Error);
                }
            }
        }
        CloseModal<T>();
    }

    /// <summary>
    /// 关闭对应的模态框
    /// </summary>
    private void CloseModal<T>() where T : class
    {
        switch (typeof(T).Name)
        {
            case nameof(sys_hospital):
                _pageElements_HospitalModal?.Close();
                break;
            case nameof(sys_region):
                _pageElements_RegionModal?.Close();
                break;
            case nameof(sys_department):
                _pageElements_DepartmentModal?.Close();
                break;
            case nameof(sys_ward):
                _pageElements_WardModal?.Close();
                break;
            default:
                break;
        }
    }

    /// <summary>
    /// 获取上下文列表
    /// </summary>
    private IEnumerable<T> _contextList<T>() where T : class
    {
        return typeof(T) switch
        {
            var type when type == typeof(sys_hospital) => _contextHospitalList as IEnumerable<T> ?? Enumerable.Empty<T>(),
            var type when type == typeof(sys_region) => _contextRegionList as IEnumerable<T> ?? Enumerable.Empty<T>(),
            var type when type == typeof(sys_department) => _contextDepartmentList as IEnumerable<T> ?? Enumerable.Empty<T>(),
            var type when type == typeof(sys_ward) => _contextWardList as IEnumerable<T> ?? Enumerable.Empty<T>(),
            _ => Enumerable.Empty<T>()
        };
    }

    /// <summary>
    /// 获取实体的Id
    /// </summary>
    private Guid GetEntityId<T>(T entity) where T : class
    {
        return entity switch
        {
            sys_hospital hospital => hospital.id,
            sys_region region => region.id,
            sys_department department => department.id,
            sys_ward ward => ward.id,
            _ => Guid.Empty
        };
    }
}