﻿@page "/LinkerRules"
@inherits BasePage
@inject ILogger<LinkerRules> inj_logger

<MudPaper Class="h-100 pa-1" Elevation="1">
    <!-- 使用4个横向排列的级联下拉菜单依次选择Hospital、Department、Ward、Project -->
    <div style="display: flex; align-items: center; position: relative;background-color: #ccdfee;">
        <MudText Class="ml-4" Typo="Typo.body1">选择项目：</MudText>
        <span>
            <Cascader TValue="string" OnSelectedItemChanged="@OnProjectChanged" Items="@_cascaderItems" ParentSelectable="false" 
            style="width: 500px;padding: 2px; z-index: 1200;" />
        </span>
        <MudText Class="ml-6" Typo="Typo.h6">
            @if (_selectedFormSet != null)
            {
                @($"{_selectedFormSet.hospital_name} - {_selectedFormSet.department_name} - {_selectedFormSet.ward_name} - {_selectedFormSet.name}")
            }
            @if (_selectedFormSet != null)
            {
                <span style="color: #007bff;">@($" - {_selectedFormSet.name}")</span>
            }
        </MudText>
    </div>
    
    @if (_selectedFormSet != null)
    {
        <div class="mt-3">
            <div class="d-flex justify-content-start mb-2">
                <Button Color="BootstrapBlazor.Components.Color.Primary" Icon="fa-solid fa-plus" Text="添加规则" OnClick="() => AddNewRule(_selectedFormSet.id)" />
            </div>
            
            <Table TItem="form_linker_rule" Items="@_linkerRules" IsStriped="true" IsPagination="true"
                PageItems="10" IsBordered="true" OnDoubleClickRowCallback="@EditRule" HeaderStyle="TableHeaderStyle.Light"
                ClickToSelect ShowLineNo LineNoText="序号" ScrollMode="ScrollMode.Virtual" Height="520">
                <TableColumns>
                    <TableColumn @bind-Field="@context.rule_name" Text="规则名称" Width="333" TextWrap="true">
                        <Template Context="v">
                            @v.Value
                        </Template>
                    </TableColumn>
                    <TableColumn @bind-Field="@context.publish_obj_id" Text="发布者对象ID" Width="333" TextWrap="true" CssClass="lucida-font">
                        <Template Context="v">
                            @((MarkupString)string.Join("<br />", v.Value?.Select(id => id.ToString()).ToArray() ?? Array.Empty<string>()))
                        </Template>
                    </TableColumn>
                    <TableColumn @bind-Field="@context.subscribe_obj_calss" Text="订阅者对象类型" Width="120">
                        <Template Context="v">
                            @v.Value
                        </Template>
                    </TableColumn>
                    <TableColumn @bind-Field="@context.subscribe_obj_id" Text="订阅者对象ID" Width="333" TextWrap="true" CssClass="lucida-font">
                        <Template Context="v">
                            @((MarkupString)string.Join("<br />", v.Value?.Select(id => id.ToString()).ToArray() ?? Array.Empty<string>()))
                        </Template>
                    </TableColumn>
                    <TableColumn @bind-Field="@context.action_function" Text="执行函数" Width="200">
                        <Template Context="v">
                            @v.Value
                        </Template>
                    </TableColumn>
                    <TableColumn @bind-Field="@context.priority" Text="优先级" Width="60">
                        <Template Context="v">
                            @v.Value
                        </Template>
                    </TableColumn>
                    <TableColumn @bind-Field="@context.parameters" Text="参数" TextWrap="true">
                        <Template Context="v">
                            @v.Value
                        </Template>
                    </TableColumn>
                    <TableColumn @bind-Field="@context.id" Text="编辑" Width="50">
                        <Template Context="v">
                            <Button Color="BootstrapBlazor.Components.Color.Primary" Icon="fa-solid fa-edit" Size="BootstrapBlazor.Components.Size.Small" @onclick="() => EditRule(v.Row)" />
                        </Template>
                    </TableColumn>
                    <TableColumn @bind-Field="@context.id" Text="删除" Width="50">
                        <Template Context="v">
                            <Button Color="BootstrapBlazor.Components.Color.Danger" Icon="fa-solid fa-trash" Size="BootstrapBlazor.Components.Size.Small" @onclick="() => DeleteRule(v.Row)" />
                        </Template>
                    </TableColumn>
                </TableColumns>
            </Table>
        </div>

        @if (_selectedRule != null)
        {
            <MudGrid Class="h-100 pa-1" Spacing="1">
                <MudItem xl="4">
                    <MudPaper Class="pa-1 mb-1" Elevation="0">
                        <div class="d-flex align-center justify-start">
                            <MudTextField @bind-Value="_selectedRule.rule_name" Label="规则名称" Variant="Variant.Outlined" Class="mr-2" Margin="Margin.Dense" Style="max-width: 520px;" />
                            <MudButton Variant="Variant.Filled" Color="MudBlazor.Color.Success" 
                                      OnClick="SaveRuleAsync" Class="mr-2">
                                保存规则
                            </MudButton>
                            <MudButton Variant="Variant.Outlined" Color="MudBlazor.Color.Default" 
                                      OnClick="CancelEdit">
                                取消
                            </MudButton>
                        </div>
                    </MudPaper>
                </MudItem>

                <MudFlexBreak />

                <MudItem xl="2" sm="12">
                    <MudPaper Class="h-100 pa-1" Elevation="0">
                        <Cmp_Question_Selector _formset="@_selectedFormSet" ObjType="publish"
                                              AddQuestionEvent="@AddObjToRule" AddCardEvent="@AddObjToRule" />
                    </MudPaper>
                </MudItem>
                <MudItem xl="2" sm="12">
                    <MudPaper Class="h-100 pa-1" Elevation="0" Outlined="true">
                        <dl>
                            @foreach (var publishObj in _selectedRule.publish_obj_id)
                            {
                                <dt class="lucida-font">
                                    @publishObj
                                    <a @onclick="() => DeleteObj(_selectedRule.publish_obj_id, publishObj)">x</a>
                                </dt>
                                @if (_formQuestionDict.ContainsKey(publishObj))
                                {
                                    @switch (_formQuestionDict[publishObj].data_type)
                                    {
                                        case "选择":
                                            <dd>- 
                                                [@_formQuestionDict[publishObj].data_type] 
                                                @_formQuestionDict[publishObj].display_name
                                                (@string.Join(",", _formQuestionDict[publishObj].select_sorted_option_subset))
                                            </dd>
                                            break;
                                        default:
                                            <dd>- [@_formQuestionDict[publishObj].data_type] @_formQuestionDict[publishObj].display_name</dd>
                                            break;
                                    }
                                }
                                @if (_formCardDict.ContainsKey(publishObj))
                                {
                                    <dd>- [卡片] @_formCardDict[publishObj].name</dd>
                                }
                            }
                        </dl>
                    </MudPaper>
                </MudItem>
                <MudItem xl="1" sm="12">
                    <MudPaper Class="h-100 pa-1" Elevation="0" Outlined="true">
                        <div class="d-flex flex-wrap gap-1 mb-1">
                            @foreach (var actionFunction in _actionFunctionList)
                            {
                                <MudButton Variant="@(actionFunction == _selectedActionFunction ? Variant.Filled : Variant.Outlined)" 
                                    Color="MudBlazor.Color.Secondary" Size="MudBlazor.Size.Small"
                                    OnClick="@(() => _selectedActionFunction = actionFunction)">
                                    @actionFunction
                                </MudButton>
                            }
                        </div>
                    </MudPaper>
                </MudItem>
                <MudItem xl="3" sm="12">
                    <MudPaper Class="h-100 pa-1" Elevation="0" Outlined="true">
                        <MudTextField @bind-Value="@_selectedRule.parameters" Variant="Variant.Outlined" Label="规则参数" AutoGrow Lines="20" />
                    </MudPaper>
                </MudItem>
                <MudItem xl="2" sm="12">
                    <MudPaper Class="h-100 pa-1" Elevation="0" Outlined="true">
                        <dl>
                            @foreach (var subscribeObj in _selectedRule.subscribe_obj_id)
                            {
                                <dt class="lucida-font">
                                    @subscribeObj
                                    <a @onclick="() => DeleteObj(_selectedRule.subscribe_obj_id, subscribeObj)">x</a>
                                </dt>
                                @if (_formQuestionDict.ContainsKey(subscribeObj))
                                {
                                    <dd>- [@_formQuestionDict[subscribeObj].data_type] @_formQuestionDict[subscribeObj].display_name</dd>
                                }
                                @if (_formCardDict.ContainsKey(subscribeObj))
                                {
                                    <dd>- [卡片] @_formCardDict[subscribeObj].name</dd>
                                }
                            }
                        </dl>
                    </MudPaper>
                </MudItem>
                <MudItem xl="2" sm="12">
                    <MudPaper Class="h-100 pa-1" Elevation="0">
                        <Cmp_Question_Selector _formset="@_selectedFormSet" ObjType="subscribe"
                                               AddQuestionEvent="@AddObjToRule" AddCardEvent="@AddObjToRule" />
                    </MudPaper>
                </MudItem>
            </MudGrid>
        }
    }
</MudPaper>

@code {
    private List<sys_hospital> _hospitalList = new();
    private List<CascaderItem> _cascaderItems = [];
    private form_form_set? _selectedFormSet;
    private List<form_linker_rule> _linkerRules = new();
    private form_linker_rule? _selectedRule;
    private List<string>? _actionFunctionList = new();
    private string? _selectedActionFunction;
    private Dictionary<Guid, form_card> _formCardDict = new();
    private Dictionary<Guid, form_question> _formQuestionDict = new();

    // 重写基类的初始化方法
    protected override async Task OnPageInitializedAsync() => await LoadProjectData();

    private async Task LoadProjectData()
    {
        using var _context = await ContextFactory.CreateDbContextAsync();

        // 修正数据加载，使用 ThenInclude 加载完整的层级结构
        _hospitalList = await _context.sys_hospital
            .Include(h => h.sys_department)
                .ThenInclude(d => d.sys_ward)
                    .ThenInclude(w => w.form_project)
                        .ThenInclude(p => p.form_form_set)
            .ToListAsync();

        _cascaderItems.Clear();

        // 修正过滤逻辑，确保只添加包含有效子项的节点
        foreach (var hospital in _hospitalList
            .Where(h => h.sys_department.Any(d => d.sys_ward.Any(w => w.form_project.Any(p => p.form_form_set.Any()))))
            .OrderBy(h => h.name))
        {
            var hospitalItem = new CascaderItem(hospital.id.ToString(), hospital.name);
            foreach (var department in hospital.sys_department
                .Where(d => d.sys_ward.Any(w => w.form_project.Any(p => p.form_form_set.Any())))
                .OrderBy(d => d.name))
            {
                var departmentItem = new CascaderItem(department.id.ToString(), department.name);
                foreach (var ward in department.sys_ward
                    .Where(w => w.form_project.Any(p => p.form_form_set.Any()))
                    .OrderBy(w => w.name))
                {
                    var wardItem = new CascaderItem(ward.id.ToString(), ward.name);
                    foreach (var project in ward.form_project
                        .Where(p => p.form_form_set.Any()) // 项目的过滤条件是正确的
                        .OrderBy(p => p.name))
                    {
                        var projectItem = new CascaderItem(project.id.ToString(), project.name);
                        foreach (var formSet in project.form_form_set.OrderBy(f => f.name))
                        {
                            var formSetItem = new CascaderItem(formSet.id.ToString(), formSet.name);
                            projectItem.AddItem(formSetItem);  
                        }
                        wardItem.AddItem(projectItem);
                    }
                    departmentItem.AddItem(wardItem);
                }
                hospitalItem.AddItem(departmentItem);
            }
            _cascaderItems.Add(hospitalItem);
        }      

        _actionFunctionList = new List<string>
        {
            "Computer.BMI",
            "DisplayHidden.DH_1N",
            "Score.Calculate"
        };
    }

    private async Task OnProjectChanged(CascaderItem[] items)
    {
        _selectedFormSet = null; // 重置为 null
        _linkerRules.Clear(); // 清空联动规则数据

        // 检查 items 是否有效且包含元素
        if (items != null && items.Length > 0)
        {
            // 获取最后一个选中的项的值，这应该是 FormSet 的 ID
            var selectedFormSetId = items[^1].Value;

            using var _context = await ContextFactory.CreateDbContextAsync();
            
            _selectedFormSet = await _context.form_form_set
                .Include(fs => fs.form_form)
                    .ThenInclude(f => f.form_card)
                        .ThenInclude(fc => fc.form_question)
                .FirstOrDefaultAsync(fs => fs.id.ToString() == selectedFormSetId);
                
            // 如果成功获取了表单集，则加载对应的联动规则数据
            if (_selectedFormSet != null)
            {
                var formsetId = _selectedFormSet.id;
                _linkerRules = await _context.form_linker_rule
                    .Where(r => r.formset_id == formsetId)
                    .ToListAsync();                    

                foreach (var form in _selectedFormSet.form_form)
                {
                    foreach (var card in form.form_card)
                    {   
                        _formCardDict.Add(card.id, card);
                        foreach (var question in card.form_question)
                        {
                            _formQuestionDict.Add(question.id, question);
                        }
                    }
                }
            }
        }

        // 更新 UI
        await InvokeAsync(StateHasChanged);
    }

    private async Task AddNewRule(Guid formsetId)
    {
        _selectedRule = new form_linker_rule
        {
            id = SequentialGuidGenerator.NewGuid(),
            formset_id = formsetId,
            // Initialize collections to prevent NullReferenceException
            publish_obj_id = new List<Guid>(), 
            subscribe_obj_id = new List<Guid>(),
            // Initialize other properties to sensible defaults if necessary
            parameters = string.Empty, // Default to empty string
            priority = 0 // Default priority
        };
        
        // Reset selected action function for a new rule
        _selectedActionFunction = null;
        
        await InvokeAsync(StateHasChanged);
    }

    private async Task EditRule(form_linker_rule rule)
    {
        _selectedRule = rule;
        _selectedActionFunction = rule.action_function;
        await InvokeAsync(StateHasChanged);
    }

    private async Task DeleteRule(form_linker_rule rule)
    {
        // 打开确认删除的弹窗
        var confirmed = await inj_dialogService.ShowMessageBox("确定删除该规则吗？", "确认", yesText: "确定", cancelText: "取消");
        if (confirmed == true)
        {
            // 如果用户确认删除，则删除数据库中的规则
            using var _context = await ContextFactory.CreateDbContextAsync();
            _context.form_linker_rule.Remove(rule);
            await _context.SaveChangesAsync();
            
            // 从内存列表中移除规则
            _linkerRules.Remove(rule);
            
            // 更新 UI
            await InvokeAsync(StateHasChanged);
        }
    }

    private async Task AddObjToRule((List<Guid>, string) param)
    {
        var (objIds, objType) = param;
        
        if (objType == "publish")
        {
            // 添加前进行排重，只添加不存在的ID
            foreach (var objId in objIds)
            {
                if (!_selectedRule.publish_obj_id.Contains(objId))
                {
                    _selectedRule.publish_obj_id.Add(objId);
                    var question = _formQuestionDict[objId];
                    _selectedRule.rule_name = $"{question.form_set_name} - {question.form_name} - {question.display_name}";
                }
            }
        }
        else if (objType == "subscribe")
        {
            foreach (var objId in objIds)
            {
                if (!_selectedRule.subscribe_obj_id.Contains(objId))
                {
                    _selectedRule.subscribe_obj_id.Add(objId);
                    // 判断是题目还是卡片
                    if (_formQuestionDict.ContainsKey(objId))
                    {
                        _selectedRule.subscribe_obj_calss = "form_question";
                    }
                    else if (_formCardDict.ContainsKey(objId))
                    {
                        _selectedRule.subscribe_obj_calss = "form_card";
                    }
                }
            }
        }
        
        await InvokeAsync(StateHasChanged);
    }

    private async Task DeleteObj(List<Guid> objIds, Guid objId)
    {
        objIds.Remove(objId);
        await InvokeAsync(StateHasChanged);
    }

    private async Task SaveRuleAsync()
    {
        if (_selectedRule == null || _selectedFormSet == null)
            return;
            
        // 确保规则名称不为空
        if (string.IsNullOrWhiteSpace(_selectedRule.rule_name))
        {
            await inj_dialogService.ShowMessageBox("错误", "规则名称不能为空", yesText: "确定");
            return;
        }
        
        // 确保执行函数已被选择
        if (string.IsNullOrWhiteSpace(_selectedActionFunction))
        {
            await inj_dialogService.ShowMessageBox("错误", "请选择一个执行函数", yesText: "确定");
            return;
        }
            
        // 设置规则的执行函数
        _selectedRule.action_function = _selectedActionFunction;
        
        // Ensure lists are not null (Belt and suspenders check)
        _selectedRule.publish_obj_id ??= new List<Guid>();
        _selectedRule.subscribe_obj_id ??= new List<Guid>();
        
        using var _context = await ContextFactory.CreateDbContextAsync();
        
        // 检查是否是新规则
        bool isNewRule = !await _context.form_linker_rule.AnyAsync(r => r.id == _selectedRule.id);
        
        try
        {
            if (isNewRule)
            {
                // 添加新规则
                _context.form_linker_rule.Add(_selectedRule);
            }
            else
            {
                // 更新现有规则
                _context.form_linker_rule.Update(_selectedRule);
            }
            
            // 保存更改
            await _context.SaveChangesAsync();
            
            // 重新加载当前表单集的规则
            var formsetId = _selectedFormSet.id;
            _linkerRules = await _context.form_linker_rule
                .Where(r => r.formset_id == formsetId)
                .ToListAsync();
                
            // 操作成功提示
            await inj_dialogService.ShowMessageBox("成功",
                isNewRule ? "规则添加成功！" : "规则更新成功！", 
                yesText: "确定");
                
            // 清除选中的规则，回到主列表
            _selectedRule = null;
        }
        catch (Exception ex)
        {
            // 异常处理
            inj_logger.LogError(ex, "保存规则时发生错误");
            await inj_dialogService.ShowMessageBox("错误", $"保存规则失败: {ex.Message}", yesText: "确定");
        }
        
        // 更新 UI
        await InvokeAsync(StateHasChanged);
    }
    
    private void CancelEdit()
    {
        // 清除选中的规则，回到主列表
        _selectedRule = null;
        StateHasChanged();
    }
}

<style>
    .lucida-font {
        font-family: 'Lucida Console', Consolas, monospace;
    }
</style>