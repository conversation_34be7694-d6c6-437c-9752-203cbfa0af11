﻿@page "/UserManager"
@inherits BasePage
@inject ILogger<UserManager> inj_logger

<MudGrid Class="ma-0">
    <!-- 左侧列表区域 -->
    <MudItem xs="6">
        <h5>角色管理</h5>
        <Table @ref="RoleTable" TItem="sys_role" OnClickRowCallback="@OnRoleTableRowClick" OnQueryAsync="@OnRoleQueryAsync"
               SearchMode="SearchMode.Top" HeaderStyle="TableHeaderStyle.Light" Height="480" LineNoText="序号"
               ShowToolbar ShowAddButton="false" ShowEditButton="false" ShowDeleteButton="false" ShowExtendButtons="false"
               IsFixedHeader="true" ClickToSelect IsStriped IsBordered ShowLineNo ShowColumnList="true">
            <TableColumns>
                <TableColumn @bind-Field="@context.name" Text="角色" Sortable="true" Filterable="true" />
                <TableColumn @bind-Field="@context.describe" Text="描述" Sortable="true" Filterable="true" />
                <TableColumn @bind-Field="@context.type" Text="类型" Sortable="true" Filterable="true" />
                <TableColumn @bind-Field="@context.user" Text="授予用户">
                    <Template Context="v">
                        @if (v.Value != null && v.Value.Count > 3)
                        {
                            var displayCount = 3;
                            var displayedItems = v.Value.Select(user => user.display_name).Take(displayCount).ToList();
                            var remainingCount = v.Value.Count - displayCount;
                            <div style="display: flex; align-items: center;">
                                <span>@string.Join(", ", displayedItems)</span>
                                @if (remainingCount > 0)
                                {
                                    <Tooltip Title="@string.Join(", ", v.Value.Select(user=>user.display_name))" CustomClass="is-valid">
                                        <i class="bi bi-three-dots" />
                                    </Tooltip>
                                }
                            </div>
                        }
                        else
                        {
                            <span>@(v.Value != null ? string.Join(", ", v.Value.Select(user => user.display_name)) : "")</span>
                        }
                    </Template>
                </TableColumn>
                <TableColumn @bind-Field="@context.is_valid" Text="是否有效" />
            </TableColumns>
        </Table>
        <MudButton Class="ma-2 pa-2" Color="Color.Primary" Variant="Variant.Filled" OnClick="AddNewRole"> ADD </MudButton>
    </MudItem>
    <!-- 右侧表单区域 -->
    <MudItem xs="6">
        <h5>用户管理</h5>
        <Table @ref="UserTable" TItem="sys_user" OnClickRowCallback="@OnUserTableRowClick" OnQueryAsync="@OnUserQueryAsync"
               HeaderStyle="TableHeaderStyle.Light" Height="480" LineNoText="序号"
               ShowToolbar ShowAddButton="false" ShowEditButton="false" ShowDeleteButton="false" ShowExtendButtons="false"
               IsFixedHeader="true" ClickToSelect IsStriped IsBordered ShowLineNo ShowColumnList="true">
            <TableColumns>
                <TableColumn @bind-Field="@context.account" Text="账号" Sortable="true" Filterable="true" />
                <TableColumn @bind-Field="@context.display_name" Text="显示名称" Sortable="true" Filterable="true" />
                <TableColumn @bind-Field="@context.can_grant_to_other" Text="向后授权" />
                <TableColumn @bind-Field="@context.is_valid" Text="是否有效" />
                <TableColumn @bind-Field="@context.last_login_ip" Text="最后登录IP" Sortable="true" Filterable="true" />
                <TableColumn @bind-Field="@context.last_login_time" Text="最后登录时间" Sortable="true" Filterable="true" />
            </TableColumns>
        </Table>
        <MudButton Class="ma-2 pa-2" Color="Color.Primary" Variant="Variant.Filled" OnClick="AddNewUser"> ADD </MudButton>
    </MudItem>
</MudGrid>
<Divider />

<MudGrid Class="ma-1">
    <!-- 左侧列表区域 -->
    <MudItem xs="6">
    </MudItem>
    <!-- 右侧表单区域 -->
    <MudItem xs="6">
    </MudItem>
</MudGrid>
<Drawer Placement="BPlacement.Left" @bind-IsOpen="@IsOpenDrawer_Role" IsBackdrop="true" AllowResize="true">
    <MudPaper Class="pt-2 pr-6" Elevation="0">
        @if (_tempRoleEntity != null)
        {
            <MudText Typo="Typo.h5" Class="pa-2">角色编辑界面</MudText>
            <EditForm Model="_tempRoleEntity" OnValidSubmit="OnRoleSubmit">
                <MudTextField @bind-Value="_tempRoleEntity.id" Label="主键" ReadOnly Disabled Margin="Margin.Dense" Variant="Variant.Outlined" />
                <MudTextField @bind-Value="_tempRoleEntity.name" Label="角色" Required Variant="Variant.Outlined" />
                <MudTextField @bind-Value="_tempRoleEntity.describe" Label="描述" Required Variant="Variant.Outlined" />
                <MudTextField @bind-Value="_tempRoleEntity.type" Label="类型" Required Variant="Variant.Outlined" />
                <MudField Label="是否有效" Margin="Margin.Dense" Variant="Variant.Outlined">
                    <Switch @bind-Value="@_tempRoleEntity.is_valid" OnColor="BColor.Success" OffColor="BColor.Danger" OnText="有效" OffText="无效"></Switch>
                </MudField>
                <MultiSelect Items="@_contextUserList.Select(user => new SelectedItem(user.id.ToString(), user.display_name))" @bind-Value="_roleSelectedUserIds"
                             Color="BColor.Success" />
                <MudButton Type="Submit" Class="ma-1 pa-2" Variant="Variant.Filled" Color="Color.Primary" OnClick="OnRoleSubmit"> Submit </MudButton>
            </EditForm>
        }
    </MudPaper>
</Drawer>
<Drawer Placement="BPlacement.Right" @bind-IsOpen="@IsOpenDrawer_User" IsBackdrop="true" AllowResize="true">
    <MudPaper Class="pt-2 pr-6" Elevation="0">
        @if (_tempUserEntity != null)
        {
            <MudText Typo="Typo.h5" Class="pa-2">用户编辑界面</MudText>
            <EditForm Model="_tempUserEntity" OnValidSubmit="OnUserSubmit">
                <MudTextField @bind-Value="_tempUserEntity.id" Label="主键" ReadOnly Disabled Margin="Margin.Dense" Variant="Variant.Outlined" />
                <MudTextField @bind-Value="_tempUserEntity.account" Label="账号" Required Variant="Variant.Outlined" />
                <MudTextField @bind-Value="_tempUserEntity.password" Label="密码" Required Variant="Variant.Outlined" />
                <MudTextField @bind-Value="_tempUserEntity.display_name" Label="显示名称" Required Variant="Variant.Outlined" />
                <MudField Label="向后授权" Margin="Margin.Dense" Variant="Variant.Outlined">
                    <Switch @bind-Value="@_tempUserEntity.can_grant_to_other" OnColor="BColor.Success" OffColor="BColor.Danger" OnText="有效" OffText="无效"></Switch>
                </MudField>
                <MudField Label="是否有效" Margin="Margin.Dense" Variant="Variant.Outlined">
                    <Switch @bind-Value="@_tempUserEntity.is_valid" OnColor="BColor.Success" OffColor="BColor.Danger" OnText="有效" OffText="无效"></Switch>
                </MudField>
                <MudButton Type="Submit" Class="ma-1 pa-2" Variant="Variant.Filled" Color="Color.Primary" OnClick="OnUserSubmit"> Submit </MudButton>
            </EditForm>
        }
    </MudPaper>
</Drawer>


@code {
    private List<sys_user> _contextUserList { get; set; } = new();
    private List<sys_role> _contextRoleList { get; set; } = new();
    private List<sys_permission> _permissionList { get; set; } = new();

    private Table<sys_user> UserTable { get; set; } = new();
    private Table<sys_role> RoleTable { get; set; } = new();

    private sys_user _selectedUserEntity { get; set; } = new();
    private sys_role _selectedRoleEntity { get; set; } = new();

    private sys_user _tempUserEntity { get; set; } = new();
    private sys_role _tempRoleEntity { get; set; } = new();

    private bool IsOpenDrawer_Role { get; set; }
    private bool IsOpenDrawer_User { get; set; }

    private IEnumerable<string> _roleSelectedUserIds { get; set; } = Enumerable.Empty<string>();

    protected override async Task OnInitializedAsync()
    {
        await ReloadAll();
    }

    private async Task ReloadAll()
    {
        _contextUserList = await inj_cubeContext.sys_user
            .OrderBy(td => td.account)
            .ToListAsync();

        _contextRoleList = await inj_cubeContext.sys_role
            .Include(r => r.user)
            .Include(r => r.permission)
            .OrderBy(td => td.name)
            .ToListAsync();

        await RoleTable.QueryAsync();
        await UserTable.QueryAsync();
        StateHasChanged();
    }

    private Task<QueryData<sys_user>> OnUserQueryAsync(QueryPageOptions options)
    {
        IEnumerable<sys_user> items = _contextUserList;

        // 先处理过滤再处理排序 提高性能
        var isFiltered = false;
        if (options.Filters.Any())
        {
            items = items.Where(options.Filters.GetFilterFunc<sys_user>());
            isFiltered = true;
        }

        // 排序
        var isSorted = false;
        if (!string.IsNullOrEmpty(options.SortName))
        {
            items = items.Sort(options.SortName, options.SortOrder);
            isSorted = true;
        }

        return Task.FromResult(new QueryData<sys_user>()
            {
                Items = items,
                IsSorted = isSorted,
                IsFiltered = isFiltered
            });
    }

    private Task<QueryData<sys_role>> OnRoleQueryAsync(QueryPageOptions options)
    {
        IEnumerable<sys_role> items = _contextRoleList;

        // 先处理过滤再处理排序 提高性能
        var isFiltered = false;
        if (options.Filters.Any())
        {
            items = items.Where(options.Filters.GetFilterFunc<sys_role>());
            isFiltered = true;
        }

        // 排序
        var isSorted = false;
        if (!string.IsNullOrEmpty(options.SortName))
        {
            items = items.Sort(options.SortName, options.SortOrder);
            isSorted = true;
        }

        return Task.FromResult(new QueryData<sys_role>()
            {
                Items = items,
                IsSorted = isSorted,
                IsFiltered = isFiltered
            });
    }

    private Task OnUserTableRowClick(sys_user item)
    {
        _selectedUserEntity = item;
        _tempUserEntity = inj_mapper.Map<sys_user>(item);
        IsOpenDrawer_User = true;
        StateHasChanged();
        return Task.CompletedTask;
    }

    private Task OnRoleTableRowClick(sys_role item)
    {
        _selectedRoleEntity = item;
        _tempRoleEntity = inj_mapper.Map<sys_role>(item);
        _roleSelectedUserIds = _tempRoleEntity.user.Select(user => user.id.ToString());
        IsOpenDrawer_Role = true;
        StateHasChanged();
        return Task.CompletedTask;
    }

    private void AddNewUser()
    {
        var currID = _selectedUserEntity?.id ?? Guid.Empty;
        _selectedUserEntity = new();
        _selectedUserEntity.id = SequentialGuidGenerator.NewGuid();
        _selectedUserEntity.can_grant_to_other = false;
        _selectedUserEntity.is_valid = true;
        _tempUserEntity = inj_mapper.Map<sys_user>(_selectedUserEntity);
        IsOpenDrawer_User = true;
        StateHasChanged();
    }

    private void AddNewRole()
    {
        var currID = _selectedRoleEntity?.id ?? Guid.Empty;
        _selectedRoleEntity = new();
        _selectedRoleEntity.id = SequentialGuidGenerator.NewGuid();
        _selectedRoleEntity.is_valid = true;
        _tempRoleEntity = inj_mapper.Map<sys_role>(_selectedRoleEntity);
        IsOpenDrawer_Role = true;
        StateHasChanged();
    }

    private async Task OnUserSubmit()
    {
        if (_tempUserEntity != null)
        {
            inj_mapper.Map(_tempUserEntity, _selectedUserEntity);

            using (var transaction = await inj_cubeContext.Database.BeginTransactionAsync())
            {
                try
                {
                    // 判断是添加还是更新 Sys_User
                    bool isNew = !_contextUserList
                        .Any(td => td.id == _selectedUserEntity.id);

                    if (isNew)
                    {
                        inj_cubeContext.sys_user.Add(_selectedUserEntity);
                        inj_snackbar.Add($"字段 [{_selectedUserEntity.display_name}] 添加成功！", Severity.Success);
                    }
                    else
                    {
                        inj_cubeContext.sys_user.Update(_selectedUserEntity);
                        inj_snackbar.Add($"字段 [{_selectedUserEntity.display_name}] 信息更新成功！", Severity.Success);
                    }

                    // 保存更改
                    await inj_cubeContext.SaveChangesAsync();

                    // 提交事务
                    await transaction.CommitAsync();

                    // 重新加载数据
                    await ReloadAll();
                }
                catch (Exception ex)
                {
                    // 回滚事务
                    await transaction.RollbackAsync();

                    inj_logger.LogError(ex, "保存 Sys_User 时出错:" + ex.Message);
                    inj_snackbar.Add("保存 Sys_User 时出错:" + ex.Message, Severity.Error);
                }
            }
        }
        IsOpenDrawer_User = false;
    }

    private async Task OnRoleSubmit()
    {
        if (_tempRoleEntity != null)
        {
            inj_mapper.Map(_tempRoleEntity, _selectedRoleEntity);

            _selectedRoleEntity.user = _contextUserList
                .Where(user => _roleSelectedUserIds.Contains(user.id.ToString()))
                .ToList();

            using (var transaction = await inj_cubeContext.Database.BeginTransactionAsync())
            {
                try
                {
                    // 判断是添加还是更新 Sys_Role
                    bool isNew = !_contextRoleList
                        .Any(td => td.id == _selectedRoleEntity.id);

                    if (isNew)
                    {
                        inj_cubeContext.sys_role.Add(_selectedRoleEntity);
                        inj_snackbar.Add($"字段 [{_selectedRoleEntity.name}] 添加成功！", Severity.Success);
                    }
                    else
                    {
                        inj_cubeContext.sys_role.Update(_selectedRoleEntity);
                        inj_snackbar.Add($"字段 [{_selectedRoleEntity.name}] 信息更新成功！", Severity.Success);
                    }

                    // 保存更改
                    await inj_cubeContext.SaveChangesAsync();

                    // 提交事务
                    await transaction.CommitAsync();

                    // 重新加载数据
                    //_roleSelectedUserIds = Enumerable.Empty<string>();
                    await ReloadAll();
                }
                catch (Exception ex)
                {
                    // 回滚事务
                    await transaction.RollbackAsync();

                    inj_logger.LogError(ex, "保存 Sys_Role 时出错:" + ex.Message);
                    inj_snackbar.Add("保存 Sys_Role 时出错:" + ex.Message, Severity.Error);
                }
            }
        }
        IsOpenDrawer_Role = false;
    }
}
