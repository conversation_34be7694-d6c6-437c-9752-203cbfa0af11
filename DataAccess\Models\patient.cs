﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace DataAccess.Models;

/// <summary>
/// 患者表
/// </summary>
public partial class patient
{
    /// <summary>
    /// 主键
    /// </summary>
    public Guid id { get; set; }

    /// <summary>
    /// 唯一患者id
    /// </summary>
    public Guid? unique_id { get; set; }

    /// <summary>
    /// 医院id
    /// </summary>
    public Guid hospital_id { get; set; }

    /// <summary>
    /// 病案号
    /// </summary>
    public string medical_record_number { get; set; }

    /// <summary>
    /// 住院号
    /// </summary>
    public string inpatient_number { get; set; }

    /// <summary>
    /// 姓名
    /// </summary>
    public string name { get; set; }

    /// <summary>
    /// 出生日期
    /// </summary>
    public DateOnly? birthday { get; set; }

    /// <summary>
    /// 年龄
    /// </summary>
    public int? age { get; set; }

    /// <summary>
    /// 性别
    /// </summary>
    public string gender { get; set; }

    /// <summary>
    /// 证件类型
    /// </summary>
    public string sid_type { get; set; }

    /// <summary>
    /// 证件号码
    /// </summary>
    public string sid_number { get; set; }

    /// <summary>
    /// 联系电话类型
    /// </summary>
    public string phone_type { get; set; }

    /// <summary>
    /// 联系电话号码
    /// </summary>
    public string phone_number { get; set; }

    /// <summary>
    /// 微信号
    /// </summary>
    public string weixin_id { get; set; }

    /// <summary>
    /// 微信号绑定电话
    /// </summary>
    public string weixin_phone_number { get; set; }

    /// <summary>
    /// 联系人
    /// </summary>
    public List<string> contact_name { get; set; }

    /// <summary>
    /// 联系人电话
    /// </summary>
    public List<string> contact_phone { get; set; }

    /// <summary>
    /// 联系人关系
    /// </summary>
    public List<string> contact_relation { get; set; }

    /// <summary>
    /// 地址类型
    /// </summary>
    public List<string> address_type { get; set; }

    /// <summary>
    /// 地址内容
    /// </summary>
    public List<string> address_content { get; set; }

    /// <summary>
    /// 邮箱
    /// </summary>
    public List<string> email { get; set; }

    /// <summary>
    /// 民族
    /// </summary>
    public string nation { get; set; }

    /// <summary>
    /// 籍贯
    /// </summary>
    public string native_place { get; set; }

    /// <summary>
    /// 婚姻状况
    /// </summary>
    public string marital_status { get; set; }

    /// <summary>
    /// 教育程度
    /// </summary>
    public string education { get; set; }

    /// <summary>
    /// 职业
    /// </summary>
    public string occupation { get; set; }

    /// <summary>
    /// 死亡时间
    /// </summary>
    public DateTime? death_time { get; set; }

    /// <summary>
    /// 死亡原因
    /// </summary>
    public string death_cause { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime? create_time { get; set; }

    /// <summary>
    /// 是否有效
    /// </summary>
    public bool? is_valid { get; set; }

    /// <summary>
    /// ProjectId
    /// </summary>
    public Guid project_id { get; set; }

    /// <summary>
    /// 患者来源(Care/followup)
    /// </summary>
    public string source_type { get; set; }

    public virtual sys_hospital hospital { get; set; }

    public virtual form_project project { get; set; }
}