﻿@inherits Cmp_Base
@inject ILogger<Cmp_Question> inj_logger
@inject CardService inj_cardService
@inject IDialogService inj_dialogService

<MudItem xl="@(12 / Question.placeholder_width_ratio)">
    <MudPaper Outlined Class="bio-question-type-div" Style=@($"background-color: {(showPopover ? "#f2fff9" : "#ffffff")}")
        @onclick="()=>OpenClosePopover(false)">
        <div style="display: flex; align-items: center; flex-wrap:wrap;row-gap:2px;">
                <h6 style="margin: 0; margin-right: 8px; font-weight: bold;">1. </h6>
                <input type="text" @bind-value="@Question.display_name" width="240px" @onblur="@HandleQuestionNameChanged"
                style="height: 36px; border: 1px solid #666; border-radius: 4px; padding: 0 8px;" />
                <MudTooltip Text="@_questionInfo">
                    <MudIconButton Icon="@Icons.Material.Filled.ErrorOutline" Size="Size.Small" />
                </MudTooltip>
                <Button Color="BColor.Primary" Size="BSize.ExtraSmall" style="margin-right: 1px;"
                        IsOutline="@(Question.placeholder_width_ratio != 1)"
                        OnClick="@(() => HandleQuestionWidthRatioChanged(1))">1/1</Button>
                <Button Color="BColor.Primary" Size="BSize.ExtraSmall" style="margin-right: 1px;"
                        IsOutline="@(Question.placeholder_width_ratio != 2)"
                        OnClick="@(() => HandleQuestionWidthRatioChanged(2))">1/2</Button>
                <Button Color="BColor.Primary" Size="BSize.ExtraSmall" style="margin-right: 1px;"
                IsOutline="@(Question.placeholder_width_ratio != 3)"
                        OnClick="@(() => HandleQuestionWidthRatioChanged(3))">1/3</Button>
               
                <Button IsOutline="true" Color="BColor.Primary" Size="BSize.ExtraSmall" class="ml-1"
                OnClick="@(async () => {
                            await inj_cardService.MoveQuestionUpAsync(_context, Question);
                            await OnParentRefresh.InvokeAsync();
                        })">↑</Button>
                <Button IsOutline="true" Color="BColor.Primary" Size="BSize.ExtraSmall" class="ml-1"
                OnClick="@(async () => {
                            await inj_cardService.MoveQuestionDownAsync(_context, Question);
                            await OnParentRefresh.InvokeAsync();
                        })">↓</Button>
                <MudIconButton Icon="@Icons.Material.Filled.Settings" Size="Size.Medium" Color="Color.Tertiary" Class="ml-2"
                               OnClick="@(()=>OpenClosePopover(null))" />
                <Switch OnColor="BColor.Primary" ShowInnerText="true"  OnInnerText="隐" OffInnerText="显" @bind-Value="@Question.is_hidden" OnValueChanged="@OnParameterChanged" style="flex:0" />
                <MudIconButton Icon="@Icons.Material.Filled.Delete" Size="Size.Small" Color="Color.Error" Style="margin-left:auto;margin-right:20px;"
                OnClick="@(() => DeleteQuestion())" />
        </div>

        <div style="display: flex;align-items: center;">
            QID：
            @Question.id
            - CID：
            @Question.column_definition_id
        </div>

        <div style="display: flex;align-items: center;">
            @switch(Question.data_type)
            {
                case "文本":
                    <Cmp_QuestionText _context="@_context" Question="@Question"  />
                    break;
                case "数值":
                    <Cmp_QuestionNumber _context="@_context" Question="@Question" />
                    break;
                case "选择":
                    <Cmp_QuestionSelect _context="@_context" Question="@Question" />
                    break;
                case "日期":
                    <Cmp_QuestionDate _context="@_context" Question="@Question"  />
                    break;
                case "布尔":
                    <Cmp_QuestionBoolean _context="@_context" Question="@Question" />
                    break;
                case "文件":
                    <Cmp_QuestionFile _context="@_context" Question="@Question" />
                    break;
            }
            <Cmp_QuestionConfig ShowPop="@showPopover" Question="@Question" _context="@_context" QuestionType="@Question.data_type" OnParentRefresh="@OnRefreshThis"
            OnShowPopChanged="@OnShowPopChanged" />
        </div>
    </MudPaper>
</MudItem>

@code {
    [Parameter]
    public required CubeContext _context { get; set; }
    [Parameter]
    public required form_question Question { get; set; }
    [Parameter]
    public EventCallback OnParentRefresh { get; set; }  // 给父组件的刷新回调事件    

    private string _questionInfo {
        get {
            var info = $"来自【{Question.table_name}】分类下的【{Question.column_name}】";
            return info;
        }
    }
    private bool showPopover = false;

    private void OpenClosePopover(bool? isShow)
    {
        showPopover = isShow ?? !showPopover;
    }

    private void OnShowPopChanged(bool value)
    {
        showPopover = value;
    }

    private async Task HandleQuestionNameChanged()
    {
        inj_logger.LogWarning($"{Question.display_name}:问题名称被修改");
        await _context.SaveChangesAsync();
    }

    private async Task HandleQuestionWidthRatioChanged(int ratio)
    {
        inj_logger.LogWarning(Question.display_name + ":问题宽度比例被修改");
        Question.placeholder_width_ratio = (short)ratio;
        await _context.SaveChangesAsync();
    }

    private async Task DeleteQuestion()
    {
        // 弹出确认对话框
        var confirm = await inj_dialogService.ShowMessageBox(
            $"确定要删除问题【{Question.display_name}】吗？", 
            "", 
            yesText: "确定", 
            cancelText: "取消"
        );

        if (confirm == true)
        {
            await inj_cardService.DeleteQuestionAsync(_context, Question);
            await OnParentRefresh.InvokeAsync();
        }
    }

    private async Task OnRefreshThis()
    {
        if (OnParentRefresh.HasDelegate)
        {
            await OnParentRefresh.InvokeAsync();
        }
    }

    private async Task OnParameterChanged<T>(T _)
    {
        await _context.SaveChangesAsync();
    }
}
