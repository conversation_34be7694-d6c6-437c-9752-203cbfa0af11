using System;
using Infrastructure;
using DataAccess.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;

namespace BusinessLogic.Cube;

public class CardService
{
    private readonly ILogger<CardService> _logger;
    private readonly IConfiguration _configuration;

    public CardService(ILogger<CardService> logger, IConfiguration configuration)
    {
        _logger = logger;
        _configuration = configuration;
    }

    /// <summary>
    /// 添加卡片到根目录
    /// </summary>
    public async Task<form_card> AddCardToRootAsync(CubeContext context, form_form thisForm, form_card? newCard = null)
    {
        if (newCard == null)
        {
            newCard = CreateNewCard(thisForm);
        }

        var items = await GetItemInContainerAsync(context, thisForm, null);

        using var transaction = await context.Database.BeginTransactionAsync(System.Data.IsolationLevel.RepeatableRead);
        try
        {
            _logger.LogWarning($"AddCardToRootAsync: {newCard.name} - {newCard.id}");
            if (items == null || items.Count == 0)
            {
                newCard.pre_uid = null;
                newCard.next_uid = null;
            }
            else
            {
                var lastItems = items.Where(item => item.next_uid==null).ToList();
                if (lastItems == null || lastItems.Count != 1)
                {
                    throw new Exception("根目录卡片链表异常");
                }
                else
                {
                    var lastItem = lastItems.First();
                    form_card lastCard = await context.form_card.FindAsync(lastItem.id);
                    lastCard.next_uid = newCard.id;
                    newCard.pre_uid = lastCard.id;
                    context.form_card.Update(lastCard);
                }
            }

            // 插入或更新到数据库
            var existingCard = await context.form_card.FindAsync(newCard.id);
            if (existingCard == null)
            {
                // 数据库中不存在此卡片，使用Add
                context.form_card.Add(newCard);
            }
            else
            {
                // 数据库中已存在此卡片，使用Update
                context.Entry(existingCard).CurrentValues.SetValues(newCard);
            }
            await context.SaveChangesAsync();
            await transaction.CommitAsync();

            return newCard;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "添加卡片到根目录失败。");
            await transaction.RollbackAsync();
            throw;
        }
    }

    /// <summary>
    /// 添加卡片到父卡片下
    /// </summary>
    public async Task<form_card> AddCardToParentCardAsync(CubeContext context, form_card parentCard, form_card? newCard = null)
    {
        if (newCard == null)
        {
            newCard = CreateNewCard(parentCard.form);
        }
        newCard.parent_id = parentCard.id;

        // 获取父卡片容器中的最后一个项
        var items = await GetItemInContainerAsync(context, parentCard.form, parentCard);

        using var transaction = await context.Database.BeginTransactionAsync(System.Data.IsolationLevel.RepeatableRead);
        try
        {
            _logger.LogWarning($"AddCardToParentCardAsync: {newCard.name} - {newCard.id} in {parentCard.form.name} - {parentCard.name} - {parentCard.id}");
            if (items == null || items.Count == 0)
            {
                newCard.pre_uid = null;
                newCard.next_uid = null;
            }
            else
            {
                var lastItems = items.Where(item => item.next_uid==null).ToList();
                if (lastItems == null || lastItems.Count != 1)
                {
                    throw new Exception("父卡片容器中的卡片链表异常");
                }
                else
                {
                    var lastItem = lastItems.First();
                    newCard.pre_uid = lastItem.id;

                    if (lastItem.item_type == "Card")
                    {
                        form_card lastCard = await context.form_card.FindAsync(lastItem.id);
                        lastCard.next_uid = newCard.id;
                        context.form_card.Update(lastCard);
                    }
                    else if (lastItem.item_type == "Question")
                    {
                        form_question lastQuestion = await context.form_question.FindAsync(lastItem.id);
                        lastQuestion.next_uid = newCard.id;
                        context.form_question.Update(lastQuestion);
                    }
                }
            }

            // 插入或更新到数据库
            var existingCard = await context.form_card.FindAsync(newCard.id);
            if (existingCard == null)
            {
                // 数据库中不存在此卡片，使用Add
                context.form_card.Add(newCard);
            }
            else
            {
                // 数据库中已存在此卡片，使用Update
                context.Entry(existingCard).CurrentValues.SetValues(newCard);
            }
            await context.SaveChangesAsync();
            await transaction.CommitAsync();

            return newCard;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "添加卡片到父卡片失败。");
            await transaction.RollbackAsync();
            throw;
        }
    }

    public async Task<form_question> AddQuestionToCardAsync(CubeContext context, form_card card, column_definition column)
    {
        var newQuestion = new form_question
        {
            id = SequentialGuidGenerator.NewGuid(),
            card_id = card.id,
            display_name = column.display_name,
            table_definition_id = column.table_id,
            table_name = column.table.unique_name,
            column_definition_id = column.id,
            column_name = column.unique_name,
            data_type = column.data_type,
            is_tip_label = false,
            form_id = card.form_id,
            form_name = card.form_name,
            form_set_id = card.form_set_id,
            form_set_name = card.form_set_name,
            project_id = card.project_id,
            project_name = card.project_name,
            ward_id = card.ward_id,
            ward_name = card.ward_name,
            department_id = card.department_id,
            department_name = card.department_name,
            hospital_id = card.hospital_id,
            hospital_name = card.hospital_name,
            created_at = DateTime.Now,
            updated_at = DateTime.Now
        };

        if(column.option_set != null)
        {
            newQuestion.select_sorted_option_subset = column.option_set;
        }

        _logger.LogWarning($"AddQuestionToCardAsync: {newQuestion.display_name} - {newQuestion.id} in {card.form.name} - {card.name} - {card.id}");
        var items = await GetItemInContainerAsync(context, card.form, card);
        if (items != null && items.Count > 0)
        {
            var lastItems = items.Where(item => item.next_uid==null).ToList();
            if (lastItems == null || lastItems.Count != 1)
            {
                throw new Exception("父卡片容器中的卡片链表异常");
            }
            else
            {
                var lastItem = lastItems.First();
                newQuestion.pre_uid = lastItem.id;

                if (lastItem.item_type == "Question")
                {
                    form_question lastQuestion = await context.form_question.FindAsync(lastItem.id);
                    lastQuestion.next_uid = newQuestion.id;
                    context.form_question.Update(lastQuestion);
                }
                else if (lastItem.item_type == "Card")
                {
                    form_card lastCard = await context.form_card.FindAsync(lastItem.id);
                    lastCard.next_uid = newQuestion.id;
                    context.form_card.Update(lastCard);
                }
            }
        }

        context.form_question.Add(newQuestion);
        await context.SaveChangesAsync();

        return newQuestion;
    }

    public async Task MoveCardUpAsync(CubeContext context, form_card card)
    {
        if (card.pre_uid == null)
        {
            _logger.LogWarning($"卡片 {card.id} 没有前驱卡片。");
            return;
        }

        _logger.LogWarning($"MoveCardUpAsync: {card.name} - {card.id} in {card.form.name}");

        var itemList = await GetItemInContainerAsync(context, card.form, card.parent);
        var item2 = itemList.FirstOrDefault(item => item.id == card.id);
        var item1 = itemList.FirstOrDefault(item => item.id == item2.pre_uid);
        await SwapAdjacentItemAsync(context, itemList, item1, item2);
    }

    public async Task MoveCardDownAsync(CubeContext context, form_card card)
    {
        if (card.next_uid == null)
        {
            _logger.LogWarning($"卡片 {card.id} 没有后继卡片。");
            return;
        }

        _logger.LogWarning($"MoveCardDownAsync: {card.name} - {card.id} in {card.form.name}");

        var itemList = await GetItemInContainerAsync(context, card.form, card.parent);
        var item1 = itemList.FirstOrDefault(item => item.id == card.id);
        var item2 = itemList.FirstOrDefault(item => item.id == item1.next_uid);
        await SwapAdjacentItemAsync(context, itemList, item1, item2);
    }

    public async Task MoveQuestionUpAsync(CubeContext context, form_question question)
    {
        if (question.pre_uid == null)
        {
            _logger.LogWarning($"问题 {question.id} 没有前驱问题。");
            return;
        }

        _logger.LogWarning($"MoveQuestionUpAsync: {question.display_name} - {question.id} in {question.card.form.name} - {question.card.name} - {question.card.id}");

        var itemList = await GetItemInContainerAsync(context, question.card.form, question.card);
        var item2 = itemList.FirstOrDefault(item => item.id == question.id);
        var item1 = itemList.FirstOrDefault(item => item.id == item2.pre_uid);
        await SwapAdjacentItemAsync(context, itemList, item1, item2);
    }

    public async Task MoveQuestionDownAsync(CubeContext context, form_question question)
    {
        if (question.next_uid == null)
        {
            _logger.LogWarning($"问题 {question.id} 没有后继问题。");
            return;
        }

        _logger.LogWarning($"MoveQuestionDownAsync: {question.display_name} - {question.id} in {question.card.form.name} - {question.card.name} - {question.card.id}");

        var itemList = await GetItemInContainerAsync(context, question.card.form, question.card);
        var item1 = itemList.FirstOrDefault(item => item.id == question.id);
        var item2 = itemList.FirstOrDefault(item => item.id == item1.next_uid);
        await SwapAdjacentItemAsync(context, itemList, item1, item2);
    }

    private async Task SwapAdjacentItemAsync(CubeContext context, List<ContainerItemViewModel> itemList, ContainerItemViewModel item1, ContainerItemViewModel item2)
    {
        var item0 = itemList.FirstOrDefault(item => item.id == item1.pre_uid);
        var item3 = itemList.FirstOrDefault(item => item.id == item2.next_uid);

        using var transaction = await context.Database.BeginTransactionAsync(System.Data.IsolationLevel.RepeatableRead);
        try
        {
            _logger.LogWarning($"SwapAdjacentItemAsync: {item1.name} - {item1.id} - {(item1.pre_uid==null?Guid.Empty:item1.pre_uid)} - {(item1.next_uid==null?Guid.Empty:item1.next_uid)} - {item1.item_type}");
            _logger.LogWarning($"SwapAdjacentItemAsync: {item2.name} - {item2.id} - {(item2.pre_uid==null?Guid.Empty:item2.pre_uid)} - {(item2.next_uid==null?Guid.Empty:item2.next_uid)} - {item2.item_type}");
            // 通过更新item1和item2的实际实体对象的Preuid和Nextuid，实现item1和item2的实体对象在链表中位置的交换
            var typeTuple = (item1.item_type, item2.item_type);
            if (typeTuple == ("Card", "Card"))
            {
                var item1Entity = await context.form_card.FindAsync(item1.id);
                var item2Entity = await context.form_card.FindAsync(item2.id);

                item1Entity.next_uid = item2Entity.next_uid;
                item2Entity.pre_uid = item1Entity.pre_uid;

                item2Entity.next_uid = item1Entity.id;
                item1Entity.pre_uid = item2Entity.id;

                context.form_card.Update(item1Entity);
                context.form_card.Update(item2Entity);
            }
            else if (typeTuple == ("Card", "Question"))
            {
                var item1Entity = await context.form_card.FindAsync(item1.id);
                var item2Entity = await context.form_question.FindAsync(item2.id);

                item1Entity.next_uid = item2Entity.next_uid;
                item2Entity.pre_uid = item1Entity.pre_uid;

                item2Entity.next_uid = item1Entity.id;
                item1Entity.pre_uid = item2Entity.id;

                context.form_card.Update(item1Entity);
                context.form_question.Update(item2Entity);
            }
            else if (typeTuple == ("Question", "Card"))
            {
                var item1Entity = await context.form_question.FindAsync(item1.id);
                var item2Entity = await context.form_card.FindAsync(item2.id);

                item1Entity.next_uid = item2Entity.next_uid;
                item2Entity.pre_uid = item1Entity.pre_uid;

                item2Entity.next_uid = item1Entity.id;
                item1Entity.pre_uid = item2Entity.id;

                context.form_question.Update(item1Entity);
                context.form_card.Update(item2Entity);
            }
            else if (typeTuple == ("Question", "Question"))
            {
                var item1Entity = await context.form_question.FindAsync(item1.id);
                var item2Entity = await context.form_question.FindAsync(item2.id);

                item1Entity.next_uid = item2Entity.next_uid;
                item2Entity.pre_uid = item1Entity.pre_uid;

                item2Entity.next_uid = item1Entity.id;
                item1Entity.pre_uid = item2Entity.id;

                context.form_question.Update(item1Entity);
                context.form_question.Update(item2Entity);
            }

            // 如果item0存在，更新item0的实际实体对象Nextuid为item2的Id
            if (item0 != null)
            {
                if (item0.item_type == "Card")
                {
                    var item0Entity = await context.form_card.FindAsync(item0.id);
                    item0Entity.next_uid = item2.id;
                    context.form_card.Update(item0Entity);
                }
                else if (item0.item_type == "Question")
                {
                    var item0Entity = await context.form_question.FindAsync(item0.id);
                    item0Entity.next_uid = item2.id;
                    context.form_question.Update(item0Entity);
                }
            }

            // 如果item3存在，更新item3的实际实体对象Preuid为item1的Id
            if (item3 != null)
            {
                if (item3.item_type == "Card")
                {
                    var item3Entity = await context.form_card.FindAsync(item3.id);
                    item3Entity.pre_uid = item1.id;
                    context.form_card.Update(item3Entity);
                }
                else if (item3.item_type == "Question")
                {
                    var item3Entity = await context.form_question.FindAsync(item3.id);
                    item3Entity.pre_uid = item1.id;
                    context.form_question.Update(item3Entity);
                }
            }

            await context.SaveChangesAsync();
            await transaction.CommitAsync();
        }
        catch (DbUpdateConcurrencyException ex)
        {
            _logger.LogError(ex, "发生并发冲突，请刷新后重试。");
            await transaction.RollbackAsync();
            throw new InvalidOperationException("操作失败：数据已被其他用户修改，请刷新后重试。", ex);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "交换项失败。");
            await transaction.RollbackAsync();
            throw;
        }
    }

    /// <summary>
    /// 将卡片移动到另一个容器（父卡片或根目录）的尾部
    /// </summary>
    /// <param unique_name="context">当前的数据库上下文。</param>
    /// <param unique_name="card">要移动的卡片。</param>
    /// <param unique_name="newParentCard">新的父卡片。如果为 null，则移动到根目录。</param>
    public async Task MoveCardToAnotherContainerAsync(CubeContext context, form_card card, form_card? newParentCard)
    {
        _logger.LogWarning($"将卡片移动到另一个容器: from {card.form.name}-{card.name}({card.id}) to {(newParentCard==null? "根目录":$"{newParentCard.name}({newParentCard.id})")}");
        using var transaction = await context.Database.BeginTransactionAsync(System.Data.IsolationLevel.RepeatableRead);
        try
        {
            // 获取被移动的卡片在容器中的位置
            var itemList = await GetItemInContainerAsync(context, card.form, card.parent);
            var item2 = itemList.FirstOrDefault(item => item.id == card.id);
            var item1 = itemList.FirstOrDefault(item => item.id == item2.pre_uid);
            var item3 = itemList.FirstOrDefault(item => item.id == item2.next_uid);

            if(item1 != null)
            {
                _logger.LogWarning($"MoveCardToAnotherContainerAsync: 前obj {item1.name} - {(item1.pre_uid==null?Guid.Empty:item1.pre_uid)} - {item1.id} - {(item1.next_uid==null?Guid.Empty:item1.next_uid)} - {item1.item_type}");
            }

            _logger.LogWarning($"MoveCardToAnotherContainerAsync: 中obj {item2.name} - {(item2.pre_uid==null?Guid.Empty:item2.pre_uid)} - {item2.id} - {(item2.next_uid==null?Guid.Empty:item2.next_uid)} - {item2.item_type}"); 

            if(item3 != null)
            {
                _logger.LogWarning($"MoveCardToAnotherContainerAsync: 后obj {item3.name} - {(item3.pre_uid==null?Guid.Empty:item3.pre_uid)} - {item3.id} - {(item3.next_uid==null?Guid.Empty:item3.next_uid)} - {item3.item_type}");
            }

            // 更新item1的Nextuid为item2的Nextuid
            if (item1 != null)
            {                
                if (item1.item_type == "Card")
                {
                    var item1Entity = await context.form_card.FindAsync(item1.id);
                    if (item2.next_uid != null)
                    {
                        item1Entity.next_uid = item2.next_uid;
                    }
                    else
                    {
                        item1Entity.next_uid = null;
                    }
                    context.form_card.Update(item1Entity);
                    _logger.LogWarning($"修改后item1: {(item1Entity.pre_uid==null?Guid.Empty:item1Entity.pre_uid)} - {item1Entity.id} - {(item1Entity.next_uid==null?Guid.Empty:item1Entity.next_uid)} - Card");
                }
                else if (item1.item_type == "Question")
                {
                    var item1Entity = await context.form_question.FindAsync(item1.id);
                    if (item2.next_uid != null)
                    {
                        item1Entity.next_uid = item2.next_uid;
                    }
                    else
                    {
                        item1Entity.next_uid = null;
                    }
                    context.form_question.Update(item1Entity);
                    _logger.LogWarning($"修改后item1: {(item1Entity.pre_uid==null?Guid.Empty:item1Entity.pre_uid)} - {item1Entity.id} - {(item1Entity.next_uid==null?Guid.Empty:item1Entity.next_uid)} - Question");
                }
            }

            // 更新item3的Preuid为item2的Preuid
            if (item3 != null)
            {                
                if (item3.item_type == "Card")
                {
                    var item3Entity = await context.form_card.FindAsync(item3.id);
                    if (item2.pre_uid != null)
                    {
                        item3Entity.pre_uid = item2.pre_uid;
                    }
                    else
                    {
                        item3Entity.pre_uid = null;
                    }
                    context.form_card.Update(item3Entity);
                    _logger.LogWarning($"修改后item3: {(item3Entity.pre_uid==null?Guid.Empty:item3Entity.pre_uid)} - {item3Entity.id} - {(item3Entity.next_uid==null?Guid.Empty:item3Entity.next_uid)} - Card");
                }
                else if (item3.item_type == "Question")
                {
                    var item3Entity = await context.form_question.FindAsync(item3.id);
                    if (item2.pre_uid != null)
                    {
                        item3Entity.pre_uid = item2.pre_uid;
                    }
                    else
                    {
                        item3Entity.pre_uid = null;
                    }
                    context.form_question.Update(item3Entity);
                    _logger.LogWarning($"修改后item3: {(item3Entity.pre_uid==null?Guid.Empty:item3Entity.pre_uid)} - {item3Entity.id} - {(item3Entity.next_uid==null?Guid.Empty:item3Entity.next_uid)} - Question");
                }
            }

            // 记录card移动前的状态
            _logger.LogWarning($"移动前card: {(card.pre_uid==null?Guid.Empty:card.pre_uid)} - {card.id} - {(card.next_uid==null?Guid.Empty:card.next_uid)} - Card");

            // 更新card的Preuid
            var newContainerItemList = await GetItemInContainerAsync(context, card.form, newParentCard);
            if (newContainerItemList != null && newContainerItemList.Count > 0)
            {
                var lastItem = newContainerItemList.LastOrDefault();
                if (lastItem != null)
                {
                    _logger.LogWarning($"目标容器最后一项: {(lastItem.pre_uid==null?Guid.Empty:lastItem.pre_uid)} - {lastItem.id} - {(lastItem.next_uid==null?Guid.Empty:lastItem.next_uid)} - {lastItem.item_type} - {lastItem.name}");
                    
                    card.pre_uid = lastItem.id;
                    if (lastItem.item_type == "Card")
                    {
                        var lastItemEntity = await context.form_card.FindAsync(lastItem.id);
                        lastItemEntity.next_uid = card.id;
                        context.form_card.Update(lastItemEntity);
                        _logger.LogWarning($"修改后lastItem: {(lastItemEntity.pre_uid==null?Guid.Empty:lastItemEntity.pre_uid)} - {lastItemEntity.id} - {(lastItemEntity.next_uid==null?Guid.Empty:lastItemEntity.next_uid)} - Card");
                    }
                    else if (lastItem.item_type == "Question")
                    {
                        var lastItemEntity = await context.form_question.FindAsync(lastItem.id);
                        lastItemEntity.next_uid = card.id;
                        context.form_question.Update(lastItemEntity);
                        _logger.LogWarning($"修改后lastItem: {(lastItemEntity.pre_uid==null?Guid.Empty:lastItemEntity.pre_uid)} - {lastItemEntity.id} - {(lastItemEntity.next_uid==null?Guid.Empty:lastItemEntity.next_uid)} - Question");
                    }
                }
            }
            else
            {
                card.pre_uid = null;
            }

            card.next_uid = null;
            card.parent_id = newParentCard?.id;
            context.form_card.Update(card);

            // 记录card移动后的状态
            _logger.LogWarning($"移动后card: parentId:{(card.parent_id==null?Guid.Empty:card.parent_id)} - preuid:{(card.pre_uid==null?Guid.Empty:card.pre_uid)} - id: {card.id} - nextuid:{(card.next_uid==null?Guid.Empty:card.next_uid)} - Card");

            // 保存更改并提交事务
            await context.SaveChangesAsync();
            await transaction.CommitAsync();

            _logger.LogWarning($"卡片 {card.id} 已成功移动到 {(newParentCard == null ? "根目录" : $"父卡片 {newParentCard.id}")}。");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"移动卡片 {card.id} 失败。");
            await transaction.RollbackAsync();
            throw;
        }
    }

    /// <summary>
    /// 将卡片及其所有子内容移动到另一个容器（可以跨表单集）
    /// </summary>
    /// <param unique_name="context">数据库上下文</param>
    /// <param unique_name="card">要移动的卡片</param>
    /// <param unique_name="targetForm">目标表单</param>
    /// <param unique_name="targetParentCard">目标父卡片（如果为null则移动到根目录）</param>
    /// <returns>移动操作的结果</returns>
    public async Task<CardMoveResult> CrossMoveCardToAnotherContainerAsync(CubeContext context, form_card card, form_form? targetForm, form_card? targetParentCard)
    {
        // 创建结果对象
        var result = new CardMoveResult 
        { 
            IsSuccess = false,
            Message = "",
            MovedCardCount = 0,
            MovedQuestionCount = 0
        };
        
        // 参数验证
        if (card == null)
        {
            result.Message = "要移动的卡片不能为空";
            _logger.LogError(result.Message);
            return result;
        }

        // 如果目标表单为空，则使用卡片当前所在表单
        if (targetForm == null)
        {
            targetForm = card.form;
        }
        
        // 检查是否存在循环依赖（不能将卡片移动到其子卡片中）
        if (targetParentCard != null && await IsCardChildOfAsync(context, targetParentCard.id, card.id))
        {
            result.Message = $"不能将卡片移动到其子卡片中，这会导致循环依赖: {card.name}({card.id}) -> {targetParentCard.name}({targetParentCard.id})";
            _logger.LogError(result.Message);
            return result;
        }
        
        // 检查目标表单和源卡片是否属于同一项目
        if (card.project_id != targetForm.project_id)
        {
            result.Message = $"不支持跨项目移动卡片: 源项目ID: {card.project_id}, 目标项目ID: {targetForm.project_id}";
            _logger.LogError(result.Message);
            return result;
        }
        
        // 记录移动操作起始信息
        bool isCrossForm = card.form_id != targetForm.id;
        bool isCrossFormSet = card.form_set_id != targetForm.form_set_id;
        
        _logger.LogWarning($"开始移动卡片: 从 {card.form?.name ?? "未知表单"}({card.form_id})-{card.name}({card.id}) " +
                          $"到 {targetForm.name}({targetForm.id})-" +
                          $"{(targetParentCard == null ? "根目录" : $"{targetParentCard.name}({targetParentCard.id})")}");
        
        if (isCrossForm)
        {
            _logger.LogWarning($"跨表单移动: {(isCrossFormSet ? "跨表单集" : "同表单集内")}");
        }
        
        // 开始事务处理
        using var transaction = await context.Database.BeginTransactionAsync(System.Data.IsolationLevel.RepeatableRead);
        try
        {
            // 1. 收集所有需要移动的卡片和问题（递归）
            var cardsToMove = new List<form_card>();
            var questionsToMove = new List<form_question>();
            
            // 首先添加源卡片
            cardsToMove.Add(card);
            
            // 递归收集所有子卡片及问题
            await CollectChildItemsRecursivelyAsync(context, card, cardsToMove, questionsToMove);
            
            _logger.LogWarning($"需要移动的卡片总数: {cardsToMove.Count}, 问题总数: {questionsToMove.Count}");
            
            // 2. 从源容器中移除卡片（维护源链表）
            var sourceItemList = await GetItemInContainerAsync(context, card.form, card.parent);
            if (sourceItemList != null)
            {
                var sourceItem = sourceItemList.FirstOrDefault(item => item.id == card.id);
                if (sourceItem != null)
                {
                    _logger.LogWarning($"从源容器移除卡片: {sourceItem.name}({sourceItem.id}) - " +
                                      $"pre:{(sourceItem.pre_uid == null ? "null" : sourceItem.pre_uid.ToString())} " +
                                      $"next:{(sourceItem.next_uid == null ? "null" : sourceItem.next_uid.ToString())}");
                    
                    var prevItem = sourceItemList.FirstOrDefault(item => item.id == sourceItem.pre_uid);
                    var nextItem = sourceItemList.FirstOrDefault(item => item.id == sourceItem.next_uid);
                    
                    // 更新前一项的next_uid指向后一项
                    if (prevItem != null)
                    {
                        await UpdateItemPointerAsync(context, prevItem, nextPointer: sourceItem.next_uid);
                    }
                    
                    // 更新后一项的pre_uid指向前一项
                    if (nextItem != null)
                    {
                        await UpdateItemPointerAsync(context, nextItem, prevPointer: sourceItem.pre_uid);
                    }
                }
            }
            
            // 3. 更新所有卡片和问题的表单信息（如果是跨表单移动）
            if (isCrossForm)
            {
                _logger.LogWarning($"更新所有项目的表单信息: 从 {card.form_id} 到 {targetForm.id}");
                
                foreach (var cardToUpdate in cardsToMove)
                {
                    // 更新表单相关信息
                    cardToUpdate.form_id = targetForm.id;
                    cardToUpdate.form_name = targetForm.name;
                    
                    if (isCrossFormSet)
                    {
                        cardToUpdate.form_set_id = targetForm.form_set_id;
                        cardToUpdate.form_set_name = targetForm.form_set_name;
                    }
                    
                    // 确保项目信息保持一致（应当都是同一项目）
                    cardToUpdate.project_id = targetForm.project_id;
                    cardToUpdate.project_name = targetForm.project_name;
                    
                    // 医院/部门信息也应保持与目标表单一致
                    cardToUpdate.hospital_id = targetForm.hospital_id;
                    cardToUpdate.hospital_name = targetForm.hospital_name;
                    cardToUpdate.department_id = targetForm.department_id;
                    cardToUpdate.department_name = targetForm.department_name;
                    cardToUpdate.ward_id = targetForm.ward_id;
                    cardToUpdate.ward_name = targetForm.ward_name;
                    
                    cardToUpdate.updated_at = DateTime.Now;
                    
                    context.form_card.Update(cardToUpdate);
                }
                
                foreach (var questionToUpdate in questionsToMove)
                {
                    // 更新问题的表单相关信息
                    questionToUpdate.form_id = targetForm.id;
                    questionToUpdate.form_name = targetForm.name;
                    
                    if (isCrossFormSet)
                    {
                        questionToUpdate.form_set_id = targetForm.form_set_id;
                        questionToUpdate.form_set_name = targetForm.form_set_name;
                    }
                    
                    // 确保项目信息保持一致
                    questionToUpdate.project_id = targetForm.project_id;
                    questionToUpdate.project_name = targetForm.project_name;
                    
                    // 医院/部门信息也应保持与目标表单一致
                    questionToUpdate.hospital_id = targetForm.hospital_id;
                    questionToUpdate.hospital_name = targetForm.hospital_name;
                    questionToUpdate.department_id = targetForm.department_id;
                    questionToUpdate.department_name = targetForm.department_name;
                    questionToUpdate.ward_id = targetForm.ward_id;
                    questionToUpdate.ward_name = targetForm.ward_name;
                    
                    questionToUpdate.updated_at = DateTime.Now;
                    
                    context.form_question.Update(questionToUpdate);
                }
            }
            
            // 4. 更新源卡片的父卡片指向和链表指针
            card.parent_id = targetParentCard?.id;
            
            // 初始化卡片的链表指针，将在下面根据目标容器情况更新
            card.pre_uid = null;
            card.next_uid = null;
            
            // 5. 将卡片添加到目标容器的链表尾部
            var targetContainerItems = await GetItemInContainerAsync(context, targetForm, targetParentCard);
            
            if (targetContainerItems != null && targetContainerItems.Count > 0)
            {
                var lastItem = targetContainerItems.LastOrDefault(item => item.next_uid == null);
                
                if (lastItem != null)
                {
                    _logger.LogWarning($"目标容器的最后一项: {lastItem.name}({lastItem.id}) - " +
                                      $"{lastItem.item_type} - " +
                                      $"pre:{(lastItem.pre_uid == null ? "null" : lastItem.pre_uid.ToString())} " +
                                      $"next:{(lastItem.next_uid == null ? "null" : lastItem.next_uid.ToString())}");
                    
                    // 更新移动卡片的前指针
                    card.pre_uid = lastItem.id;
                    
                    // 更新目标容器最后一项的后指针
                    if (lastItem.item_type == "Card")
                    {
                        var lastCardEntity = await context.form_card.FindAsync(lastItem.id);
                        if (lastCardEntity != null)
                        {
                            lastCardEntity.next_uid = card.id;
                            context.form_card.Update(lastCardEntity);
                            _logger.LogWarning($"更新目标容器最后一项的后指针: {lastCardEntity.name}({lastCardEntity.id}) -> {card.name}({card.id})");
                        }
                    }
                    else if (lastItem.item_type == "Question")
                    {
                        var lastQuestionEntity = await context.form_question.FindAsync(lastItem.id);
                        if (lastQuestionEntity != null)
                        {
                            lastQuestionEntity.next_uid = card.id;
                            context.form_question.Update(lastQuestionEntity);
                            _logger.LogWarning($"更新目标容器最后一项的后指针: {lastQuestionEntity.display_name}({lastQuestionEntity.id}) -> {card.name}({card.id})");
                        }
                    }
                }
            }
            
            // 更新源卡片
            context.form_card.Update(card);
            
            // 记录移动后的状态
            _logger.LogWarning($"移动后card状态: parentId:{(card.parent_id == null ? "null" : card.parent_id.ToString())} - " +
                              $"preuid:{(card.pre_uid == null ? "null" : card.pre_uid.ToString())} - " +
                              $"id:{card.id} - " +
                              $"nextuid:{(card.next_uid == null ? "null" : card.next_uid.ToString())}");
            
            // 6. 保存所有更改并提交事务
            await context.SaveChangesAsync();
            await transaction.CommitAsync();
            
            // 7. 更新结果信息
            result.IsSuccess = true;
            result.MovedCardCount = cardsToMove.Count;
            result.MovedQuestionCount = questionsToMove.Count;
            result.Message = $"成功移动卡片 {card.name} 及其{cardsToMove.Count-1}个子卡片和{questionsToMove.Count}个问题到{(targetParentCard == null ? "根目录" : targetParentCard.name)}";
            
            _logger.LogWarning(result.Message);
            return result;
        }
        catch (DbUpdateConcurrencyException ex)
        {
            _logger.LogError(ex, $"移动卡片时发生并发冲突: {card.id}");
            await transaction.RollbackAsync();
            
            result.Message = "操作失败：数据已被其他用户修改，请刷新后重试";
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"移动卡片失败: {card.id} - {ex.Message}");
            await transaction.RollbackAsync();
            
            result.Message = $"移动卡片失败: {ex.Message}";
            return result;
        }
    }

    /// <summary>
    /// 检查一个卡片是否是另一个卡片的子卡片（递归检查）
    /// </summary>
    /// <param unique_name="context">数据库上下文</param>
    /// <param unique_name="parentCardId">可能的父卡片ID</param>
    /// <param unique_name="childCardId">可能的子卡片ID</param>
    /// <returns>如果是子卡片关系则返回true</returns>
    private async Task<bool> IsCardChildOfAsync(CubeContext context, Guid parentCardId, Guid childCardId)
    {
        // 直接相同就是循环依赖
        if (parentCardId == childCardId)
        {
            return true;
        }
        
        // 获取子卡片的所有子卡片
        var childCards = await context.form_card
            .Where(c => c.parent_id == childCardId)
            .ToListAsync();
        
        // 递归检查每个子卡片
        foreach (var subChild in childCards)
        {
            if (subChild.id == parentCardId || await IsCardChildOfAsync(context, parentCardId, subChild.id))
            {
                return true;
            }
        }
        
        return false;
    }

    /// <summary>
    /// 删除指定的卡片，并级联删除其所有子卡片和相关的问题。
    /// </summary>
    /// <param unique_name="context">当前的数据库上下文。</param>
    /// <param unique_name="card">要删除的卡片。</param>
    public async Task DeleteCardAsync(CubeContext context, form_card card)
    {      
        using var transaction = await context.Database.BeginTransactionAsync(System.Data.IsolationLevel.RepeatableRead);
        try
        {
            _logger.LogWarning($"DeleteCardAsync: {card.name} - {card.id} in {card.form.name}");
            await DeleteCardRecursivelyAsync(context, card);
            await context.SaveChangesAsync();
            await transaction.CommitAsync();
            _logger.LogWarning($"卡片及其所有子卡片和问题已成功删除。");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"删除卡片 {card.id} 失败。");
            await transaction.RollbackAsync();
            throw;
        }
    }

    private async Task DeleteCardRecursivelyAsync(CubeContext context, form_card card)
    {
        // 遍历卡片，删除所有子卡片和相关的问题
        foreach (var childCard in card.Inverseparent)
        {
            await DeleteCardRecursivelyAsync(context, childCard);
        }

        foreach (var question in card.form_question)
        {
            context.form_question.Remove(question);
        }
        
        var itemList = await GetItemInContainerAsync(context, card.form, card.parent);
        var item2 = itemList.FirstOrDefault(item => item.id == card.id);
        var item1 = itemList.FirstOrDefault(item => item.id == item2.pre_uid);
        var item3 = itemList.FirstOrDefault(item => item.id == item2.next_uid);

        if (item1 != null)
        {
            if (item1.item_type == "Card")
            {
                var item1Entity = await context.form_card.FindAsync(item1.id);
                item1Entity.next_uid = item2.next_uid;
                context.form_card.Update(item1Entity);
            }
            else if (item1.item_type == "Question")
            {
                var item1Entity = await context.form_question.FindAsync(item1.id);
                item1Entity.next_uid = item2.next_uid;
                context.form_question.Update(item1Entity);
            }
        }

        if (item3 != null)
        {
            if (item3.item_type == "Card")
            {
                var item3Entity = await context.form_card.FindAsync(item3.id);
                item3Entity.pre_uid = item2.pre_uid;
                context.form_card.Update(item3Entity);
            }
            else if (item3.item_type == "Question")
            {
                var item3Entity = await context.form_question.FindAsync(item3.id);
                item3Entity.pre_uid = item2.pre_uid;
                context.form_question.Update(item3Entity);
            }
        }

        context.form_card.Remove(card);
    }

    public async Task DeleteQuestionAsync(CubeContext context, form_question question)
    {
        using var transaction = await context.Database.BeginTransactionAsync(System.Data.IsolationLevel.RepeatableRead);
        try
        {
            _logger.LogWarning($"DeleteQuestionAsync: {question.display_name} - {question.id} in {question.card.form.name} - {question.card.name} - {question.card.id}");
            // 获取要移动的卡片在容器中的位置
            var itemList = await GetItemInContainerAsync(context, question.card.form, question.card);
            var item2 = itemList.FirstOrDefault(item => item.id == question.id);
            var item1 = itemList.FirstOrDefault(item => item.id == item2.pre_uid);
            var item3 = itemList.FirstOrDefault(item => item.id == item2.next_uid);

            // 更新前一个项的nextuid
            if (item1 != null)
            {
                if (item1.item_type == "Card")
                {
                    var item1Entity = await context.form_card.FindAsync(item1.id);
                    if (item1Entity != null)
                    {
                        item1Entity.next_uid = item2.next_uid;
                        context.form_card.Update(item1Entity);
                    }
                }
                else if (item1.item_type == "Question")
                {
                    var item1Entity = await context.form_question.FindAsync(item1.id);
                    if (item1Entity != null)
                    {
                        item1Entity.next_uid = item2.next_uid;
                        context.form_question.Update(item1Entity);
                    }
                }
            }

            // 更新后一个项的preuid
            if (item3 != null)
            {
                if (item3.item_type == "Card")
                {
                    var item3Entity = await context.form_card.FindAsync(item3.id);
                    if (item3Entity != null)
                    {
                        item3Entity.pre_uid = item2.pre_uid;
                        context.form_card.Update(item3Entity);
                    }
                }
                else if (item3.item_type == "Question")
                {
                    var item3Entity = await context.form_question.FindAsync(item3.id);
                    if (item3Entity != null)
                    {
                        item3Entity.pre_uid = item2.pre_uid;
                        context.form_question.Update(item3Entity);
                    }
                }
            }

            context.form_question.Remove(question);
            await context.SaveChangesAsync();
            await transaction.CommitAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"删除问题 {question.id} 失败。");
            await transaction.RollbackAsync();
            throw;
        }
    }

    /// <summary>
    /// 获取指定容器中的（卡片或问题）的排序后List
    /// </summary>
    /// <param unique_name="context">当前的数据库上下文。</param>
    /// <param unique_name="container">指定的容器卡片。如为 null，则表示根目录。</param>
    /// <returns>最后一个项（Form_Card 或 Form_Question）或 null。</returns>
    private async Task<List<ContainerItemViewModel>> GetItemInContainerAsync(CubeContext context, form_form thisForm, form_card container)
    {
        // 创建新的数据库上下文实例，而不使用传入的context
        var options = new DbContextOptionsBuilder<CubeContext>()
                         .UseNpgsql(_configuration.GetConnectionString("DefaultConnection"))
                         .Options;
        using var newContext = new CubeContext(options);
        
        var items = new List<ContainerItemViewModel>();
        _logger.LogWarning($"GetItemInContainerAsync - Start: Form:{thisForm.name}({thisForm.id}) Container:{container?.name}({container?.id})");

        var cards = await newContext.form_card.AsNoTracking()
            .Where(c => c.form_id == thisForm.id && (container == null ? c.parent_id == null : c.parent_id == container.id))
            .ToListAsync();

        if (cards != null)
        {
            foreach (var card in cards)
            {
                items.Add(new ContainerItemViewModel
                {
                    id = card.id,
                    name = card.name,
                    item_type = "Card",
                    pre_uid = card.pre_uid,
                    next_uid = card.next_uid
                });
            }
        }

        if (container != null)
        {
            var questions = await newContext.form_question.AsNoTracking()
                .Where(q => q.card_id == container.id)
                .ToListAsync();
            if (questions != null)
            {
                foreach (var question in questions)
                {
                    items.Add(new ContainerItemViewModel
                    {
                        id = question.id,
                        name = question.display_name,
                        item_type = "Question",
                        pre_uid = question.pre_uid,
                        next_uid = question.next_uid
                    });
                }
            }
        }

        // 复制items
        var itemsCopy = items.ToList();

        var logItem = itemsCopy.Where(item => item.pre_uid == null).FirstOrDefault();
        if (logItem != null)
        {
            _logger.LogWarning($"GetItemInContainerAsync: {Guid.Empty} - {logItem.id} - {logItem.next_uid} - {logItem.item_type} - {logItem.name}");
            if(logItem.next_uid != null)
            {
                while(true)
                {
                    var nextItem = items.Where(item => item.id == logItem.next_uid).FirstOrDefault();
                    if (nextItem != null)
                    {
                        itemsCopy.Remove(logItem);
                        logItem = nextItem;
                        _logger.LogWarning($"GetItemInContainerAsync: {logItem.pre_uid} - {logItem.id} - {(logItem.next_uid==null?Guid.Empty:logItem.next_uid)} - {logItem.item_type} - {logItem.name}");
                        if(logItem.next_uid == null)
                        {
                            itemsCopy.Remove(logItem);
                            break;
                        }
                    }
                    else
                    {
                        break;
                    }
                }
            }
            if(itemsCopy.Count > 0)
            {
                foreach(var tempItem in itemsCopy)
                {
                    _logger.LogWarning($"GetItemInContainerAsync: {tempItem.pre_uid} - {tempItem.id} - {(tempItem.next_uid==null?Guid.Empty:tempItem.next_uid)} - {tempItem.item_type} - {tempItem.name}");
                }
            }
        }

        if (!items.Any())
            return null;

        return items;
    }

    private form_card CreateNewCard(form_form form)
    {
        return new form_card
        {
            id = SequentialGuidGenerator.NewGuid(),
            name = $"{form.form_card.Count + 1}",
            parent_id = null,
            form_id = form.id,
            type = "default",
            form_name = form.name,
            form_set_id = form.form_set_id,
            form_set_name = form.form_set_name,
            project_id = form.project_id,
            project_name = form.project_name,
            ward_id = form.ward_id,
            ward_name = form.ward_name,
            department_id = form.department_id,
            department_name = form.department_name,
            hospital_id = form.hospital_id,
            hospital_name = form.hospital_name,
            created_at = DateTime.Now,
            updated_at = DateTime.Now,
            pre_uid = null, // 上一个卡片的Preuid
            next_uid = null // 目前是最后一个
        };
    }

    /// <summary>
    /// 检查表单中所有卡片链表的完整性
    /// </summary>
    /// <param unique_name="context">当前的数据库上下文</param>
    /// <param unique_name="form">要检查的表单</param>
    /// <returns>发现的链表问题列表</returns>
    public async Task<List<CardListIntegrityIssue>> CheckCardListIntegrityAsync(CubeContext context, form_form form)
    {
        var issues = new List<CardListIntegrityIssue>();
        
        // 检查根目录
        _logger.LogInformation($"开始检查表单 {form.name}({form.id}) 根目录的链表完整性");
        var rootIssues = await CheckContainerIntegrityAsync(context, form, null);
        issues.AddRange(rootIssues);
        
        // 检查所有卡片容器
            var allCards = await context.form_card
            .Where(c => c.form_id == form.id)
            .ToListAsync();
            
        foreach (var card in allCards)
        {
            _logger.LogInformation($"检查卡片 {card.name}({card.id}) 容器的链表完整性");
            var cardIssues = await CheckContainerIntegrityAsync(context, form, card);
            issues.AddRange(cardIssues);
        }
        
        _logger.LogWarning($"表单 {form.name} 链表完整性检查完成，发现 {issues.Count} 个问题");
        return issues;
    }
    
    /// <summary>
    /// 检查单个容器（根目录或卡片）的链表完整性
    /// </summary>
    private async Task<List<CardListIntegrityIssue>> CheckContainerIntegrityAsync(CubeContext context, form_form form, form_card container)
    {
        var issues = new List<CardListIntegrityIssue>();
        var containerName = container == null ? "根目录" : container.name;
        var containerId = container == null ? Guid.Empty : container.id;
        
        // 获取容器中的所有项目
        var allItems = await GetItemInContainerAsync(context, form, container);
        if (allItems == null || !allItems.Any())
        {
            // 空容器没有完整性问题
            return issues;
        }
        
        // 1. 检查头节点（是否只有一个pre_uid为null的节点）
        var headNodes = allItems.Where(i => i.pre_uid == null).ToList();
        if (headNodes.Count == 0)
        {
            issues.Add(new CardListIntegrityIssue
            {
                IssueType = "NoHeadNode",
                Description = $"容器 {containerName} 中没有头节点（pre_uid为null的节点）",
                ContainerId = containerId,
                ContainerName = containerName,
                FormId = form.id,
                AffectedItems = allItems
            });
        }
        else if (headNodes.Count > 1)
        {
            issues.Add(new CardListIntegrityIssue
            {
                IssueType = "MultipleHeadNodes",
                Description = $"容器 {containerName} 中存在多个头节点（{headNodes.Count}个pre_uid为null的节点）",
                ContainerId = containerId,
                ContainerName = containerName,
                FormId = form.id,
                AffectedItems = headNodes
            });
        }
        
        // 2. 检查尾节点（是否只有一个next_uid为null的节点）
        var tailNodes = allItems.Where(i => i.next_uid == null).ToList();
        if (tailNodes.Count == 0)
        {
            issues.Add(new CardListIntegrityIssue
            {
                IssueType = "NoTailNode",
                Description = $"容器 {containerName} 中没有尾节点（next_uid为null的节点）",
                ContainerId = containerId,
                ContainerName = containerName,
                FormId = form.id,
                AffectedItems = allItems
            });
        }
        else if (tailNodes.Count > 1)
        {
            issues.Add(new CardListIntegrityIssue
            {
                IssueType = "MultipleTailNodes",
                Description = $"容器 {containerName} 中存在多个尾节点（{tailNodes.Count}个next_uid为null的节点）",
                ContainerId = containerId,
                ContainerName = containerName,
                FormId = form.id,
                AffectedItems = tailNodes
            });
        }
        
        // 3. 检查链表连续性
        bool hasValidChain = CheckChainContinuity(allItems, out var unreachedItems);
        if (!hasValidChain && unreachedItems.Any())
        {
            issues.Add(new CardListIntegrityIssue
            {
                IssueType = "BrokenChain",
                Description = $"容器 {containerName} 中链表不连续，有 {unreachedItems.Count} 个项目无法从头节点访问到",
                ContainerId = containerId,
                ContainerName = containerName,
                FormId = form.id,
                AffectedItems = unreachedItems
            });
        }
        
        return issues;
    }
    
    /// <summary>
    /// 检查链表连续性，并返回无法从头节点访问到的项目
    /// </summary>
    private bool CheckChainContinuity(List<ContainerItemViewModel> items, out List<ContainerItemViewModel> unreachedItems)
    {
        unreachedItems = new List<ContainerItemViewModel>();
        
        // 找到头节点
        var headNode = items.FirstOrDefault(i => i.pre_uid == null);
        if (headNode == null)
        {
            // 没有头节点，无法检查连续性
            unreachedItems = items.ToList();
            return false;
        }
        
        // 从头节点开始遍历
        var visitedIds = new HashSet<Guid>();
        var current = headNode;
        visitedIds.Add(current.id);
        
        // 限制遍历次数，防止循环引用导致无限循环
        int maxIterations = items.Count * 2;
        int iteration = 0;
        
        while (current.next_uid != null && iteration < maxIterations)
        {
            var nextNode = items.FirstOrDefault(i => i.id == current.next_uid);
            if (nextNode == null)
            {
                // 链表中断，next_uid指向的节点不存在
                break;
            }
            
            if (visitedIds.Contains(nextNode.id))
            {
                // 发现循环引用
                break;
            }
            
            visitedIds.Add(nextNode.id);
            current = nextNode;
            iteration++;
        }
        
        // 收集未访问到的节点
        unreachedItems = items.Where(i => !visitedIds.Contains(i.id)).ToList();
        
        return unreachedItems.Count == 0 && iteration < maxIterations;
    }
    
    /// <summary>
    /// 修复容器中的链表完整性问题
    /// </summary>
    public async Task<bool> FixCardListIntegrityAsync(CubeContext context, CardListIntegrityIssue issue)
    {
        _logger.LogWarning($"开始修复链表完整性问题: {issue.Description}");
        
        using var transaction = await context.Database.BeginTransactionAsync(System.Data.IsolationLevel.RepeatableRead);
        try
        {
            form_form form = await context.form_form.FindAsync(issue.FormId);
            form_card container = issue.ContainerId == Guid.Empty ? null : await context.form_card.FindAsync(issue.ContainerId);
            
            // 获取容器中的所有项目
            var allItems = await GetItemInContainerAsync(context, form, container);
            if (allItems == null || !allItems.Any())
            {
                _logger.LogWarning("容器为空，无需修复");
                return true;
            }
            
            // 简单策略：按ID顺序重新构建整个链表
            await RebuildListInContainerAsync(context, form, container, allItems);
            
            await context.SaveChangesAsync();
            await transaction.CommitAsync();
            
            _logger.LogWarning($"链表完整性问题修复成功");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"修复链表完整性问题失败: {issue.Description}");
            await transaction.RollbackAsync();
            throw;
        }
    }
    
    /// <summary>
    /// 按ID顺序重新构建容器中的链表
    /// </summary>
    private async Task RebuildListInContainerAsync(CubeContext context, form_form form, form_card container, List<ContainerItemViewModel> items)
    {
        // 按ID排序
        var sortedItems = items.OrderBy(i => i.id).ToList();
        _logger.LogWarning($"重建链表，项目数量: {sortedItems.Count}");
        
        if (sortedItems.Count == 0)
            return;
            
        // 第一个项目设为头节点
        var firstItem = sortedItems.First();
        if (firstItem.item_type == "Card")
        {
            var cardEntity = await context.form_card.FindAsync(firstItem.id);
            cardEntity.pre_uid = null;
            context.form_card.Update(cardEntity);
            _logger.LogWarning($"设置头节点: {cardEntity.name}({cardEntity.id})");
        }
        else if (firstItem.item_type == "Question")
        {
            var questionEntity = await context.form_question.FindAsync(firstItem.id);
            questionEntity.pre_uid = null;
            context.form_question.Update(questionEntity);
            _logger.LogWarning($"设置头节点: {questionEntity.display_name}({questionEntity.id})");
        }
        
        // 依次连接所有节点
        for (int i = 0; i < sortedItems.Count - 1; i++)
        {
            var currentItem = sortedItems[i];
            var nextItem = sortedItems[i + 1];
            
            // 更新当前节点的next_uid
            if (currentItem.item_type == "Card")
            {
                var cardEntity = await context.form_card.FindAsync(currentItem.id);
                cardEntity.next_uid = nextItem.id;
                context.form_card.Update(cardEntity);
            }
            else if (currentItem.item_type == "Question")
            {
                var questionEntity = await context.form_question.FindAsync(currentItem.id);
                questionEntity.next_uid = nextItem.id;
                context.form_question.Update(questionEntity);
            }
            
            // 更新下一个节点的pre_uid
            if (nextItem.item_type == "Card")
            {
                var cardEntity = await context.form_card.FindAsync(nextItem.id);
                cardEntity.pre_uid = currentItem.id;
                context.form_card.Update(cardEntity);
            }
            else if (nextItem.item_type == "Question")
            {
                var questionEntity = await context.form_question.FindAsync(nextItem.id);
                questionEntity.pre_uid = currentItem.id;
                context.form_question.Update(questionEntity);
            }
            
            _logger.LogWarning($"连接节点: {currentItem.name}({currentItem.id}) -> {nextItem.name}({nextItem.id})");
        }
        
        // 最后一个项目设为尾节点
        var lastItem = sortedItems.Last();
        if (lastItem.item_type == "Card")
        {
            var cardEntity = await context.form_card.FindAsync(lastItem.id);
            cardEntity.next_uid = null;
            context.form_card.Update(cardEntity);
            _logger.LogWarning($"设置尾节点: {cardEntity.name}({cardEntity.id})");
        }
        else if (lastItem.item_type == "Question")
        {
            var questionEntity = await context.form_question.FindAsync(lastItem.id);
            questionEntity.next_uid = null;
            context.form_question.Update(questionEntity);
            _logger.LogWarning($"设置尾节点: {questionEntity.display_name}({questionEntity.id})");
        }
    }

    /// <summary>
    /// 递归收集卡片的所有子卡片和问题
    /// </summary>
    /// <param unique_name="context">数据库上下文</param>
    /// <param unique_name="parentCard">父卡片</param>
    /// <param unique_name="cardsCollector">卡片收集器</param>
    /// <param unique_name="questionsCollector">问题收集器</param>
    /// <returns>异步任务</returns>
    private async Task CollectChildItemsRecursivelyAsync(CubeContext context, form_card parentCard, 
        List<form_card> cardsCollector, List<form_question> questionsCollector)
    {
        _logger.LogWarning($"收集卡片 {parentCard.name}({parentCard.id}) 的子项");
        
        // 获取该卡片的所有直接子卡片
        var childCards = await context.form_card
            .Where(c => c.parent_id == parentCard.id)
            .ToListAsync();
        
        foreach (var childCard in childCards)
        {
            // 防止重复添加
            if (!cardsCollector.Any(c => c.id == childCard.id))
            {
                _logger.LogWarning($"添加子卡片: {childCard.name}({childCard.id})");
                cardsCollector.Add(childCard);
                
                // 递归收集子卡片的子卡片
                await CollectChildItemsRecursivelyAsync(context, childCard, cardsCollector, questionsCollector);
            }
        }
        
        // 获取该卡片的所有问题
        var questions = await context.form_question
            .Where(q => q.card_id == parentCard.id)
            .ToListAsync();
        
        foreach (var question in questions)
        {
            // 防止重复添加
            if (!questionsCollector.Any(q => q.id == question.id))
            {
                _logger.LogWarning($"添加问题: {question.display_name}({question.id})");
                questionsCollector.Add(question);
            }
        }
    }

    /// <summary>
    /// 更新链表节点的指针
    /// </summary>
    /// <param unique_name="context">数据库上下文</param>
    /// <param unique_name="item">要更新的节点</param>
    /// <param unique_name="prevPointer">新的前指针值（为null时不更新）</param>
    /// <param unique_name="nextPointer">新的后指针值（为null时不更新）</param>
    private async Task UpdateItemPointerAsync(CubeContext context, ContainerItemViewModel item, 
        Guid? prevPointer = null, Guid? nextPointer = null)
    {
        if (item.item_type == "Card")
        {
            var entity = await context.form_card.FindAsync(item.id);
            if (entity != null)
            {
                if (prevPointer.HasValue || prevPointer == null && prevPointer != item.pre_uid)
                {
                    entity.pre_uid = prevPointer;
                }
                    
                if (nextPointer.HasValue || nextPointer == null && nextPointer != item.next_uid)
                {
                    entity.next_uid = nextPointer;
                }
                    
                context.form_card.Update(entity);
                _logger.LogWarning($"更新卡片指针: {entity.name}({entity.id}) - pre:{entity.pre_uid} next:{entity.next_uid}");
            }
        }
        else if (item.item_type == "Question")
        {
            var entity = await context.form_question.FindAsync(item.id);
            if (entity != null)
            {
                if (prevPointer.HasValue || prevPointer == null && prevPointer != item.pre_uid)
                {
                    entity.pre_uid = prevPointer;
                }
                    
                if (nextPointer.HasValue || nextPointer == null && nextPointer != item.next_uid)
                {
                    entity.next_uid = nextPointer;
                }
                    
                context.form_question.Update(entity);
                _logger.LogWarning($"更新问题指针: {entity.display_name}({entity.id}) - pre:{entity.pre_uid} next:{entity.next_uid}");
            }
        }
    }
}

/// <summary>
/// 表示链表完整性问题的类
/// </summary>
public class CardListIntegrityIssue
{
    public string IssueType { get; set; }
    public string Description { get; set; }
    public Guid ContainerId { get; set; }
    public string ContainerName { get; set; }
    public Guid FormId { get; set; }
    public List<ContainerItemViewModel> AffectedItems { get; set; }
    public bool IsFixed { get; set; }
}

public class ContainerItemViewModel
{
    public Guid id { get; set; }
    public string name { get; set; }
    public string item_type { get; set; } // "Card" 或 "Question"
    public Guid? pre_uid { get; set; }
    public Guid? next_uid { get; set; }
}

/// <summary>
/// 卡片移动操作的结果信息
/// </summary>
public class CardMoveResult
{
    /// <summary>
    /// 操作是否成功
    /// </summary>
    public bool IsSuccess { get; set; }
    
    /// <summary>
    /// 结果消息
    /// </summary>
    public string Message { get; set; }
    
    /// <summary>
    /// 移动的卡片数量（包括子卡片）
    /// </summary>
    public int MovedCardCount { get; set; }
    
    /// <summary>
    /// 移动的问题数量
    /// </summary>
    public int MovedQuestionCount { get; set; }
}
