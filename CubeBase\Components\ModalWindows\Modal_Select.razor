﻿@inherits Modal_Base


<MudDialog Style="width:100%;">
    <TitleContent>
        <MudText Typo="Typo.h6">@MudDialog.Title</MudText>
    </TitleContent>
    <DialogContent>
        <label class="form-label mb-2">输入所选选项文字</label>
        <Textarea @bind-Value="@_sortedOptionSubsetValue" rows="8" IsAutoScroll></Textarea>
    </DialogContent>
    <DialogActions>
        <MudButton Variant="Variant.Filled" OnClick="Cancel">取消</MudButton>
        <MudButton Variant="Variant.Filled" Color="Color.Primary" OnClick="@(async () => await Submit())">导入</MudButton>
    </DialogActions>
</MudDialog>


@code {
    [CascadingParameter]
    private IMudDialogInstance MudDialog { get; set; }
    private List<string> tempSelectedList = new List<string>();

    private string _sortedOptionSubsetValue
    {
        get
        {
            if (tempSelectedList.Count > 0)
            {
                return string.Join("\n", tempSelectedList);
            }
            return string.Empty;
        }
        set
        {
            // 将输入字符串拆分为列表，并移除空行
            value = value.Replace("###", "\n");
            tempSelectedList = value.Split(new string[] { "\n" }, StringSplitOptions.RemoveEmptyEntries)
                                            .Select(option => option.Trim())  // 去除每行的首尾空格
                                            .Where(option => !string.IsNullOrWhiteSpace(option))  // 移除空行
                                            .ToList();

            // 去除重复项
            tempSelectedList = tempSelectedList.Distinct().ToList();

        }
    }


    private void Cancel() => MudDialog.Cancel();

    private async Task Submit()
    {
        if(string.IsNullOrWhiteSpace(_sortedOptionSubsetValue)){
            return;
        }
        MudDialog.Close(MudBlazor.DialogResult.Ok(_sortedOptionSubsetValue.Replace("\n",",")));
    }
}
