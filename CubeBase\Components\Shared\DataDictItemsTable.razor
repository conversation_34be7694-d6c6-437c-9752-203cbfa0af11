@inherits Cmp_Base
@inject ILogger<DataDictItemsTable> Logger

@if (Items.Any())
{
    <MudExpansionPanels Elevation="2" Outlined="true" Class="mt-0 pa-0" Style="background-color: #d6ffd5;">
        <MudExpansionPanel Text="表单匹配" Expanded="true" Dense="true" Class="ma-0 pa-0">
            <TitleContent>
                <div style="display: flex; align-items: center; padding: 8px 16px; background: #f5f5f5; border-radius: 4px;">
                    <span style="margin-right: 16px; font-size: 16px; font-weight: 500;">表单匹配列表</span>
                    <div style="width: 1px; height: 20px; background: #ddd; margin: 0 16px;"></div>
                    <span style="margin-right: 16px; color: #1976d2;">
                        待分析：<span style="font-weight: bold;">@PendingAnalysisCount</span>
                    </span>
                    <span style="margin-right: 16px; color: #2e7d32;">
                        已匹配：<span style="font-weight: bold;">@MatchedCount</span>
                    </span>
                    <span style="margin-right: 16px; color: #d32f2f;">
                        未匹配：<span style="font-weight: bold;">@UnmatchedCount</span>
                    </span>
                </div>
            </TitleContent>
            <ChildContent>
                <table class="data-dict-table">
                    <thead>
                        <tr>
                            <th class="id-column">编号</th>
                            <th>名称</th>
                            <th class="type-column">类型</th>
                            <th class="attr-column">其他属性</th>
                            <th class="match-result-column">分类匹配结果</th>
                            <th class="match-result-column">
                                <div class="d-flex justify-content-between align-items-center">
                                    <span>字段匹配结果</span>
                                    <input type="checkbox"
                                           checked="@_allSelected"
                                           @onchange="HandleAllSelectedChanged"
                                           class="native-checkbox" />
                                </div>
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var item in Items)
                        {
                            <tr>
                                <td class="id-column">@item.Id</td>
                                <td class="name-column">@item.DisplayName</td>
                                <td class="type-column">@item.ItemType</td>
                                <td class="attr-column">@((MarkupString)FormatAttributes(item.Attributes))</td>
                                <td class="match-result-column">
                                    @if (!string.IsNullOrEmpty(item.MatchType))
                                    {
                                        @if (item.MatchType == "MATCH_T")
                                        {
                                            <div class="llm-match">
                                                @((MarkupString)item.MatchResult)
                                            </div>
                                        }
                                        else if (item.MatchType == "SUGGEST_NEW_T")
                                        {
                                            <div class="llm-potential-match">
                                                @((MarkupString)item.MatchResult)
                                            </div>
                                        }
                                        else if (item.MatchType == "REQUIRE_T")
                                        {
                                            <div class="llm-no-match">
                                                @((MarkupString)item.MatchResult)
                                            </div>
                                        }
                                        else
                                        {
                                            @((MarkupString)item.MatchResult)
                                        }
                                    }
                                    else if (!string.IsNullOrEmpty(item.MatchResult))
                                    {
                                        @((MarkupString)item.MatchResult)
                                    }
                                </td>
                                <td class="match-result-column">
                                    @if (item.LlmAnalysis != null)
                                    {
                                        if (!string.IsNullOrEmpty(item.LlmAnalysis.ErrorMessage))
                                        {
                                            <div class="llm-error" title="@item.LlmAnalysis.ErrorMessage">
                                                <span class="error-text">分析错误</span>
                                            </div>
                                        }
                                        else if (item.LlmAnalysis.IsProcessed)
                                        {
                                            if (item.LlmAnalysis.MatchFound)
                                            {
                                                <div class="llm-match" title="@item.LlmAnalysis.Reasoning">
                                                    <span class="match-text">匹配: @item.LlmAnalysis.MatchedExistingItemDisplayName</span>
                                                    <span class="match-type">(@item.LlmAnalysis.MatchedExistingItemType) - [@item.LlmAnalysis.MatchedExistingItemId]</span>
                                                    @if (!string.IsNullOrEmpty(item.LlmAnalysis.ExistingCategoryDataAsText))
                                                    {
                                                        <div class="match-path">
                                                            <small>路径: @ExtractPathFromCategoryData(item.LlmAnalysis.ExistingCategoryDataAsText)</small>
                                                        </div>
                                                    }
                                                </div>
                                            }
                                            else
                                            {
                                                <div class="llm-no-match" title="@item.LlmAnalysis.Reasoning" @onclick="() => ToggleSelection(item)">
                                                    <div class="d-flex justify-content-between align-items-center">
                                                        <div>
                                                            <span class="no-match-text">未找到匹配</span>
                                                            <!-- 显示中文名称和llm返回的建议英文名称及建议中文名称 -->
                                                            <div class="suggestion-info">
                                                                @if (!string.IsNullOrEmpty(item.LlmAnalysis.SuggestedChineseName))
                                                                {
                                                                    <small>建议中文名: @item.LlmAnalysis.SuggestedChineseName</small>
                                                                }
                                                                @if (!string.IsNullOrEmpty(item.LlmAnalysis.SuggestedEnglishName))
                                                                {
                                                                    <small>建议英文名: @item.LlmAnalysis.SuggestedEnglishName</small>
                                                                }
                                                            </div>
                                                            @if (!string.IsNullOrEmpty(item.LlmAnalysis.ExistingCategoryDataAsText))
                                                            {
                                                                <div class="match-path">
                                                                    <small>路径: @ExtractPathFromCategoryData(item.LlmAnalysis.ExistingCategoryDataAsText)</small>
                                                                </div>
                                                            }
                                                        </div>
                                                        @if (IsItemSelectable(item))
                                                        {
                                                            <div class="form-check">
                                                                <input type="checkbox" class="form-check-input"
                                                                       checked="@item.IsSelected"
                                                                       @onchange="(e) => HandleNativeCheckboxChanged(item, e)" />
                                                            </div>
                                                        }
                                                    </div>
                                                </div>
                                            }
                                        }
                                        else
                                        {
                                            <div class="llm-pending">等待分析...</div>
                                        }
                                    }
                                    else if (!string.IsNullOrEmpty(item.MatchResult) && !string.IsNullOrEmpty(item.CategoryId))
                                    {
                                        <div class="llm-pending">待分析</div>
                                    }
                                    else if (IsParentNode(item))
                                    {
                                        <div class="d-flex justify-content-between align-items-center">
                                            <span>父节点</span>
                                            <input type="checkbox"
                                                   checked="@item.IsSelected"
                                                   @onchange="(e) => HandleNativeCheckboxChanged(item, e)"
                                                   class="native-checkbox"
                                                   onclick="event.stopPropagation();" />
                                        </div>
                                    }
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </ChildContent>
        </MudExpansionPanel>
    </MudExpansionPanels>

    <MudExpansionPanels Elevation="2" Outlined="true" Class="mt-1 pa-0" Style="background-color: transparent;">
        <MudExpansionPanel Text="数据字典SQL生成" Expanded="false">
            <TitleContent>
                <div class="d-flex align-items-center">
                    <span class="mr-2">仅显示SQL语句</span>
                    <MudSwitch T="bool" Color="Color.Primary" ValueChanged="OnSqlModeChanged" Value="@_onlySqlMode" Class="mt-1" />
                </div>
            </TitleContent>
            <ChildContent>
                <MudTextField T="string" Label="选中项目属性" Value="@_selectedItemsText" Variant="Variant.Outlined" Lines="5" AutoGrow MaxLines="120" FullWidth Margin="Margin.Dense" />
            </ChildContent>
        </MudExpansionPanel>
    </MudExpansionPanels>

    <FormSqlGenerator Items="@Items" />
}
else
{
    <MudAlert Severity="Severity.Info" Class="my-2">没有可显示的数据或解析失败。</MudAlert>
}

@code {
    [Parameter]
    public List<IDataDictLLMService.ParsedDataItem> Items { get; set; } = new();
    [Parameter]
    public EventCallback<string> OnShowCategoryData { get; set; }
    [Parameter]
    public bool IsTextFieldEditable { get; set; } = false;
    // 存储选中项目的文本表示
    private string _selectedItemsText = string.Empty;
    // 全选状态
    private bool _allSelected = false;
    // 仅显示SQL语句模式
    private bool _onlySqlMode = false;
    // 存储项目层级关系的字典
    private Dictionary<string, List<IDataDictLLMService.ParsedDataItem>> _parentChildrenMap = new();
    // 存储项目ID到项目的映射
    private Dictionary<string, IDataDictLLMService.ParsedDataItem> _idToItemMap = new();

    protected override void OnParametersSet()
    {
        base.OnParametersSet();
        // 初始化层级关系
        BuildHierarchy();
        // 更新全选状态
        UpdateAllSelectedState();
    }

    /// <summary>构建项目层级关系</summary>
    private void BuildHierarchy()
    {
        _parentChildrenMap.Clear();
        _idToItemMap.Clear();

        // 构建ID到项目的映射
        foreach (var item in Items)
        {
            string normalizedId = NormalizeId(item.Id);
            _idToItemMap[normalizedId] = item;
        }

        // 构建父子关系
        foreach (var item in Items)
        {
            string normalizedId = NormalizeId(item.Id);
            string parentId = GetParentId(normalizedId);

            if (!string.IsNullOrEmpty(parentId))
            {
                if (!_parentChildrenMap.ContainsKey(parentId))
                {
                    _parentChildrenMap[parentId] = new List<IDataDictLLMService.ParsedDataItem>();
                }

                _parentChildrenMap[parentId].Add(item);
            }
        }
    }

    /// <summary>标准化ID格式，移除空格和括号</summary>
    private string NormalizeId(string id) => id.Replace(" ", "").Trim('(', ')');

    /// <summary>获取父节点ID</summary>
    private string GetParentId(string normalizedId)
    {
        int lastDashIndex = normalizedId.LastIndexOf('-');
        if (lastDashIndex > 0)
        {
            return normalizedId.Substring(0, lastDashIndex).Trim();
        }

        return string.Empty;
    }

    /// <summary>判断项目是否为父节点</summary>
    private bool IsParentNode(IDataDictLLMService.ParsedDataItem item) =>
        _parentChildrenMap.ContainsKey(NormalizeId(item.Id));

    /// <summary>获取项目的子节点</summary>
    private List<IDataDictLLMService.ParsedDataItem> GetChildItems(IDataDictLLMService.ParsedDataItem item)
    {
        string normalizedId = NormalizeId(item.Id);
        if (_parentChildrenMap.TryGetValue(normalizedId, out var children))
        {
            return children;
        }

        return new List<IDataDictLLMService.ParsedDataItem>();
    }

    /// <summary>获取可选择的子节点（未匹配的叶子节点）</summary>
    private List<IDataDictLLMService.ParsedDataItem> GetSelectableChildItems(IDataDictLLMService.ParsedDataItem item) =>
        GetAllDescendants(item).Where(IsItemSelectable).ToList();

    /// <summary>获取所有后代节点（包括子节点的子节点）</summary>
    private List<IDataDictLLMService.ParsedDataItem> GetAllDescendants(IDataDictLLMService.ParsedDataItem item)
    {
        var result = new List<IDataDictLLMService.ParsedDataItem>();
        var directChildren = GetChildItems(item);

        foreach (var child in directChildren)
        {
            result.Add(child);
            result.AddRange(GetAllDescendants(child));
        }

        return result;
    }

    /// <summary>判断项目是否可选择（未匹配的叶子节点或父节点）</summary>
    private bool IsItemSelectable(IDataDictLLMService.ParsedDataItem item) =>
        // 父节点总是可选择的
        IsParentNode(item) || (item.LlmAnalysis != null && item.LlmAnalysis.IsProcessed && !item.LlmAnalysis.MatchFound);

    /// <summary>获取所有可选择的项目</summary>
    private List<IDataDictLLMService.ParsedDataItem> GetAllSelectableItems() =>
        Items.Where(IsItemSelectable).ToList();

    /// <summary>格式化属性列表，使属性名称加粗，使用全角冒号</summary>
    private string FormatAttributes(List<string> attributes)
    {
        if (attributes == null || !attributes.Any())
            return string.Empty;

        return string.Join("", attributes.Select(attr =>
        {
            int colonIndex = attr.IndexOf(':');
            if (colonIndex > 0)
            {
                string name = attr.Substring(0, colonIndex).Trim();
                string value = attr.Substring(colonIndex + 1).Trim();
                return $"<span class=\"attr-name\">{name}</span>：{value}<br />";
            }
            return $"{attr}<br />";
        }));
    }

    /// <summary>从ExistingCategoryDataAsText中提取第一行作为路径信息</summary>
    private string ExtractPathFromCategoryData(string categoryData) =>
        string.IsNullOrEmpty(categoryData) ? string.Empty :
        categoryData.Split(new[] { "\r\n", "\r", "\n" }, StringSplitOptions.None).FirstOrDefault()?.Trim() ?? string.Empty;

    private void ToggleSelection(IDataDictLLMService.ParsedDataItem item)
    {
        if (IsItemSelectable(item))
        {
            ProcessItemSelectionChange(item, !item.IsSelected);
        }
    }

    // 处理原生checkbox状态变化
    private void HandleNativeCheckboxChanged(IDataDictLLMService.ParsedDataItem item, ChangeEventArgs e)
    {
        if (IsItemSelectable(item))
        {
            ProcessItemSelectionChange(item, (bool)e.Value);
        }
    }

    // 处理项目选择状态变化的通用方法
    private void ProcessItemSelectionChange(IDataDictLLMService.ParsedDataItem item, bool newValue)
    {
        item.IsSelected = newValue;

        // 如果是父节点，级联更新子节点
        if (IsParentNode(item))
        {
            UpdateChildrenSelectionState(item, newValue);
        }

        // 更新父节点状态
        UpdateParentSelectionState(item);

        // 更新全选状态
        UpdateAllSelectedState();

        // 更新选中项目的文本
        UpdateSelectedItemsText();

        StateHasChanged();
    }

    // 处理全选复选框状态变化
    private void HandleAllSelectedChanged(ChangeEventArgs e)
    {
        bool newValue = (bool)e.Value;
        _allSelected = newValue;

        // 更新所有可选择项目的状态
        foreach (var item in GetAllSelectableItems())
        {
            item.IsSelected = newValue;
        }

        // 更新选中项目的文本
        UpdateSelectedItemsText();

        StateHasChanged();
    }

    /// <summary>更新子节点选择状态</summary>
    private void UpdateChildrenSelectionState(IDataDictLLMService.ParsedDataItem parent, bool isSelected)
    {
        var selectableChildren = GetSelectableChildItems(parent);
        foreach (var child in selectableChildren)
        {
            child.IsSelected = isSelected;
        }
    }

    /// <summary>更新父节点选择状态</summary>
    private void UpdateParentSelectionState(IDataDictLLMService.ParsedDataItem item)
    {
        string normalizedId = NormalizeId(item.Id);
        string parentId = GetParentId(normalizedId);

        if (!string.IsNullOrEmpty(parentId) && _idToItemMap.TryGetValue(parentId, out var parentItem))
        {
            var selectableChildren = GetSelectableChildItems(parentItem);
            if (selectableChildren.Any())
            {
                // 如果所有可选择的子节点都被选中，父节点也被选中
                parentItem.IsSelected = selectableChildren.All(c => c.IsSelected);

                // 递归更新更高层级的父节点
                UpdateParentSelectionState(parentItem);
            }
        }
    }

    /// <summary>更新全选状态</summary>
    private void UpdateAllSelectedState()
    {
        var selectableItems = GetAllSelectableItems();
        _allSelected = selectableItems.Any() && selectableItems.All(item => item.IsSelected);
    }

    /// <summary>从ExistingCategoryDataAsText中提取表ID</summary>
    private string ExtractTableId(string categoryData)
    {
        if (string.IsNullOrEmpty(categoryData))
            return string.Empty;

        var lines = categoryData.Split('\n');
        foreach (var line in lines)
        {
            var match = System.Text.RegularExpressions.Regex.Match(line, @"\((.*?)\)");
            if (match.Success)
            {
                return match.Groups[1].Value;
            }
        }
        return string.Empty;
    }

    /// <summary>更新选中项目的文本表示</summary>
    private void UpdateSelectedItemsText()
    {
        var selectedItems = Items.Where(i => i.IsSelected && i.Id.Count(c => c == '-') == 3).ToList();
        if (!selectedItems.Any())
        {
            _selectedItemsText = string.Empty;
            return;
        }

        var sb = new System.Text.StringBuilder();
        foreach (var item in selectedItems)
        {
            string tableId = string.Empty;
            string rootPathForTID = string.Empty;

            // 预先提取表ID和路径（如果有的话）
            if (item.LlmAnalysis != null && !string.IsNullOrEmpty(item.LlmAnalysis.ExistingCategoryDataAsText))
            {
                tableId = ExtractTableId(item.LlmAnalysis.ExistingCategoryDataAsText);
                if (!_onlySqlMode)
                {
                    var lines = item.LlmAnalysis.ExistingCategoryDataAsText.Split('\n');
                    rootPathForTID = lines[0].Trim();
                }
            }

            // 如果不是仅SQL模式，则添加描述性文本
            if (!_onlySqlMode)
            {
                // 添加基本属性
                sb.Append($"Id: {item.Id}, DisplayName: {inj_dataDictLLMService.CleanDisplayName(item.DisplayName)}, ItemType: {item.ItemType}，");

                // 添加其他属性
                if (item.Attributes != null && item.Attributes.Any()) { sb.Append("Attributes: " + string.Join(", ", item.Attributes) + "，"); }

                sb.Append($"MatchType: {item.MatchType}，");

                // 添加LLM分析结果相关属性
                if (item.LlmAnalysis != null)
                {
                    if (!string.IsNullOrEmpty(rootPathForTID)) { sb.Append($"TID: {rootPathForTID} [{tableId}]，"); }
                    if (!string.IsNullOrEmpty(item.LlmAnalysis.SuggestedChineseName)) { sb.Append($"建议中文名: {item.LlmAnalysis.SuggestedChineseName}，"); }
                    if (!string.IsNullOrEmpty(item.LlmAnalysis.SuggestedEnglishName)) { sb.Append($"建议英文名: {item.LlmAnalysis.SuggestedEnglishName}，"); }
                    if (!string.IsNullOrEmpty(item.LlmAnalysis.Reasoning)) { sb.Append($"分析理由: {item.LlmAnalysis.Reasoning}，"); }
                    if (!string.IsNullOrEmpty(item.LlmAnalysis.Reasoning)) { sb.Append($"分析理由: {item.LlmAnalysis.Reasoning}，"); }
                }
                // 添加PostgreSQL INSERT语句
                sb.AppendLine();
            }

            // 提取或生成英文名
            string uniqueName = !string.IsNullOrEmpty(item.LlmAnalysis?.SuggestedEnglishName)
                ? item.LlmAnalysis.SuggestedEnglishName.ToLower().Replace(" ", "_")
                : inj_dataDictLLMService.CleanDisplayName(item.DisplayName).ToLower().Replace(" ", "_");

            // 提取显示名称
            string displayName = !string.IsNullOrEmpty(item.LlmAnalysis?.SuggestedChineseName)
                ? item.LlmAnalysis.SuggestedChineseName
                : inj_dataDictLLMService.CleanDisplayName(item.DisplayName);

            // 转换数据类型
            string dataType = TypeTranslateGenerator.ConvertQuestionDataType(item.ItemType);

            // 提取选项集
            string optionSetSql = "NULL";
            if (dataType == "选择") // 修改这里，使用dataType而不是原始的itemType
            {
                var options = ExtractOptionsFromAttributes(item.Attributes);
                if (options.Any())
                {
                    optionSetSql = "ARRAY[" + string.Join(", ", options.Select(o => $"'{o}'")) + "]";
                }
            }

            // 生成INSERT语句
            sb.AppendLine($"INSERT INTO public.column_definition (id, table_id, unique_name, display_name, data_type, is_valid, is_changed, sorted_index, option_set)");
            sb.AppendLine($"VALUES (uuid_generate_v1(), '{tableId}', '{uniqueName}', '{displayName}', '{dataType}', true, true, 0, {optionSetSql});");

            // 添加分隔线
            sb.AppendLine(_onlySqlMode ? "" : "----------------------------------------");
        }

        _selectedItemsText = sb.ToString().TrimEnd();
    }

    /// <summary>从属性中提取选项</summary>
    private List<string> ExtractOptionsFromAttributes(List<string> attributes)
    {
        if (attributes == null || !attributes.Any())
            return new List<string>();

        foreach (var attr in attributes)
        {
            if (attr.Contains("选项:"))
            {
                var optionsText = attr.Substring(attr.IndexOf("选项:") + "选项:".Length).Trim();
                return optionsText.Split(new[] { "###" }, StringSplitOptions.RemoveEmptyEntries)
                    .Select(o => o.Trim()).Where(o => !string.IsNullOrWhiteSpace(o)).ToList();
            }
        }

        return new List<string>();
    }

    /// <summary>处理SQL模式开关变化</summary>
    private void OnSqlModeChanged()
    {
        _onlySqlMode = !_onlySqlMode;
        UpdateSelectedItemsText();
        StateHasChanged();
    }

    // 统计待分析的项目数量（ID中有3个'-'且LlmAnalysis为null或IsProcessed为false）
    private int PendingAnalysisCount => Items.Count(item => 
        CountDashes(item.Id) == 3 && 
        (item.LlmAnalysis == null || !item.LlmAnalysis.IsProcessed));
   
    // 统计已匹配的项目数量（ID中有3个'-'且LlmAnalysis.IsProcessed为true且MatchFound为true）
    private int MatchedCount => Items.Count(item => CountDashes(item.Id) == 3 && item.LlmAnalysis != null && item.LlmAnalysis.IsProcessed && item.LlmAnalysis.MatchFound);
   
    // 统计未匹配的项目数量（ID中有3个'-'且LlmAnalysis.IsProcessed为true且MatchFound为false）
    private int UnmatchedCount => Items.Count(item => CountDashes(item.Id) == 3 && item.LlmAnalysis != null && item.LlmAnalysis.IsProcessed && !item.LlmAnalysis.MatchFound);
   
    // 辅助方法：计算字符串中'-'的数量
    private int CountDashes(string id) => id?.Count(c => c == '-') ?? 0;
}
