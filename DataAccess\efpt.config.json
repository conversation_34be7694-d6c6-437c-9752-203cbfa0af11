﻿{
   "CodeGenerationMode": 4,
   "ContextClassName": "CubeContext",
   "ContextNamespace": null,
   "FilterSchemas": false,
   "IncludeConnectionString": false,
   "ModelNamespace": null,
   "OutputContextPath": null,
   "OutputPath": "Models",
   "PreserveCasingWithRegex": true,
   "ProjectRootNamespace": "DataAccess",
   "Schemas": null,
   "SelectedHandlebarsLanguage": 2,
   "SelectedToBeGenerated": 0,
   "T4TemplatePath": null,
   "Tables": [
      {
         "Name": "form.form_card",
         "ObjectType": 0
      },
      {
         "Name": "form.form_form",
         "ObjectType": 0
      },
      {
         "Name": "form.form_form_set",
         "ObjectType": 0
      },
      {
         "Name": "form.form_linker_rule",
         "ObjectType": 0
      },
      {
         "Name": "form.form_project",
         "ObjectType": 0
      },
      {
         "Name": "form.form_question",
         "ObjectType": 0
      },
      {
         "Name": "public.column_definition",
         "ObjectType": 0
      },
      {
         "Name": "public.patient",
         "ObjectType": 0
      },
      {
         "Name": "public.table_definition",
         "ObjectType": 0
      },
      {
         "Name": "public.unique_patient",
         "ObjectType": 0
      },
      {
         "Name": "system.sys_department",
         "ObjectType": 0
      },
      {
         "Name": "system.sys_hospital",
         "ObjectType": 0
      },
      {
         "Name": "system.sys_license",
         "ObjectType": 0
      },
      {
         "Name": "system.sys_map_role_license",
         "ObjectType": 0
      },
      {
         "Name": "system.sys_map_role_permission",
         "ObjectType": 0
      },
      {
         "Name": "system.sys_map_user_role",
         "ObjectType": 0
      },
      {
         "Name": "system.sys_permission",
         "ObjectType": 0
      },
      {
         "Name": "system.sys_permission_data",
         "ObjectType": 0
      },
      {
         "Name": "system.sys_region",
         "ObjectType": 0
      },
      {
         "Name": "system.sys_role",
         "ObjectType": 0
      },
      {
         "Name": "system.sys_user",
         "ObjectType": 0
      },
      {
         "Name": "system.sys_ward",
         "ObjectType": 0
      }
   ],
   "UiHint": null,
   "UncountableWords": null,
   "UseAsyncStoredProcedureCalls": true,
   "UseBoolPropertiesWithoutDefaultSql": false,
   "UseDatabaseNames": true,
   "UseDatabaseNamesForRoutines": true,
   "UseDateOnlyTimeOnly": true,
   "UseDbContextSplitting": false,
   "UseDecimalDataAnnotationForSprocResult": true,
   "UseFluentApiOnly": true,
   "UseHandleBars": false,
   "UseHierarchyId": false,
   "UseInflector": false,
   "UseInternalAccessModifiersForSprocsAndFunctions": false,
   "UseLegacyPluralizer": false,
   "UseManyToManyEntity": false,
   "UseNoDefaultConstructor": false,
   "UseNoNavigations": false,
   "UseNoObjectFilter": false,
   "UseNodaTime": false,
   "UseNullableReferences": false,
   "UsePrefixNavigationNaming": false,
   "UseSchemaFolders": false,
   "UseSchemaNamespaces": false,
   "UseSpatial": false,
   "UseT4": false,
   "UseT4Split": false
}