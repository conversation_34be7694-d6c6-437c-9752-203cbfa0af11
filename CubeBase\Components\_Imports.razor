﻿@using System.Net.Http
@using System.Net.Http.Json
@using System.Text
@using System.Collections.ObjectModel
@using System.IO
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Authorization
@using Microsoft.AspNetCore.Components.Forms
@using Microsoft.AspNetCore.Components.Routing
@using Microsoft.AspNetCore.Components.Web
@using Microsoft.AspNetCore.Components.Web.Virtualization
@using Microsoft.AspNetCore.Components.Rendering
@using System.Text.RegularExpressions;

@using static Microsoft.AspNetCore.Components.Web.RenderMode
@using Microsoft.JSInterop
@using Microsoft.EntityFrameworkCore

@*本地命名空间*@
@using CubeBase
@using CubeBase.Components
@using CubeBase.Components.Layout
@using CubeBase.Components.ModalWindows
@using CubeBase.Components.Pages
@using CubeBase.Components.Shared

@using BusinessLogic.Auth
@using BusinessLogic.Cube

@*Bio.AI*@
@using Bio.AI
@using Bio.AI.Models
@using BusinessLogic.Messages
@using Infrastructure
@using DataAccess.Models

@*第三方组件库*@
@using AutoMapper
@using BitzArt.Blazor.Auth
@using BootstrapBlazor.Components
@using MudBlazor
@using MudBlazor.Services
@using Easy.MessageHub
@using Tizzani.MudBlazor.HtmlEditor
@attribute [Authorize]

@*获取user鉴权信息*@
@inject IUserService inj_userService
@inject NavigationManager inj_navigationManager
@inject CubeContext inj_cubeContext
@inject ISnackbar inj_snackbar
@inject IMapper inj_mapper
@inject IConfiguration inj_configuration
@inject IMessageHub inj_messageHub
@inject IJSRuntime inj_jsRuntime
@inject CardService inj_cardService
@inject CardExportService inj_cardExportService
@inject CardImportService inj_cardImportService
@inject IDialogService inj_dialogService
@inject IMedDictService inj_medDictService
@inject IDataDictLLMService inj_dataDictLLMService
@inject IWebHostEnvironment inj_Env

@using BColor = BootstrapBlazor.Components.Color
@using BSize = BootstrapBlazor.Components.Size
@using BPlacement = BootstrapBlazor.Components.Placement
@using Color = MudBlazor.Color
@using Size = MudBlazor.Size
@using Placement = MudBlazor.Placement