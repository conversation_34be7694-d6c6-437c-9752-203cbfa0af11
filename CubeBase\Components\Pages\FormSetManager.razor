﻿@page "/FormsetManager"
@inherits BasePage
@inject ILogger<FormSetManager> inj_logger
@implements IAsyncDisposable
@inject IMedDictService inj_medDictService
@using System.Diagnostics

<MudPaper Class="h-100 pa-1" Elevation="1">
    <!-- 使用4个横向排列的级联下拉菜单依次选择Hospital、Department、Ward、Project -->
    <div style="display: flex; align-items: center; position: relative;">
        <MudText Class="ml-4" Typo="Typo.body1">选择项目：</MudText>
        <span><Cascader TValue="string" OnSelectedItemChanged="@OnProjectChanged" Items="@_projectItems" ParentSelectable="false" style="width: 300px;padding: 2px; z-index: 1200;" /></span>
        <MudText Class="ml-6" Typo="Typo.h6">
            @if (_selectedProject != null)
            {
                @($"{_selectedProject.hospital.name} - {_selectedProject.department.name} - {_selectedProject.ward.name} - {_selectedProject.name}")
            }
            @if (_selectedFormSet != null)
            {
                <span style="color: #007bff;">@($" - {_selectedFormSet.name} - {_selectedFormSet.id}")</span>
            }
        </MudText>
    </div>
</MudPaper>

@if (_selectedProject != null)
{
    <MudPaper Height="calc(100vh - 115px)" Class="d-flex" Width="100%">

        <div style="height:100%; width:10%">

            <MudText Typo="Typo.h6" Class="mt-2" Style="text-align:center">表单集</MudText>

            <MudNavMenu Color="Color.Success" Bordered="true">
                @foreach (var formSet in _selectedProject.form_form_set)
                {
                    <MudNavLink Icon="@Icons.Material.Filled.Store" Match="NavLinkMatch.All"
                                IconColor="@(((_selectedFormSet?.id as Guid?)==formSet.id) ? Color.Success : Color.Dark)"
                                OnClick="@(()=>_selectedFormSet=formSet)">
                        @formSet.name
                        &nbsp;
                    </MudNavLink>
                }
                <MudNavLink Icon="@Icons.Material.Filled.Add" OnClick="@OpenFormsetDialog">添加表单集</MudNavLink>
            </MudNavMenu>
        </div>
        <div class="d-flex" style="width:90%;position:relative;">
            <div style="width:30%; display: @(_isCollapsed ? "none" : "block")" class="FormsetManagerLeft">
                <Cmp_MedDictTable />
            </div>
            <div class="FormsetManagerRight" style="border-left: @(_isCollapsed ? "1px solid #dee2e6" : "none"); width: @(_isCollapsed ? "100%" : "70%")">
                @if (_selectedFormSet != null)
                {
                    <CascadingValue Value="@_medDictItems" Name="MedDictItems">
                        <CascadingValue Value="true" Name="admin">
                            <CascadingValue Value="@_tablePathCache" Name="TablePathCache">
                                <Cmp_FormTab FormSetID="@_selectedFormSet.id" _parentContext="@context" />
                            </CascadingValue>
                        </CascadingValue>
                    </CascadingValue>
                }
            </div>
            <div class="CollapseBtnBox">
                <div class="CollapseBtn" @onclick="ToggleCollapse">
                    <MudTooltip Text="@(_isCollapsed ? "展开" : "收起")">
                        <MudIcon Icon="@(_isCollapsed ? Icons.Material.Filled.ChevronRight : Icons.Material.Filled.ChevronLeft)" Size="Size.Small" />
                    </MudTooltip>
                </div>
            </div>
        </div>
    </MudPaper>
}
<style>
    .CollapseBtnBox {
        position: absolute;
        margin-left: -4px;
        left: 0%;
        top: 50%;
        cursor: pointer;
        width: 16px;
        height: 62px;
        border-radius: 8px;
    }

    .CollapseBtn {
        width: 16px;
        text-align: center;
        height: 62px;
        line-height: 62px;
        background-color: #dddddd;
        border-radius: 4px;
    }
</style>
@code {
    private bool _isCollapsed = false;
    private List<sys_hospital> _contextHospitalList = new();
    private List<CascaderItem> _projectItems = [];
    private List<CascaderItem> _medDictItems = [];
    private form_project? _selectedProject;
    private form_form_set? _selectedFormSet;
    private Dictionary<Guid, List<column_definition>> columnsByTableId = new();
    
    // 新增：路径缓存，key为table_definition_id，value为完整路径
    private Dictionary<Guid, string> _tablePathCache = new();

    // 重写基类的初始化方法
    protected override Task OnPageInitializedAsync() => LoadProjectData();

    private async Task LoadProjectData()
    {
        var stopwatch = Stopwatch.StartNew();
        inj_logger.LogWarning("[性能调试] FormSetManager.LoadProjectData 开始");

        if (context == null)
        {
            inj_logger.LogWarning("[性能调试] LoadProjectData context为空，跳过加载");
            return;
        }

        try
        {
            // 串行执行数据库操作，避免并发使用同一个 DbContext
            var sw1 = Stopwatch.StartNew();
            _contextHospitalList = await context.sys_hospital
                .Include(td => td.sys_department)
                .Include(td => td.sys_ward)
                .Include(td => td.form_project)
                .Include(td => td.form_form_set)
                .ToListAsync();
            sw1.Stop();
            inj_logger.LogWarning("[性能调试] 加载医院数据 耗时: {ElapsedMs}ms, 医院数: {HospitalCount}",
                sw1.ElapsedMilliseconds, _contextHospitalList.Count);

            var sw2 = Stopwatch.StartNew();
            _projectItems.Clear();
            // 构建级联下拉菜单
            foreach (var hospital in _contextHospitalList.Where(h => h.form_project.Any()).OrderBy(h => h.name))
            {
                var hospitalItem = new CascaderItem(hospital.id.ToString(), hospital.name);
                foreach (var department in hospital.sys_department.Where(d => d.form_project.Any()).OrderBy(d => d.name))
                {
                    var departmentItem = new CascaderItem(department.id.ToString(), department.name);
                    foreach (var ward in department.sys_ward.Where(w => w.form_project.Any()).OrderBy(w => w.name))
                    {
                        var wardItem = new CascaderItem(ward.id.ToString(), ward.name);
                        foreach (var project in ward.form_project.OrderBy(p => p.name))
                        {
                            var projectItem = new CascaderItem(project.id.ToString(), project.name);
                            wardItem.AddItem(projectItem);
                        }
                        departmentItem.AddItem(wardItem);
                    }
                    hospitalItem.AddItem(departmentItem);
                }
                _projectItems.Add(hospitalItem);
            }
            sw2.Stop();
            inj_logger.LogWarning("[性能调试] 构建项目级联菜单 耗时: {ElapsedMs}ms, 项目菜单项数: {ProjectItemCount}",
                sw2.ElapsedMilliseconds, _projectItems.Count);

            // 使用 MedDictService 的全局缓存获取表定义数据
            var sw3 = Stopwatch.StartNew();
            // 通过 MedDictService 获取全局缓存的 table_definition 数据
            var tableDefinitionsDict = await inj_medDictService.GetAllTableDefinitionsAsync(context);
            var tableDefinitions = tableDefinitionsDict.Values.OrderBy(t => t.sorted_index).ToList();
            sw3.Stop();
            inj_logger.LogWarning("[性能调试] 获取表定义 耗时: {ElapsedMs}ms (通过MedDictService全局缓存), 表定义数: {TableCount}",
                sw3.ElapsedMilliseconds, tableDefinitions.Count);

            // 获取所有有效的列定义数据
            var sw4 = Stopwatch.StartNew();
            var tableNodeIds = tableDefinitions
                .Where(t => t.node_type == "Table")
                .Select(t => t.id)
                .ToList();

            var allColumns = await context.column_definition
                .AsNoTracking()
                .Where(c => c.is_valid && tableNodeIds.Contains(c.table_id))
                .OrderBy(c => c.sorted_index)
                .ThenBy(c => c.display_name)
                .ToListAsync();

            // 按 table_id 对列进行分组构建字典
            columnsByTableId = allColumns
                .GroupBy(c => c.table_id)
                .ToDictionary(g => g.Key, g => g.ToList());
            sw4.Stop();
            inj_logger.LogWarning("[性能调试] 获取列定义 耗时: {ElapsedMs}ms, 列定义数: {ColumnCount}",
                sw4.ElapsedMilliseconds, allColumns.Count);

            // 构建医疗字典树状结构
            var sw5 = Stopwatch.StartNew();
            _medDictItems.Clear();
            _medDictItems = BuildMedDictTreeNodes(new Guid("00000000-0000-0000-0000-000000000000"), tableDefinitions);

            // 统计_medDictItems的总元素个数
            int totalCount = CountAllItems(_medDictItems);
            sw5.Stop();
            inj_logger.LogWarning("[性能调试] 构建医疗字典树 耗时: {ElapsedMs}ms, 树节点总数: {TreeNodeCount}",
                sw5.ElapsedMilliseconds, totalCount);

            // 新增：预构建所有table的路径缓存
            var sw6 = Stopwatch.StartNew();
            BuildTablePathCache(tableDefinitions);
            sw6.Stop();
            inj_logger.LogWarning("[性能调试] 构建表路径缓存 耗时: {ElapsedMs}ms, 缓存条目数: {CacheCount}",
                sw6.ElapsedMilliseconds, _tablePathCache.Count);

            stopwatch.Stop();
            inj_logger.LogWarning("[性能调试] FormSetManager.LoadProjectData 完成 - 总耗时: {ElapsedMs}ms", stopwatch.ElapsedMilliseconds);
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            inj_logger.LogError(ex, "[性能调试] LoadProjectData 发生错误 - 耗时: {ElapsedMs}ms", stopwatch.ElapsedMilliseconds);
            throw;
        }
    }

    private List<CascaderItem> BuildMedDictTreeNodes(Guid parentId, List<table_definition> allTables)
    {
        var result = new List<CascaderItem>();

        // 找到所有parent_node_id等于传入parentId的table_definition
        var childTables = allTables.Where(t => t.parent_node_id == parentId)
                                   .OrderBy(t => t.sorted_index)
                                   .ThenBy(t => t.display_name)
                                   .ToList();

        foreach (var table in childTables)
        {
            var tableItem = new CascaderItem(table.id.ToString(), table.display_name);

            if (table.node_type == "Visual")
            {
                // 对于Visual类型的节点，递归构建子节点
                var children = BuildMedDictTreeNodes(table.id, allTables);
                foreach (var child in children)
                {
                    tableItem.AddItem(child);
                }
            }
            else if (table.node_type == "Table")
            {
                // 对于Table类型的节点，添加其关联的column_definition作为叶子节点
                if (columnsByTableId.TryGetValue(table.id, out var columns))
                {
                    var orderedColumns = columns
                                  .OrderBy(c => c.sorted_index)
                                  .ThenBy(c => c.display_name);

                    foreach (var column in orderedColumns)
                    {
                        var columnItem = new CascaderItem(column.id.ToString(), $"{column.display_name} [{column.data_type}]");
                        tableItem.AddItem(columnItem);
                    }
                }
            }

            result.Add(tableItem);
        }

        return result;
    }

    private int CountAllItems(List<CascaderItem> items)
    {
        if (items == null || items.Count == 0)
            return 0;

        int count = items.Count;

        foreach (var item in items)
        {
            if (item.Items != null && item.Items.Any())
            {
                count += CountAllItems(item.Items.ToList());
            }
        }

        return count;
    }

    private async Task OnProjectChanged(CascaderItem[] items)
    {
        var stopwatch = Stopwatch.StartNew();
        inj_logger.LogWarning("[性能调试] OnProjectChanged 开始 - 选择项目: {ProjectValue}", items[^1].Value);

        _selectedFormSet = null;
        _selectedProject = _contextHospitalList.SelectMany(h => h.form_project)
            .FirstOrDefault(p => p.id.ToString() == items[^1].Value);

        var sw1 = Stopwatch.StartNew();
        await InvokeAsync(StateHasChanged);
        sw1.Stop();
        inj_logger.LogWarning("[性能调试] OnProjectChanged-StateHasChanged 耗时: {ElapsedMs}ms", sw1.ElapsedMilliseconds);

        stopwatch.Stop();
        inj_logger.LogWarning("[性能调试] OnProjectChanged 完成 - 总耗时: {ElapsedMs}ms, 项目名: {ProjectName}",
            stopwatch.ElapsedMilliseconds, _selectedProject?.name ?? "未找到");
    }

    private async Task OpenFormsetDialog()
    {
        var parameters = new DialogParameters
            {
                ["_selectedProject"] = _selectedProject,
                ["_selectedFormSet"] = null,
                ["OnFormSetModalClosed"] = EventCallback.Factory.Create(this, async () =>
                {
                    await LoadProjectData();
                    if (_selectedProject != null)
                    {
                        _selectedProject = _contextHospitalList.SelectMany(h => h.form_project)
                            .FirstOrDefault(p => p.id == _selectedProject.id);
                        _selectedFormSet = _selectedProject?.form_form_set
                            .FirstOrDefault(fs => fs?.id == _selectedFormSet?.id);
                    }
                    await InvokeAsync(StateHasChanged);
                })
            };

        var options = new DialogOptions
            {
                MaxWidth = MaxWidth.ExtraSmall,
                FullWidth = true,
                CloseButton = true,
                CloseOnEscapeKey = true
            };

        await inj_dialogService.ShowAsync<Modal_FormSet>("添加表单集", parameters, options);
    }

    private void ToggleCollapse()
    {
        var stopwatch = Stopwatch.StartNew();
        _isCollapsed = !_isCollapsed;
        StateHasChanged();
        stopwatch.Stop();
        inj_logger.LogWarning("[性能调试] ToggleCollapse 耗时: {ElapsedMs}ms, 新状态: {IsCollapsed}",
            stopwatch.ElapsedMilliseconds, _isCollapsed);
    }

    /// <summary>
    /// 构建所有table的路径缓存
    /// </summary>
    private void BuildTablePathCache(List<table_definition> tableDefinitions)
    {
        _tablePathCache.Clear();
        
        // 将table列表转换为字典，便于快速查找
        var tablesById = tableDefinitions.ToDictionary(t => t.id);
        
        // 为每个table构建路径
        foreach (var table in tableDefinitions)
        {
            var path = BuildTablePath(table.id, tablesById);
            _tablePathCache[table.id] = path;
        }
    }

    /// <summary>
    /// 递归构建指定table的完整路径
    /// </summary>
    private string BuildTablePath(Guid tableId, Dictionary<Guid, table_definition> tablesById, List<string> pathParts = null)
    {
        pathParts ??= new List<string>();

        if (!tablesById.TryGetValue(tableId, out var table))
        {
            return "错误: 未找到路径段";
        }

        // 将当前表的显示名称添加到路径开头
        pathParts.Insert(0, table.display_name ?? $"未知(ID:{tableId})");

        // 如果父节点ID存在且有效，则递归获取父路径
        if (table.parent_node_id != Guid.Empty && tablesById.ContainsKey(table.parent_node_id))
        {
            return BuildTablePath(table.parent_node_id, tablesById, pathParts);
        }
        else
        {
            // 到达根节点或父节点无效，返回完整路径字符串
            return string.Join(" > ", pathParts);
        }
    }

    // 重写DisposeAsync来清理缓存
    public override async ValueTask DisposeAsync()
    {
        try
        {
            // 清理路径缓存
            _tablePathCache?.Clear();
            inj_logger.LogWarning("[资源管理] FormSetManager 路径缓存已清理，缓存条目数: {CacheCount}", _tablePathCache?.Count ?? 0);
        }
        catch (Exception ex)
        {
            inj_logger.LogError(ex, "[资源管理] FormSetManager 清理路径缓存时发生错误");
        }
        finally
        {
            // 调用基类的DisposeAsync
            await base.DisposeAsync();
        }
    }
}