DO $$
DECLARE
    -- -----------------------------------------------------------------------------
    -- 变量声明区域
    -- -----------------------------------------------------------------------------
    -- 关键占位符: 顶层父节点ID (例如“既往史”这个V节点的ID)
    -- !!! 重要: 执行前必须将此占位符替换为实际的父节点UUID !!!
    v_past_medical_history_root_id UUID := 'e6d012f2-95bc-11ef-aba4-00163e08a3b0'; -- <--- 请替换为实际的“既往史”顶级V节点ID

    -- 缓存表T节点ID
    v_cache_table_id UUID := '85154641-91f4-4344-8326-8e0e0cbf5325';

    -- 新创建的V节点ID变量
    v_general_diseases_v_id UUID;
    v_intervention_v_id UUID;
    v_personal_social_v_id UUID;
    v_family_v_id UUID;

    -- 新创建的T节点ID变量
    v_chronic_illness_t_id UUID;
    v_infectious_history_t_id UUID;
    v_allergy_history_t_id UUID;
    v_general_surgical_t_id UUID;
    v_lifestyle_history_t_id UUID;
    v_reproductive_history_t_id UUID;
    v_family_illness_t_id UUID;

BEGIN
    RAISE NOTICE '开始重构“既往史”模块的元数据定义...';

    -- -----------------------------------------------------------------------------
    -- 步骤 1: 创建新的V节点 (Visual Groups)
    -- -----------------------------------------------------------------------------
    RAISE NOTICE '步骤 1: 正在创建新的V节点...';

    -- V节点: 一般疾病史
    INSERT INTO "public"."table_definition" ("id", "unique_name", "display_name", "is_valid", "node_type", "parent_node_id", "is_changed", "sorted_index")
    VALUES (uuid_generate_v1(), 'past_medical_history_general_diseases', '一般疾病史', TRUE, 'Visual', v_past_medical_history_root_id, TRUE, 10)
    RETURNING "id" INTO v_general_diseases_v_id;
    RAISE NOTICE '    创建V节点“一般疾病史”，ID: %', v_general_diseases_v_id;

    -- V节点: 手术与操作史
    INSERT INTO "public"."table_definition" ("id", "unique_name", "display_name", "is_valid", "node_type", "parent_node_id", "is_changed", "sorted_index")
    VALUES (uuid_generate_v1(), 'past_medical_history_intervention', '手术与操作史', TRUE, 'Visual', v_past_medical_history_root_id, TRUE, 20)
    RETURNING "id" INTO v_intervention_v_id;
    RAISE NOTICE '    创建V节点“手术与操作史”，ID: %', v_intervention_v_id;

    -- V节点: 个人与社会史
    INSERT INTO "public"."table_definition" ("id", "unique_name", "display_name", "is_valid", "node_type", "parent_node_id", "is_changed", "sorted_index")
    VALUES (uuid_generate_v1(), 'past_medical_history_personal_social', '个人与社会史', TRUE, 'Visual', v_past_medical_history_root_id, TRUE, 30)
    RETURNING "id" INTO v_personal_social_v_id;
    RAISE NOTICE '    创建V节点“个人与社会史”，ID: %', v_personal_social_v_id;

    -- V节点: 家族史
    INSERT INTO "public"."table_definition" ("id", "unique_name", "display_name", "is_valid", "node_type", "parent_node_id", "is_changed", "sorted_index")
    VALUES (uuid_generate_v1(), 'past_medical_history_family', '家族史', TRUE, 'Visual', v_past_medical_history_root_id, TRUE, 40)
    RETURNING "id" INTO v_family_v_id;
    RAISE NOTICE '    创建V节点“家族史”，ID: %', v_family_v_id;

    -- -----------------------------------------------------------------------------
    -- 步骤 2: 创建新的T节点 (Table Definitions)
    -- -----------------------------------------------------------------------------
    RAISE NOTICE '步骤 2: 正在创建新的T节点...';

    -- T节点: 慢性病史 (父V: 一般疾病史)
    INSERT INTO "public"."table_definition" ("id", "unique_name", "display_name", "is_valid", "node_type", "parent_node_id", "is_changed", "sorted_index")
    VALUES (uuid_generate_v1(), 'chronic_illness_history', '慢性病史', TRUE, 'Table', v_general_diseases_v_id, TRUE, 10)
    RETURNING "id" INTO v_chronic_illness_t_id;
    RAISE NOTICE '    创建T节点“慢性病史”，ID: %，父V ID: %', v_chronic_illness_t_id, v_general_diseases_v_id;

    -- T节点: 传染病史 (父V: 一般疾病史)
    INSERT INTO "public"."table_definition" ("id", "unique_name", "display_name", "is_valid", "node_type", "parent_node_id", "is_changed", "sorted_index")
    VALUES (uuid_generate_v1(), 'infectious_history', '传染病史', TRUE, 'Table', v_general_diseases_v_id, TRUE, 20)
    RETURNING "id" INTO v_infectious_history_t_id;
    RAISE NOTICE '    创建T节点“传染病史”，ID: %，父V ID: %', v_infectious_history_t_id, v_general_diseases_v_id;

    -- T节点: 过敏史 (父V: 一般疾病史)
    INSERT INTO "public"."table_definition" ("id", "unique_name", "display_name", "is_valid", "node_type", "parent_node_id", "is_changed", "sorted_index")
    VALUES (uuid_generate_v1(), 'allergy_history', '过敏史', TRUE, 'Table', v_general_diseases_v_id, TRUE, 30)
    RETURNING "id" INTO v_allergy_history_t_id;
    RAISE NOTICE '    创建T节点“过敏史”，ID: %，父V ID: %', v_allergy_history_t_id, v_general_diseases_v_id;

    -- T节点: 通用手术史 (父V: 手术与操作史)
    INSERT INTO "public"."table_definition" ("id", "unique_name", "display_name", "is_valid", "node_type", "parent_node_id", "is_changed", "sorted_index")
    VALUES (uuid_generate_v1(), 'general_surgical_history', '通用手术史', TRUE, 'Table', v_intervention_v_id, TRUE, 10)
    RETURNING "id" INTO v_general_surgical_t_id;
    RAISE NOTICE '    创建T节点“通用手术史”，ID: %，父V ID: %', v_general_surgical_t_id, v_intervention_v_id;

    -- T节点: 生活习惯史 (父V: 个人与社会史)
    INSERT INTO "public"."table_definition" ("id", "unique_name", "display_name", "is_valid", "node_type", "parent_node_id", "is_changed", "sorted_index")
    VALUES (uuid_generate_v1(), 'lifestyle_history', '生活习惯史', TRUE, 'Table', v_personal_social_v_id, TRUE, 10)
    RETURNING "id" INTO v_lifestyle_history_t_id;
    RAISE NOTICE '    创建T节点“生活习惯史”，ID: %，父V ID: %', v_lifestyle_history_t_id, v_personal_social_v_id;

    -- T节点: 婚育与月经史 (父V: 个人与社会史)
    INSERT INTO "public"."table_definition" ("id", "unique_name", "display_name", "is_valid", "node_type", "parent_node_id", "is_changed", "sorted_index")
    VALUES (uuid_generate_v1(), 'reproductive_history', '婚育与月经史', TRUE, 'Table', v_personal_social_v_id, TRUE, 20)
    RETURNING "id" INTO v_reproductive_history_t_id;
    RAISE NOTICE '    创建T节点“婚育与月经史”，ID: %，父V ID: %', v_reproductive_history_t_id, v_personal_social_v_id;

    -- T节点: 家族疾病史 (父V: 家族史)
    INSERT INTO "public"."table_definition" ("id", "unique_name", "display_name", "is_valid", "node_type", "parent_node_id", "is_changed", "sorted_index")
    VALUES (uuid_generate_v1(), 'family_illness_history', '家族疾病史', TRUE, 'Table', v_family_v_id, TRUE, 10)
    RETURNING "id" INTO v_family_illness_t_id;
    RAISE NOTICE '    创建T节点“家族疾病史”，ID: %，父V ID: %', v_family_illness_t_id, v_family_v_id;

    -- -----------------------------------------------------------------------------
    -- 步骤 3: 更新现有的T节点 (重命名, 重新指定父节点)
    -- -----------------------------------------------------------------------------
    RAISE NOTICE '步骤 3: 正在更新现有的T节点...';

    -- T:(78ae2e08-bc36-11ef-aba4-00163e08a3b0) past_vascular_surgery_history -> vascular_intervention_history
    UPDATE "public"."table_definition"
    SET
        "unique_name" = 'vascular_intervention_history',
        "display_name" = '血管外科介入史',
        "parent_node_id" = v_intervention_v_id,
        "sorted_index" = 20,
        "is_changed" = TRUE
    WHERE "id" = '78ae2e08-bc36-11ef-aba4-00163e08a3b0';
    RAISE NOTICE '    更新T节点 (原past_vascular_surgery_history) ID: 78ae2e08-bc36-11ef-aba4-00163e08a3b0 为 "血管外科介入史", 父V ID: %', v_intervention_v_id;

    -- T:(a79709c2-95dc-11ef-aba4-00163e08a3b0) past_neurosurgery_history -> neurosurgical_intervention_history
    UPDATE "public"."table_definition"
    SET
        "unique_name" = 'neurosurgical_intervention_history',
        "display_name" = '神经外科介入与治疗史',
        "parent_node_id" = v_intervention_v_id,
        "sorted_index" = 30,
        "is_changed" = TRUE
    WHERE "id" = 'a79709c2-95dc-11ef-aba4-00163e08a3b0';
    RAISE NOTICE '    更新T节点 (原past_neurosurgery_history) ID: a79709c2-95dc-11ef-aba4-00163e08a3b0 为 "神经外科介入与治疗史", 父V ID: %', v_intervention_v_id;

    -- -----------------------------------------------------------------------------
    -- 步骤 4: 迁移C节点 (Column Definitions) 到新的T节点
    -- 注意: 此处仅迁移table_id。unique_name, display_name, sorted_index 的更新
    --       是根据之前讨论的设计方案来确定的，这里假设这些属性已经符合目标表结构，
    --       如果需要批量修改这些属性，需要额外的UPDATE语句。
    --       为了简化，这里仅做table_id迁移，并设定is_changed=TRUE。
    -- -----------------------------------------------------------------------------
    RAISE NOTICE '步骤 4: 正在迁移C节点...';

    RAISE NOTICE '    迁移C节点到“慢性病史” (T ID: %)...', v_chronic_illness_t_id;
    UPDATE "public"."column_definition" SET "table_id" = v_chronic_illness_t_id, "is_changed" = TRUE WHERE "id" IN (
        '8721766f-93ce-415d-93e0-f3fdf358c548', '17d1603b-2ebb-4dff-87be-7c98e0a73329', 'c5248ae4-3968-4e90-931c-7037147bf8d3', '31f89202-429e-491e-a1a8-2e20d8c051f5',
        'e5c76c2d-c11f-4d1b-bfc9-56418d71a553', '24fa23d0-35ac-4ee6-a97e-56419a0e054d', 'e4af4982-6309-4177-aa71-5d63d31c05ff', 'fd730017-dc58-4aca-953b-5641ad3f6c2f',
        'ae623659-5d4f-4bed-b9b0-7c98944bf2c5', '51fdd9af-53fa-4b40-8dbc-1e5a5d694657', 'a24fb108-2056-4b1b-b0c9-61401c14e5aa', '4eb685e3-9539-4081-b54f-614027d2194c',
        'b83b5076-7c80-4dac-9e52-6140310cdd3e', 'c3a19c72-a846-40d5-909e-613e68b57553', '7e0db0c4-c939-4fe9-8f3a-613e728ee352', '8e40f6bc-6209-461a-b592-613df6721ef6',
        '618a5d14-8183-42f6-8970-613e499e9e00', '144b20cc-96b3-4386-a09a-6c2a5c412f0c', '5e3f1e7a-c4bc-4cf2-8fbf-6c2a3be92f61', 'fbf198bf-9111-48cd-ad99-6c2a4e6239b0',
        'da332153-f27d-4c31-91f5-7c986619fb9b', '45970802-69b9-42fc-8e4a-61413d49a43a', 'd7525e3f-e23b-4e7f-91eb-61414679792f', '0ac44d0f-c055-456a-a7c5-61415f57863f',
        '5216373d-8f03-4e6d-a3ca-61414d2fba88', '41731ec4-f496-473c-80fa-7c98f4c03721', '4c0d5e32-614e-4aef-a901-614156ec17db', '55b4d932-9268-42e0-b038-7c98b4ed18f1',
        'c53171e8-917e-4cd7-9f1b-7c98ca1c89f1', '80a28b6b-04ae-411c-b91d-7c98a8cf6a2d', '2e23972e-c200-4f5f-ad71-7c987dff9163', '7e786854-6122-4d98-978b-7c983d9a53ed',
        '742653cc-466e-4f39-8256-614166220364', '9f8876ed-dd2b-4f49-b7d7-61403770d9de', 'e96d189f-6977-40c7-96de-6c2a63aab51c', 'c34e75e2-2fe5-44f1-9e81-6c2a70558a71',
        '7069850b-d4d3-4783-a86d-6c2a75b078ee', 'c16e5bbb-cc8d-4156-8aac-61312b098344', 'a0670d14-8880-40c6-be27-613e369f4a33', '6b10baa2-e65a-4178-a9fe-61313ed529f6',
        '9ec2f048-0092-434c-af59-61313494e413', '7ad6cd85-09a3-4df4-8c34-613e440511ed',
        -- 从原 past_neurosurgery_history 迁移过来的字段
        '7ff1e38c-12e3-44f4-9440-563b70b846b1', '58176f8e-d2b8-4d85-9322-7c911673122a', '3e367d95-76f6-4911-a6f9-563b3a117fa7', '3bff408f-e3b5-4ad2-ba58-563b53b98be1',
        '7a329d19-baa1-49e5-af4e-563b43b8e54c', '562b1547-d6ac-46b0-ad29-563b5f8d545c'
    );

    RAISE NOTICE '    迁移C节点到“传染病史” (T ID: %)...', v_infectious_history_t_id;
    UPDATE "public"."column_definition" SET "table_id" = v_infectious_history_t_id, "is_changed" = TRUE WHERE "id" IN (
        '90823135-b33a-485e-858d-159f7bfd663c'
    );

    RAISE NOTICE '    迁移C节点到“过敏史” (T ID: %)...', v_allergy_history_t_id;
    UPDATE "public"."column_definition" SET "table_id" = v_allergy_history_t_id, "is_changed" = TRUE WHERE "id" IN (
        '43372964-bf96-4805-8c50-517313f9b6f9', 'ba6e6a63-6804-476d-9b8a-7cae71477a22', '3bebb59e-cc8d-4068-aa83-51732cde03d1', 'ce83f93e-40c9-4407-b829-7ca2fd12deb2',
        '81195a57-da98-469e-b9d8-5641d5c7a99e', '1c29e2c5-e745-4db0-8569-7ca2edbbc019', '4a23c3b4-ae44-4f83-a190-7ca30e19ebaa'
    );

    RAISE NOTICE '    迁移C节点到“通用手术史” (T ID: %)...', v_general_surgical_t_id;
    UPDATE "public"."column_definition" SET "table_id" = v_general_surgical_t_id, "is_changed" = TRUE WHERE "id" IN (
        'aa64772f-1114-47db-a15c-f7f13a2a5757', '60212e31-4017-41a6-9e78-613bd1862ff2', '0bfd9fbb-c6b0-4bc6-a7d1-1e5beedbb17c', '19f2e7c8-accc-4c08-adbb-6d9694235ade',
        '96aa8eaf-0fd7-47f7-a360-f804ce942c61', '182ea341-3133-4d2a-b5f7-59f2f128425d', 'd61cb5d8-a2b9-4fcf-9327-f7f14b7a43f5', '5c8ff316-1e0c-4808-9275-7c919c55cb96',
        '36afbe4b-fec9-4df4-8c65-7c918173e923', '4db55aae-1ef5-4763-99b7-613cdb3ebe24', 'f30cc1f7-65c3-4393-a32c-613ceae97416', '18dfe210-a29b-49fe-a1f3-8df5dab1680e',
        'd89b42d2-2678-49ac-9471-613d1cf4d8f2', '047effb0-d31a-45e3-8cec-81677fd8f2c3', 'e1b0e066-8955-4501-853e-613d266d97e4', 'c4af6066-97be-47e2-b3f8-613cd1946f72',
        '889176a8-ebf4-4fd5-a79e-613d03496945', 'fdbde844-0079-40f4-954c-613d193c498f', '96a70046-cf98-459a-a3ba-613cf37557fd'
    );

    RAISE NOTICE '    迁移C节点到“生活习惯史” (T ID: %)...', v_lifestyle_history_t_id;
    UPDATE "public"."column_definition" SET "table_id" = v_lifestyle_history_t_id, "is_changed" = TRUE WHERE "id" IN (
        'd47279db-394d-4aae-922b-f4d60e0f0d2e', '62f60a4c-a127-44a8-848a-f4001061aa68', '624b7821-068c-40af-b079-7c91d3908f88', 'e5d1b016-a5ba-4805-813a-1e5c5e3d86bd',
        'aef4aba6-42e4-4f75-bcdf-1e5c675ff28b', 'daaef508-7361-4318-a833-6c3339e51edc', '47675f70-bbb6-418c-9e8c-8166ef9f702b', '167523e7-94bf-488b-9220-f40028c34e89',
        '48aed4a5-4845-4467-bdc8-7c91ee76fdb5', '31a87dcd-1047-46b8-adda-6c334807396d', 'fa29e289-82e9-4916-8df0-81671a568dde', 'b36b979e-c079-491f-bbab-6c3382233873',
        '4bc9f634-7651-4754-88ae-81672509bfd6'
    );

    RAISE NOTICE '    迁移C节点到“婚育与月经史” (T ID: %)...', v_reproductive_history_t_id;
    UPDATE "public"."column_definition" SET "table_id" = v_reproductive_history_t_id, "is_changed" = TRUE WHERE "id" IN (
        '05a7a771-180e-4c56-8060-613d6749c564'
    );

    RAISE NOTICE '    迁移C节点到“家族疾病史” (T ID: %)...', v_family_illness_t_id;
    UPDATE "public"."column_definition" SET "table_id" = v_family_illness_t_id, "is_changed" = TRUE WHERE "id" IN (
        '6a464e7e-105c-4aa9-8739-f7dc68fed1b8', '198605bc-68ec-4d35-81b0-7cbc276fda50', '38c02cd5-168c-4b58-9446-6d97eb43bc47', 'b4904df0-f437-4f4f-84a1-f7dd7504787d',
        '62b89aac-fa5a-4e64-abeb-6d97adaf0fc0', 'b1747b0b-ed2a-481e-adb7-6d97b7baf796', '997df5c5-bb0a-449a-8055-6d97ce630555'
    );

    -- 迁移C节点到缓存表
    RAISE NOTICE '    迁移C节点到缓存表 (T ID: %)...', v_cache_table_id;
    UPDATE "public"."column_definition"
    SET
        "table_id" = v_cache_table_id,
        "is_changed" = TRUE,
        "sorted_index" = 1000 -- 示例：归档的条目可以有较大的sorted_index
    WHERE "id" IN (
        '20b07000-caf3-4a88-aeea-614040fd128f', -- pain_location
        '22fd2c06-b52a-415d-9961-6140518cbe87', -- relief_method
        'da5ce3b0-2e0e-4052-b9d0-61404b614a77'  -- pain_radiation_site
    );

    -- -----------------------------------------------------------------------------
    -- 步骤 5: 作废旧的T节点 (其下的C节点已全部迁移)
    -- -----------------------------------------------------------------------------
    RAISE NOTICE '步骤 5: 正在作废旧的T节点...';

    -- 作废原 T:(87e40e9c-b37b-11ef-aba4-00163e08a3b0) past_general_history
    UPDATE "public"."table_definition"
    SET
        "is_valid" = FALSE,
        "is_changed" = TRUE
    WHERE "id" = '87e40e9c-b37b-11ef-aba4-00163e08a3b0';
    RAISE NOTICE '    作废旧T节点 "past_general_history", ID: 87e40e9c-b37b-11ef-aba4-00163e08a3b0';

    RAISE NOTICE '“既往史”模块元数据重构成功完成。';

EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE '发生错误，正在回滚事务...';
        RAISE NOTICE '错误信息: %', SQLERRM;
        RAISE NOTICE '错误状态: %', SQLSTATE;
        RAISE; -- 重新抛出当前异常，这将导致DO块终止并回滚
END $$;