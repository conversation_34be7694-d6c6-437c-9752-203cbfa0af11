@page "/card-mover"
@using BusinessLogic.Cube
@using DataAccess.Models
@using Microsoft.EntityFrameworkCore
@using System.ComponentModel.DataAnnotations

@inject CardService CardService
@inject CubeContext DbContext
@inject ISnackbar Snackbar
@inject ILogger<CardMover> Logger

<PageTitle>卡片跨表单移动工具</PageTitle>

<h3>卡片跨表单移动测试工具</h3>
<p>此页面用于测试跨表单移动卡片功能，请输入相关ID进行操作</p>

<div class="row mb-4">
    <div class="col-md-6">
        <h4>输入参数</h4>
        <div class="card">
            <div class="card-body">
                <EditForm Model="@model" OnValidSubmit="HandleValidSubmit">
                    <DataAnnotationsValidator />
                    <ValidationSummary />

                    <div class="mb-3">
                        <label class="form-label">源卡片ID</label>
                        <input type="text" class="form-control" @bind="model.CardId" placeholder="输入要移动的卡片GUID" />
                        <ValidationMessage For="@(() => model.CardId)" />
                    </div>

                    <div class="mb-3">
                        <label class="form-label">目标表单ID</label>
                        <input type="text" class="form-control" @bind="model.TargetFormId" placeholder="输入目标表单GUID" />
                        <ValidationMessage For="@(() => model.TargetFormId)" />
                    </div>

                    <div class="mb-3">
                        <label class="form-label">目标父卡片ID (可选)</label>
                        <input type="text" class="form-control" @bind="model.TargetParentCardId" placeholder="输入目标父卡片GUID (可不填)" />
                        <ValidationMessage For="@(() => model.TargetParentCardId)" />
                    </div>

                    <button type="submit" class="btn btn-primary" disabled="@isProcessing">
                        @if (isProcessing)
                        {
                            <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                            <span>处理中...</span>
                        }
                        else
                        {
                            <span>执行移动操作</span>
                        }
                    </button>
                </EditForm>
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <h4>对象信息</h4>
        <div class="card">
            <div class="card-body">
                @if (card != null)
                {
                    <div class="mb-3">
                        <h5>源卡片信息</h5>
                        <p><strong>ID:</strong> @card.id</p>
                        <p><strong>名称:</strong> @card.name</p>
                        <p><strong>表单:</strong> @card.form_name (@card.form_id)</p>
                        <p><strong>表单集:</strong> @card.form_set_name (@card.form_set_id)</p>
                    </div>
                }

                @if (targetForm != null)
                {
                    <div class="mb-3">
                        <h5>目标表单信息</h5>
                        <p><strong>ID:</strong> @targetForm.id</p>
                        <p><strong>名称:</strong> @targetForm.name</p>
                        <p><strong>表单集:</strong> @targetForm.form_set_name (@targetForm.form_set_id)</p>
                    </div>
                }

                @if (targetParentCard != null)
                {
                    <div class="mb-3">
                        <h5>目标父卡片信息</h5>
                        <p><strong>ID:</strong> @targetParentCard.id</p>
                        <p><strong>名称:</strong> @targetParentCard.name</p>
                        <p><strong>表单:</strong> @targetParentCard.form_name (@targetParentCard.form_id)</p>
                    </div>
                }
            </div>
        </div>
    </div>
</div>

@if (moveResult != null)
{
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header @(moveResult.IsSuccess ? "bg-success text-white" : "bg-danger text-white")">
                    <h5 class="mb-0">操作结果: @(moveResult.IsSuccess ? "成功" : "失败")</h5>
                </div>
                <div class="card-body">
                    <p><strong>消息:</strong> @moveResult.Message</p>
                    @if (moveResult.IsSuccess)
                    {
                        <p><strong>移动的卡片数:</strong> @moveResult.MovedCardCount</p>
                        <p><strong>移动的问题数:</strong> @moveResult.MovedQuestionCount</p>
                    }
                </div>
            </div>
        </div>
    </div>
}

@code {
    private CardMoveViewModel model = new();
    private form_card? card;
    private form_form? targetForm;
    private form_card? targetParentCard;
    private CardMoveResult? moveResult;
    private bool isProcessing = false;

    public class CardMoveViewModel
    {
        [Required(ErrorMessage = "请输入源卡片ID")]
        public string CardId { get; set; } = string.Empty;

        [Required(ErrorMessage = "请输入目标表单ID")]
        public string TargetFormId { get; set; } = string.Empty;

        public string? TargetParentCardId { get; set; }
    }

    private async Task HandleValidSubmit()
    {
        isProcessing = true;
        moveResult = null;
        
        try
        {
            // 解析ID
            if (!Guid.TryParse(model.CardId, out var cardId))
            {
                Snackbar.Add("无效的卡片ID格式", Severity.Error);
                return;
            }

            if (!Guid.TryParse(model.TargetFormId, out var targetFormId))
            {
                Snackbar.Add("无效的表单ID格式", Severity.Error);
                return;
            }

            Guid? targetParentCardId = null;
            if (!string.IsNullOrWhiteSpace(model.TargetParentCardId) &&
                Guid.TryParse(model.TargetParentCardId, out var parsedParentId))
            {
                targetParentCardId = parsedParentId;
            }

            // 从数据库加载实体
            card = await DbContext.form_card
                .Include(c => c.form)
                .FirstOrDefaultAsync(c => c.id == cardId);

            if (card == null)
            {
                Snackbar.Add($"未找到ID为 {cardId} 的卡片", Severity.Error);
                return;
            }

            targetForm = await DbContext.form_form
                .FirstOrDefaultAsync(f => f.id == targetFormId);

            if (targetForm == null)
            {
                Snackbar.Add($"未找到ID为 {targetFormId} 的表单", Severity.Error);
                return;
            }

            targetParentCard = null;
            if (targetParentCardId.HasValue)
            {
                targetParentCard = await DbContext.form_card
                    .FirstOrDefaultAsync(c => c.id == targetParentCardId.Value);

                if (targetParentCard == null)
                {
                    Snackbar.Add($"未找到ID为 {targetParentCardId} 的父卡片", Severity.Error);
                    return;
                }
            }

            // 执行跨表单移动操作
            Logger.LogInformation($"开始移动卡片: {card.name}({card.id}) 到表单 {targetForm.name}({targetForm.id})" +
                                 $"{(targetParentCard == null ? " 根目录" : $" 卡片 {targetParentCard.name}({targetParentCard.id})")}");

            moveResult = await CardService.CrossMoveCardToAnotherContainerAsync(
                DbContext, card, targetForm, targetParentCard);

            // 显示操作结果
            if (moveResult.IsSuccess)
            {
                Snackbar.Add($"卡片移动成功: {moveResult.Message}", Severity.Success);
            }
            else
            {
                Snackbar.Add($"卡片移动失败: {moveResult.Message}", Severity.Error);
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "卡片移动过程中发生异常");
            Snackbar.Add($"发生错误: {ex.Message}", Severity.Error);
        }
        finally
        {
            isProcessing = false;
        }
    }
} 