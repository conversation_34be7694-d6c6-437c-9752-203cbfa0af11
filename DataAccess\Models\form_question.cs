﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace DataAccess.Models;

public partial class form_question
{
    /// <summary>
    /// 题目ID
    /// </summary>
    public Guid id { get; set; }

    /// <summary>
    /// 题卡ID
    /// </summary>
    public Guid card_id { get; set; }

    /// <summary>
    /// 表名
    /// </summary>
    public string table_name { get; set; }

    /// <summary>
    /// 列名
    /// </summary>
    public string column_name { get; set; }

    /// <summary>
    /// 数据类型
    /// </summary>
    public string data_type { get; set; }

    /// <summary>
    /// 显示组件
    /// </summary>
    public string display_component { get; set; }

    /// <summary>
    /// 显示名称
    /// </summary>
    public string display_name { get; set; }

    /// <summary>
    /// 标注文字
    /// </summary>
    public string label_text { get; set; }

    /// <summary>
    /// 提示文字
    /// </summary>
    public string prompt_text { get; set; }

    /// <summary>
    /// 前缀
    /// </summary>
    public string prefix_text { get; set; }

    /// <summary>
    /// 后缀
    /// </summary>
    public string suffix_text { get; set; }

    /// <summary>
    /// 显示样式
    /// </summary>
    public string display_style { get; set; }

    /// <summary>
    /// 量纲
    /// </summary>
    public string dimension_text { get; set; }

    /// <summary>
    /// 输入框宽度比例
    /// </summary>
    public short placeholder_width_ratio { get; set; }

    /// <summary>
    /// 排序索引
    /// </summary>
    public int sort_index { get; set; }

    /// <summary>
    /// 是否必填
    /// </summary>
    public bool is_required { get; set; }

    /// <summary>
    /// 是否禁用
    /// </summary>
    public bool is_disabled { get; set; }

    /// <summary>
    /// 是否隐藏
    /// </summary>
    public bool is_hidden { get; set; }

    /// <summary>
    /// 表定义ID
    /// </summary>
    public Guid table_definition_id { get; set; }

    /// <summary>
    /// 列定义ID
    /// </summary>
    public Guid column_definition_id { get; set; }

    /// <summary>
    /// 量纲ID
    /// </summary>
    public Guid? dimension_id { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime created_at { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime updated_at { get; set; }

    /// <summary>
    /// 是否用Tip显示标注文字
    /// </summary>
    public bool is_tip_label { get; set; }

    /// <summary>
    /// 是否显示标注文字
    /// </summary>
    public bool is_show_label { get; set; }

    /// <summary>
    /// 虚拟卡片标志
    /// </summary>
    public string vcard_flag { get; set; }

    /// <summary>
    /// 虚拟卡片名称
    /// </summary>
    public string vcard_name { get; set; }

    /// <summary>
    /// 保留字段5
    /// </summary>
    public string reserved5 { get; set; }

    /// <summary>
    /// 保留字段6
    /// </summary>
    public string reserved6 { get; set; }

    /// <summary>
    /// 保留字段7
    /// </summary>
    public string reserved7 { get; set; }

    /// <summary>
    /// 保留字段8
    /// </summary>
    public string reserved8 { get; set; }

    /// <summary>
    /// 保留字段9
    /// </summary>
    public string reserved9 { get; set; }

    /// <summary>
    /// 是否多选
    /// </summary>
    public bool select_is_multiple_choice { get; set; }

    /// <summary>
    /// 排序选项子集
    /// </summary>
    public List<string> select_sorted_option_subset { get; set; }

    /// <summary>
    /// 默认选中项
    /// </summary>
    public List<string> select_default_option { get; set; }

    /// <summary>
    /// 是否需要“其它”选项
    /// </summary>
    public bool select_is_has_others { get; set; }

    /// <summary>
    /// 选择题保留字段6
    /// </summary>
    public string select_reserved6 { get; set; }

    /// <summary>
    /// 选择题保留字段7
    /// </summary>
    public string select_reserved7 { get; set; }

    /// <summary>
    /// 选择题保留字段8
    /// </summary>
    public string select_reserved8 { get; set; }

    /// <summary>
    /// 选择题保留字段9
    /// </summary>
    public string select_reserved9 { get; set; }

    /// <summary>
    /// 是否多行
    /// </summary>
    public bool text_is_multiple_line { get; set; }

    /// <summary>
    /// 宽度
    /// </summary>
    public short? text_width { get; set; }

    /// <summary>
    /// 最大行数
    /// </summary>
    public int? text_max_line { get; set; }

    /// <summary>
    /// 文本最大长度
    /// </summary>
    public int? text_max_length { get; set; }

    /// <summary>
    /// 文本保留字段5
    /// </summary>
    public string text_reserved5 { get; set; }

    /// <summary>
    /// 文本保留字段6
    /// </summary>
    public string text_reserved6 { get; set; }

    /// <summary>
    /// 文本保留字段7
    /// </summary>
    public string text_reserved7 { get; set; }

    /// <summary>
    /// 文本保留字段8
    /// </summary>
    public string text_reserved8 { get; set; }

    /// <summary>
    /// 文本保留字段9
    /// </summary>
    public string text_reserved9 { get; set; }

    /// <summary>
    /// 是否整数
    /// </summary>
    public bool number_is_integer { get; set; }

    /// <summary>
    /// 小数位数
    /// </summary>
    public short? number_decimal_places { get; set; }

    /// <summary>
    /// 格式
    /// </summary>
    public string number_format { get; set; }

    /// <summary>
    /// 最小值
    /// </summary>
    public decimal? number_min { get; set; }

    /// <summary>
    /// 最大值
    /// </summary>
    public decimal? number_max { get; set; }

    /// <summary>
    /// 最小值是否包含
    /// </summary>
    public bool? number_min_inclusive { get; set; }

    /// <summary>
    /// 最大值是否包含
    /// </summary>
    public bool? number_max_inclusive { get; set; }

    /// <summary>
    /// 默认值
    /// </summary>
    public decimal? number_default_value { get; set; }

    /// <summary>
    /// 数字保留字段9
    /// </summary>
    public string number_reserved9 { get; set; }

    /// <summary>
    /// 日期格式
    /// </summary>
    public string date_format { get; set; }

    /// <summary>
    /// 最小日期
    /// </summary>
    public DateTime? date_min { get; set; }

    /// <summary>
    /// 最大日期
    /// </summary>
    public DateTime? date_max { get; set; }

    /// <summary>
    /// 日期保留字段4
    /// </summary>
    public string date_reserved4 { get; set; }

    /// <summary>
    /// 日期保留字段5
    /// </summary>
    public string date_reserved5 { get; set; }

    /// <summary>
    /// 日期保留字段6
    /// </summary>
    public string date_reserved6 { get; set; }

    /// <summary>
    /// 日期保留字段7
    /// </summary>
    public string date_reserved7 { get; set; }

    /// <summary>
    /// 日期保留字段8
    /// </summary>
    public string date_reserved8 { get; set; }

    /// <summary>
    /// 日期保留字段9
    /// </summary>
    public string date_reserved9 { get; set; }

    /// <summary>
    /// 最大上传文件数量
    /// </summary>
    public short? file_max_file_count { get; set; }

    public Guid? pre_uid { get; set; }

    public Guid? next_uid { get; set; }

    /// <summary>
    /// 表单ID
    /// </summary>
    public Guid form_id { get; set; }

    /// <summary>
    /// 表单名称
    /// </summary>
    public string form_name { get; set; }

    /// <summary>
    /// 表单集ID
    /// </summary>
    public Guid form_set_id { get; set; }

    /// <summary>
    /// 表单集名称
    /// </summary>
    public string form_set_name { get; set; }

    /// <summary>
    /// 项目ID
    /// </summary>
    public Guid project_id { get; set; }

    /// <summary>
    /// 项目名称
    /// </summary>
    public string project_name { get; set; }

    /// <summary>
    /// 所属病区ID
    /// </summary>
    public Guid ward_id { get; set; }

    /// <summary>
    /// 所属病区名称
    /// </summary>
    public string ward_name { get; set; }

    /// <summary>
    /// 所属科室ID
    /// </summary>
    public Guid department_id { get; set; }

    /// <summary>
    /// 所属科室名称
    /// </summary>
    public string department_name { get; set; }

    /// <summary>
    /// 所属医院ID
    /// </summary>
    public Guid hospital_id { get; set; }

    /// <summary>
    /// 所属医院名称
    /// </summary>
    public string hospital_name { get; set; }

    /// <summary>
    /// 选了我，其他进不来
    /// </summary>
    public List<string> select_no_others_allowed { get; set; }

    /// <summary>
    /// 是否有默认值
    /// </summary>
    public bool has_default_value { get; set; }

    public virtual form_card card { get; set; }

    public virtual column_definition column_definition { get; set; }

    public virtual sys_department department { get; set; }

    public virtual form_form form { get; set; }

    public virtual form_form_set form_set { get; set; }

    public virtual sys_hospital hospital { get; set; }

    public virtual form_project project { get; set; }

    public virtual table_definition table_definition { get; set; }

    public virtual sys_ward ward { get; set; }
}