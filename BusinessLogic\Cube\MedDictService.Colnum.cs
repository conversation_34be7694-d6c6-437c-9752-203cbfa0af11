using System;
using Microsoft.EntityFrameworkCore;
using Infrastructure;
using DataAccess.Models;

namespace BusinessLogic.Cube;

public partial class MedDictService : IMedDictService
{
    #region 列管理方法
    
    /// <summary>
    /// 添加一个新的列定义到指定的表中（但不保存更改）
    /// </summary>
    /// <param name="context">数据库上下文</param>
    /// <param name="newColumnData">包含新列信息的对象，其table_id必须已设置</param>
    /// <returns>已添加到上下文的新创建的列定义实体</returns>
    /// <exception cref="ArgumentException">当输入验证失败时抛出</exception>
    /// <exception cref="InvalidOperationException">当无法找到有效的父表时抛出</exception>
    public async Task<column_definition> AddColumnDefinitionAsync(CubeContext context, column_definition newColumnData)
    {
        if (context == null)
            throw new ArgumentNullException(nameof(context), "数据库上下文不能为空");
            
        if (newColumnData == null)
            throw new ArgumentNullException(nameof(newColumnData), "新列数据不能为空");
            
        var tableId = newColumnData.table_id;
        if (tableId == Guid.Empty)
            throw new ArgumentException("新列数据必须包含有效的 table_id", nameof(newColumnData));
            
        // 验证表是否存在且有效
        var parentTable = await context.table_definition
            .FirstOrDefaultAsync(t => t.id == tableId && t.is_valid);
            
        if (parentTable == null)
            throw new InvalidOperationException($"找不到指定的表 (ID: {tableId})");
            
        if (parentTable.node_type != "Table")
            throw new InvalidOperationException($"指定的节点 (ID: {tableId}) 不是表类型");
            
        // 规范化并验证唯一编码
        var uniqueName = (newColumnData.unique_name ?? "").Trim().ToLower();
        
        if (string.IsNullOrWhiteSpace(uniqueName))
            throw new ArgumentException("列的唯一编码不能为空", nameof(newColumnData));
            
        if (uniqueName.Length > 60)
            throw new ArgumentException("列的唯一编码不能超过60个字符", nameof(newColumnData));
            
        // 验证唯一编码是否与表名冲突
        if (string.Equals(uniqueName, parentTable.unique_name, StringComparison.OrdinalIgnoreCase))
            throw new ArgumentException($"列的唯一编码不能与表名 '{parentTable.unique_name}' 相同", nameof(newColumnData));
            
        // 验证唯一编码是否在表中已存在
        var duplicateExists = await context.column_definition
            .AnyAsync(c => c.table_id == tableId && 
                          c.is_valid && 
                          c.unique_name.ToLower() == uniqueName);
                          
        if (duplicateExists)
            throw new ArgumentException($"列的唯一编码 '{uniqueName}' 在表 '{parentTable.display_name}' 中已存在", nameof(newColumnData));
            
        // 验证显示名称
        if (string.IsNullOrWhiteSpace(newColumnData.display_name))
            throw new ArgumentException("列的显示名称不能为空", nameof(newColumnData));
            
        // 计算新列的排序索引
        int maxSortedIndex = 0;
        var existingColumns = await context.column_definition
            .Where(c => c.table_id == tableId && c.is_valid)
            .Select(c => c.sorted_index)
            .ToListAsync();
            
        if (existingColumns.Any())
        {
            maxSortedIndex = existingColumns.Max();
        }
        
        // 创建新的列定义实体
        var createdColumn = new column_definition
        {
            id = SequentialGuidGenerator.NewGuid(),
            unique_name = uniqueName,
            display_name = newColumnData.display_name,
            data_type = newColumnData.data_type,
            table_id = tableId, // 使用从newColumnData获取的tableId
            sorted_index = maxSortedIndex + 1,
            is_valid = true,
            option_set = newColumnData.data_type == "选择" ? 
                (newColumnData.option_set ?? new List<string>()) : 
                new List<string>()
        };
        
        // 添加到上下文（但不保存）
        context.column_definition.Add(createdColumn);
        
        return createdColumn;
    }
    
    /// <summary>
    /// 更新指定的列定义（但不保存更改）
    /// </summary>
    /// <param name="context">数据库上下文</param>
    /// <param name="columnData">包含更新列信息的对象，其 id 必须有效</param>
    /// <exception cref="ArgumentNullException">参数为空时抛出</exception>
    /// <exception cref="ArgumentException">输入验证失败时抛出</exception>
    /// <exception cref="InvalidOperationException">当列或其表不存在，或验证失败时抛出</exception>
    public async Task UpdateColumnDefinitionAsync(CubeContext context, column_definition columnData)
    {
        if (context == null)
            throw new ArgumentNullException(nameof(context));
        if (columnData == null)
            throw new ArgumentNullException(nameof(columnData));
        if (columnData.id == Guid.Empty)
            throw new ArgumentException("列数据必须包含有效的 id", nameof(columnData));

        // 验证唯一编码 (传入 columnData)
        await ValidateUniqueNameForUpdateAsync(context, columnData);

        // 需要知道原始 TableId 来判断是否发生变化
        // 这需要一种方法来获取原始 TableId。由于我们不再加载原始实体，
        // 简单的 context.Update 无法直接处理这种情况。
        // 为了保持逻辑，我们仍然需要加载原始 TableId
        var originalTableId = await context.column_definition
            .Where(c => c.id == columnData.id)
            .Select(c => c.table_id)
            .FirstOrDefaultAsync();

        if (originalTableId == Guid.Empty)
        {   // 如果找不到原始记录，也意味着无法更新
            throw new InvalidOperationException($"尝试更新的列 (ID: {columnData.id}) 在数据库中未找到，无法确定原始表 ID。");
        }
        
        // 如果列所属表发生了变化，需要更新排序索引 (在 columnData 上更新)
        if (columnData.table_id != originalTableId)
        {
            await HandleTableChangeForColumnAsync(context, columnData);
        }

        // 清理 option_set 如果数据类型不是 "选择" (在 columnData 上操作)
        if (columnData.data_type != "选择")
        {
            columnData.option_set.Clear();
        }
        else
        {
            // 确保选项集不为空或包含无效条目
             columnData.option_set = columnData.option_set?
                                               .Select(option => option.Trim())
                                               .Where(option => !string.IsNullOrWhiteSpace(option))
                                               .Distinct()
                                               .ToList() ?? new List<string>();
        }

        // --- 使用 context.Update 标记实体为已修改 ---
        context.Update(columnData);

        // 不需要调用 SaveChangesAsync
    }

    /// <summary>
    /// 删除指定的列定义（逻辑删除，但不保存更改）
    /// </summary>
    /// <param name="context">数据库上下文</param>
    /// <param name="columnId">要删除的列的ID</param>
    /// <exception cref="ArgumentNullException">context 为空时抛出</exception>
    /// <exception cref="ArgumentException">columnId 无效时抛出</exception>
    /// <exception cref="InvalidOperationException">当列不存在时抛出</exception>
    public async Task DeleteColumnDefinitionAsync(CubeContext context, Guid columnId)
    {
        if (context == null)
            throw new ArgumentNullException(nameof(context));
        if (columnId == Guid.Empty)
            throw new ArgumentException("无效的列 ID", nameof(columnId));

        var columnToDelete = await context.column_definition
            .FirstOrDefaultAsync(c => c.id == columnId);

        if (columnToDelete == null)
            throw new InvalidOperationException($"尝试删除的列 (ID: {columnId}) 在数据库中未找到。");

        if (columnToDelete.is_valid) // 只有当它当前有效时才进行操作
        {
             columnToDelete.is_valid = false;
             // 可以在这里添加其他逻辑，例如更新修改时间等
        }
        
        // 不需要调用 SaveChangesAsync
    }

    /// <summary>
    /// 改变指定列的排序顺序，并重新索引同一表中的所有列（但不保存更改）
    /// </summary>
    /// <param name="context">数据库上下文</param>
    /// <param name="columnId">要改变排序顺序的列的ID</param>
    /// <param name="direction">改变的方向（"up" 或 "down"）</param>
    /// <exception cref="ArgumentNullException">context 为空时抛出</exception>
    /// <exception cref="ArgumentException">columnId 无效或 direction 无效时抛出</exception>
    /// <exception cref="InvalidOperationException">当列或其表不存在，或无法移动时抛出</exception>
    public async Task ChangeColumnSortOrderAsync(CubeContext context, Guid columnId, string direction)
    {
        if (context == null)
            throw new ArgumentNullException(nameof(context));
        if (columnId == Guid.Empty)
            throw new ArgumentException("无效的列 ID", nameof(columnId));
        if (direction != "up" && direction != "down")
            throw new ArgumentException("无效的方向，必须是 'up' 或 'down'", nameof(direction));

        var currentColumn = await context.column_definition
            .FirstOrDefaultAsync(c => c.id == columnId && c.is_valid);

        if (currentColumn == null)
            throw new InvalidOperationException($"找不到要排序的有效列 (ID: {columnId})。");

        var tableId = currentColumn.table_id;

        // 获取所有有效的列并按排序索引排序
        var columns = await context.column_definition
            .Where(cd => cd.table_id == tableId && cd.is_valid)
            .OrderBy(cd => cd.sorted_index)
            .ToListAsync();

        if (!columns.Any())
            throw new InvalidOperationException($"表 (ID: {tableId}) 中没有找到有效的列。");

        // 找到当前列的索引
        var currentIndex = columns.FindIndex(cd => cd.id == columnId);
        if (currentIndex == -1)
        {
             // 这理论上不应该发生，因为上面已经找到了 currentColumn
            throw new InvalidOperationException($"在排序列表中未找到列 (ID: {columnId})。");
        }

        // 确定交换目标
        column_definition? swapTarget = null;
        int targetIndex = -1;

        if (direction == "up")
        {
            if (currentIndex == 0) throw new InvalidOperationException("列已经是第一个，无法上移。");
            targetIndex = currentIndex - 1;
            swapTarget = columns[targetIndex];
        }
        else // direction == "down"
        {
            if (currentIndex == columns.Count - 1) throw new InvalidOperationException("列已经是最后一个，无法下移。");
            targetIndex = currentIndex + 1;
            swapTarget = columns[targetIndex];
        }

        if (swapTarget == null)
        {
             // 这理论上也不应该发生
            throw new InvalidOperationException("未能确定用于交换的目标列。");
        }

        // --- 直接交换 sorted_index ---
        // (currentColumn.sorted_index, swapTarget.sorted_index) = (swapTarget.sorted_index, currentColumn.sorted_index);
        // 注意：上面的直接交换可能导致EF Core跟踪问题或不必要的更新。
        // 更好的方法是重新分配所有索引。

        // --- 重新分配所有列的索引以确保连续性 ---
        // 将要移动的列从列表中移除
        columns.RemoveAt(currentIndex);
        // 将其插入到新位置
        columns.Insert(targetIndex, currentColumn);

        // 重新分配连续的 sorted_index 值，从 1 开始
        for (int i = 0; i < columns.Count; i++)
        {
            // 只有当索引实际改变时才更新，以减少不必要的数据库更新
            if (columns[i].sorted_index != i + 1)
            {
                columns[i].sorted_index = i + 1;
            }
        }

        // 不需要调用 SaveChangesAsync
    }

    // --- Helper methods moved/adapted from component ---

    private async Task ValidateUniqueNameForUpdateAsync(CubeContext context, column_definition columnData)
    {
        var uniqueName = (columnData.unique_name ?? "").Trim().ToLower();
        var currentTableId = columnData.table_id;
        var currentColumnId = columnData.id;

        if (string.IsNullOrWhiteSpace(uniqueName))
            throw new ArgumentException("列的唯一编码不能为空");
        if (uniqueName.Length > 60)
            throw new ArgumentException("列的唯一编码不能超过60个字符");

        // 验证唯一编码是否与表名冲突
        var parentTable = await context.table_definition
            .AsNoTracking()
            .FirstOrDefaultAsync(t => t.id == currentTableId);
        if (parentTable == null)
            throw new InvalidOperationException($"列所属的表 (ID: {currentTableId}) 未找到。");
        if (string.Equals(uniqueName, parentTable.unique_name, StringComparison.OrdinalIgnoreCase))
            throw new ArgumentException($"列的唯一编码不能与表名 '{parentTable.unique_name}' 相同");

        // 验证唯一编码是否在表中已存在 (排除自身)
        var duplicateExists = await context.column_definition
            .AnyAsync(c => c.table_id == currentTableId &&
                          c.is_valid &&
                          c.id != currentColumnId && // 排除当前正在编辑的列
                          c.unique_name.ToLower() == uniqueName);
        if (duplicateExists)
            throw new ArgumentException($"列的唯一编码 '{uniqueName}' 在表 '{parentTable.display_name}' 中已存在");
    }

    private async Task HandleTableChangeForColumnAsync(CubeContext context, column_definition columnData)
    {
        // 当列所属的表发生变化时，将其排序索引设置为新表中的最大索引+1
        var sortedIndexes = await context.column_definition
            .Where(cd => cd.table_id == columnData.table_id && cd.is_valid)
            .Select(cd => cd.sorted_index)
            .ToListAsync();

        int maxSortedIndex = 0;
        if (sortedIndexes.Any())
        {
            maxSortedIndex = sortedIndexes.Max();
        }

        columnData.sorted_index = maxSortedIndex + 1;
        // 注意：这里只是修改了传入的 columnData 的 sorted_index，
        // UpdateColumnDefinitionAsync 中的 Mapper 会将这个新值映射到 columnToUpdate 实体上。
    }

    #endregion
}
