﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace DataAccess.Models;

public partial class sys_ward
{
    /// <summary>
    /// 病区ID
    /// </summary>
    public Guid id { get; set; }

    /// <summary>
    /// 医院ID
    /// </summary>
    public Guid hospital_id { get; set; }

    /// <summary>
    /// 院区ID
    /// </summary>
    public Guid region_id { get; set; }

    /// <summary>
    /// 科室ID
    /// </summary>
    public Guid department_id { get; set; }

    /// <summary>
    /// 病区名称
    /// </summary>
    public string name { get; set; }

    public virtual sys_department department { get; set; }

    public virtual ICollection<form_card> form_card { get; set; } = new List<form_card>();

    public virtual ICollection<form_form> form_form { get; set; } = new List<form_form>();

    public virtual ICollection<form_form_set> form_form_set { get; set; } = new List<form_form_set>();

    public virtual ICollection<form_project> form_project { get; set; } = new List<form_project>();

    public virtual ICollection<form_question> form_question { get; set; } = new List<form_question>();

    public virtual sys_hospital hospital { get; set; }

    public virtual sys_region region { get; set; }
}