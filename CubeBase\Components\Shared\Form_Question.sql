CREATE TABLE "Form"."Form_Question" (
  "Id" uuid NOT NULL,
  "CardId" uuid NOT NULL,
  "TableName" text COLLATE "pg_catalog"."default",
  "ColumnName" text COLLATE "pg_catalog"."default",
  "DataType" text COLLATE "pg_catalog"."default",
  "DisplayComponent" text COLLATE "pg_catalog"."default",
  "DisplayName" text COLLATE "pg_catalog"."default",
  "LabelText" text COLLATE "pg_catalog"."default",
  "PromptText" text COLLATE "pg_catalog"."default",  
  "PrefixText" text COLLATE "pg_catalog"."default",
  "SuffixText" text COLLATE "pg_catalog"."default",
  "DisplayStyle" text COLLATE "pg_catalog"."default",
  "DimensionText" text COLLATE "pg_catalog"."default",
  "PlaceholderWidthRatio" int2 NOT NULL DEFAULT 1,
  "SortedIndex" int4 NOT NULL DEFAULT 0,
  "IsRequired" bool NOT NULL DEFAULT false,
  "IsDisabled" bool NOT NULL DEFAULT false,
  "IsHidden" bool NOT NULL DEFAULT false,
  "TableDefinitionId" uuid NOT NULL,
  "ColumnDefinitionId" uuid NOT NULL,
  "DimensionId" uuid,
  "CreatedAt" timestamp(6) NOT NULL DEFAULT now(),
  "UpdatedAt" timestamp(6) NOT NULL DEFAULT now(),

  "Reserved1" text COLLATE "pg_catalog"."default",
  "Reserved2" text COLLATE "pg_catalog"."default",
  "Reserved3" text COLLATE "pg_catalog"."default",
  "Reserved4" text COLLATE "pg_catalog"."default",
  "Reserved5" text COLLATE "pg_catalog"."default",
  "Reserved6" text COLLATE "pg_catalog"."default",
  "Reserved7" text COLLATE "pg_catalog"."default",
  "Reserved8" text COLLATE "pg_catalog"."default",
  "Reserved9" text COLLATE "pg_catalog"."default",

  "Select_IsMultipleChoice" bool NOT NULL DEFAULT false,
  "Select_SortedOptionSubset" text[] COLLATE "pg_catalog"."default",
  "Select_DefaultOption" text COLLATE "pg_catalog"."default",
  "Select_Reserved4" text COLLATE "pg_catalog"."default",
  "Select_Reserved5" text COLLATE "pg_catalog"."default",
  "Select_Reserved6" text COLLATE "pg_catalog"."default",
  "Select_Reserved7" text COLLATE "pg_catalog"."default",
  "Select_Reserved8" text COLLATE "pg_catalog"."default",
  "Select_Reserved9" text COLLATE "pg_catalog"."default",

  "Text_IsMultipleLine" bool NOT NULL DEFAULT false,
  "Text_Width" int2,
  "Text_MaxLine" int4,
  "Text_MaxLength" int4,
  "Text_Reserved5" text COLLATE "pg_catalog"."default",
  "Text_Reserved6" text COLLATE "pg_catalog"."default",
  "Text_Reserved7" text COLLATE "pg_catalog"."default",
  "Text_Reserved8" text COLLATE "pg_catalog"."default",
  "Text_Reserved9" text COLLATE "pg_catalog"."default",

  "Number_IsInteger" bool NOT NULL DEFAULT false,
  "Number_DecimalPlaces" int2,
  "Number_Format" text COLLATE "pg_catalog"."default",
  "Number_Min" numeric,
  "Number_Max" numeric,
  "Number_Reserved6" text COLLATE "pg_catalog"."default",
  "Number_Reserved7" text COLLATE "pg_catalog"."default",
  "Number_Reserved8" text COLLATE "pg_catalog"."default",
  "Number_Reserved9" text COLLATE "pg_catalog"."default",

  "Date_Format" text COLLATE "pg_catalog"."default",
  "Date_Min" timestamp(6),
  "Date_Max" timestamp(6),
  "Date_Reserved4" text COLLATE "pg_catalog"."default",
  "Date_Reserved5" text COLLATE "pg_catalog"."default",
  "Date_Reserved6" text COLLATE "pg_catalog"."default",
  "Date_Reserved7" text COLLATE "pg_catalog"."default",
  "Date_Reserved8" text COLLATE "pg_catalog"."default",
  "Date_Reserved9" text COLLATE "pg_catalog"."default",
  CONSTRAINT "PK_Form_Question" PRIMARY KEY ("Id"),
  CONSTRAINT "FK_Form_Question_Card" FOREIGN KEY ("CardId") REFERENCES "Form"."Form_Card" ("Id") ON DELETE CASCADE ON UPDATE NO ACTION,
  CONSTRAINT "FK_Form_Question_ColumnDefinition" FOREIGN KEY ("ColumnDefinitionId") REFERENCES "public"."ColumnDefinition" ("Id") ON DELETE CASCADE ON UPDATE NO ACTION,
  CONSTRAINT "FK_Form_Question_TableDefinition" FOREIGN KEY ("TableDefinitionId") REFERENCES "public"."TableDefinition" ("Id") ON DELETE CASCADE ON UPDATE NO ACTION
)
;

ALTER TABLE "Form"."Form_Question" 
  OWNER TO "postgres";

CREATE INDEX "IX_Form_Question_CardId" ON "Form"."Form_Question" USING btree (
  "CardId" "pg_catalog"."uuid_ops" ASC NULLS LAST
);

CREATE INDEX "IX_Form_Question_ColumnDefinitionId" ON "Form"."Form_Question" USING btree (
  "ColumnDefinitionId" "pg_catalog"."uuid_ops" ASC NULLS LAST
);

CREATE INDEX "IX_Form_Question_TableDefinitionId" ON "Form"."Form_Question" USING btree (
  "TableDefinitionId" "pg_catalog"."uuid_ops" ASC NULLS LAST
);

COMMENT ON COLUMN "Form"."Form_Question"."Id" IS '题目ID';
COMMENT ON COLUMN "Form"."Form_Question"."CardId" IS '题卡ID';
COMMENT ON COLUMN "Form"."Form_Question"."TableName" IS '表名';
COMMENT ON COLUMN "Form"."Form_Question"."ColumnName" IS '列名';
COMMENT ON COLUMN "Form"."Form_Question"."DataType" IS '数据类型';
COMMENT ON COLUMN "Form"."Form_Question"."DisplayComponent" IS '显示组件';
COMMENT ON COLUMN "Form"."Form_Question"."DisplayName" IS '显示名称';
COMMENT ON COLUMN "Form"."Form_Question"."LabelText" IS '标注文字';
COMMENT ON COLUMN "Form"."Form_Question"."PromptText" IS '提示文字';
COMMENT ON COLUMN "Form"."Form_Question"."PrefixText" IS '前缀';
COMMENT ON COLUMN "Form"."Form_Question"."SuffixText" IS '后缀';
COMMENT ON COLUMN "Form"."Form_Question"."DisplayStyle" IS '显示样式';
COMMENT ON COLUMN "Form"."Form_Question"."DimensionText" IS '维度文字';
COMMENT ON COLUMN "Form"."Form_Question"."PlaceholderWidthRatio" IS '输入框宽度比例';
COMMENT ON COLUMN "Form"."Form_Question"."SortedIndex" IS '排序索引';
COMMENT ON COLUMN "Form"."Form_Question"."IsRequired" IS '是否必填';
COMMENT ON COLUMN "Form"."Form_Question"."IsDisabled" IS '是否禁用';
COMMENT ON COLUMN "Form"."Form_Question"."IsHidden" IS '是否隐藏';
COMMENT ON COLUMN "Form"."Form_Question"."TableDefinitionId" IS '表定义ID';
COMMENT ON COLUMN "Form"."Form_Question"."ColumnDefinitionId" IS '列定义ID';
COMMENT ON COLUMN "Form"."Form_Question"."DimensionId" IS '量纲ID';
COMMENT ON COLUMN "Form"."Form_Question"."CreatedAt" IS '创建时间';
COMMENT ON COLUMN "Form"."Form_Question"."UpdatedAt" IS '更新时间';

COMMENT ON COLUMN "Form"."Form_Question"."Reserved1" IS '保留字段1';
COMMENT ON COLUMN "Form"."Form_Question"."Reserved2" IS '保留字段2';
COMMENT ON COLUMN "Form"."Form_Question"."Reserved3" IS '保留字段3';
COMMENT ON COLUMN "Form"."Form_Question"."Reserved4" IS '保留字段4';
COMMENT ON COLUMN "Form"."Form_Question"."Reserved5" IS '保留字段5';
COMMENT ON COLUMN "Form"."Form_Question"."Reserved6" IS '保留字段6';
COMMENT ON COLUMN "Form"."Form_Question"."Reserved7" IS '保留字段7';
COMMENT ON COLUMN "Form"."Form_Question"."Reserved8" IS '保留字段8';
COMMENT ON COLUMN "Form"."Form_Question"."Reserved9" IS '保留字段9';

COMMENT ON COLUMN "Form"."Form_Question"."Select_IsMultipleChoice" IS '是否多选';
COMMENT ON COLUMN "Form"."Form_Question"."Select_SortedOptionSubset" IS '排序选项子集';
COMMENT ON COLUMN "Form"."Form_Question"."Select_DefaultOption" IS '默认选中项';
COMMENT ON COLUMN "Form"."Form_Question"."Select_Reserved4" IS '选择题保留字段4';
COMMENT ON COLUMN "Form"."Form_Question"."Select_Reserved5" IS '选择题保留字段5';
COMMENT ON COLUMN "Form"."Form_Question"."Select_Reserved6" IS '选择题保留字段6';
COMMENT ON COLUMN "Form"."Form_Question"."Select_Reserved7" IS '保留字段7';
COMMENT ON COLUMN "Form"."Form_Question"."Select_Reserved8" IS '选择题保留字段8';
COMMENT ON COLUMN "Form"."Form_Question"."Select_Reserved9" IS '选择题保留字段9';

COMMENT ON COLUMN "Form"."Form_Question"."Text_IsMultipleLine" IS '是否多行';
COMMENT ON COLUMN "Form"."Form_Question"."Text_Width" IS '宽度';
COMMENT ON COLUMN "Form"."Form_Question"."Text_MaxLine" IS '最大行数';
COMMENT ON COLUMN "Form"."Form_Question"."Text_MaxLength" IS '文本最大长度';
COMMENT ON COLUMN "Form"."Form_Question"."Text_Reserved5" IS '文本保留字段5';
COMMENT ON COLUMN "Form"."Form_Question"."Text_Reserved6" IS '文本保留字段6';
COMMENT ON COLUMN "Form"."Form_Question"."Text_Reserved7" IS '文本保留字段7';
COMMENT ON COLUMN "Form"."Form_Question"."Text_Reserved8" IS '文本保留字段8';
COMMENT ON COLUMN "Form"."Form_Question"."Text_Reserved9" IS '文本保留字段9';

COMMENT ON COLUMN "Form"."Form_Question"."Number_IsInteger" IS '是否整数';
COMMENT ON COLUMN "Form"."Form_Question"."Number_DecimalPlaces" IS '小数位数';
COMMENT ON COLUMN "Form"."Form_Question"."Number_Format" IS '格式';
COMMENT ON COLUMN "Form"."Form_Question"."Number_Min" IS '最小值';
COMMENT ON COLUMN "Form"."Form_Question"."Number_Max" IS '最大值';
COMMENT ON COLUMN "Form"."Form_Question"."Number_Reserved6" IS '数字保留字段6';
COMMENT ON COLUMN "Form"."Form_Question"."Number_Reserved7" IS '数字保留字段7';
COMMENT ON COLUMN "Form"."Form_Question"."Number_Reserved8" IS '数字保留字段8';
COMMENT ON COLUMN "Form"."Form_Question"."Number_Reserved9" IS '数字保留字段9';

COMMENT ON COLUMN "Form"."Form_Question"."Date_Format" IS '日期格式';
COMMENT ON COLUMN "Form"."Form_Question"."Date_Min" IS '最小日期';
COMMENT ON COLUMN "Form"."Form_Question"."Date_Max" IS '最大日期';
COMMENT ON COLUMN "Form"."Form_Question"."Date_Reserved4" IS '日期保留字段4';
COMMENT ON COLUMN "Form"."Form_Question"."Date_Reserved5" IS '日期保留字段5';
COMMENT ON COLUMN "Form"."Form_Question"."Date_Reserved6" IS '日期保留字段6';
COMMENT ON COLUMN "Form"."Form_Question"."Date_Reserved7" IS '日期保留字段7';
COMMENT ON COLUMN "Form"."Form_Question"."Date_Reserved8" IS '日期保留字段8';
COMMENT ON COLUMN "Form"."Form_Question"."Date_Reserved9" IS '日期保留字段9';