// 文件下载辅助函数 v2
window.fileDownloadInterop = {
    // 从内容创建并下载文件的函数
    downloadFromContent: function (content, filename, contentType) {
        try {
            // 创建Blob对象
            const blob = new Blob([content], { type: contentType || 'application/json' });
            
            // 尝试使用showSaveFilePicker API (如果支持)
            if (window.showSaveFilePicker) {
                console.log("尝试使用现代文件系统API");
                this.downloadUsingFilePicker(blob, filename, contentType);
                return true;
            }
            
            // 使用FileSaver API (如果支持)
            if (window.navigator.msSaveOrOpenBlob) {
                // For IE/Edge
                window.navigator.msSaveOrOpenBlob(blob, filename);
                console.log("使用IE/Edge特定的保存方法");
                return true;
            }

            // 创建临时URL
            const url = window.URL.createObjectURL(blob);
            
            // 创建临时下载链接
            const link = document.createElement('a');
            link.href = url;
            link.setAttribute('download', filename);
            
            // 添加到文档并触发点击
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            
            // 延迟释放URL
            setTimeout(function() {
                window.URL.revokeObjectURL(url);
            }, 100);
            
            console.log("文件下载请求已发送");
            return true;
        } catch (e) {
            console.error("文件下载失败:", e);
            return false;
        }
    },
    
    // 使用现代文件系统API下载 (Chrome 86+, Edge 86+)
    downloadUsingFilePicker: async function(blob, filename, contentType) {
        try {
            const options = {
                suggestedName: filename,
                types: [{
                    description: '导出文件',
                    accept: { [contentType || 'application/json']: ['.json'] }
                }]
            };
            
            const handle = await window.showSaveFilePicker(options);
            const writable = await handle.createWritable();
            await writable.write(blob);
            await writable.close();
            console.log("使用文件系统API保存成功");
            return true;
        } catch (e) {
            console.error("文件系统API保存失败, 回退到传统方法:", e);
            return false;
        }
    },
    
    // 提示用户输入文件名的下载方法
    downloadWithPrompt: function (content, defaultFilename, contentType) {
        // 提示用户输入文件名
        const userFilename = prompt("请输入保存的文件名:", defaultFilename);
        
        // 如果用户取消或输入空文件名，则使用默认文件名
        const filename = userFilename || defaultFilename;
        
        // 调用下载函数
        return this.downloadFromContent(content, filename, contentType);
    }
}; 