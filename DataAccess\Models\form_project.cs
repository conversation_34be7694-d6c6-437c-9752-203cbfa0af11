﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace DataAccess.Models;

/// <summary>
/// 项目
/// </summary>
public partial class form_project
{
    /// <summary>
    /// 项目ID
    /// </summary>
    public Guid id { get; set; }

    /// <summary>
    /// 项目名称
    /// </summary>
    public string name { get; set; }

    /// <summary>
    /// 项目描述
    /// </summary>
    public string display_name { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime created_at { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime updated_at { get; set; }

    /// <summary>
    /// 所属病区ID
    /// </summary>
    public Guid ward_id { get; set; }

    /// <summary>
    /// 所属病区名称
    /// </summary>
    public string ward_name { get; set; }

    /// <summary>
    /// 所属科室ID
    /// </summary>
    public Guid department_id { get; set; }

    /// <summary>
    /// 所属科室名称
    /// </summary>
    public string department_name { get; set; }

    /// <summary>
    /// 所属医院ID
    /// </summary>
    public Guid hospital_id { get; set; }

    /// <summary>
    /// 所属医院名称
    /// </summary>
    public string hospital_name { get; set; }

    /// <summary>
    /// 扫码消息
    /// </summary>
    public string scan_code_msg { get; set; }

    public virtual sys_department department { get; set; }

    public virtual ICollection<form_card> form_card { get; set; } = new List<form_card>();

    public virtual ICollection<form_form> form_form { get; set; } = new List<form_form>();

    public virtual ICollection<form_form_set> form_form_set { get; set; } = new List<form_form_set>();

    public virtual ICollection<form_question> form_question { get; set; } = new List<form_question>();

    public virtual sys_hospital hospital { get; set; }

    public virtual ICollection<patient> patient { get; set; } = new List<patient>();

    public virtual ICollection<sys_license> sys_license { get; set; } = new List<sys_license>();

    public virtual sys_ward ward { get; set; }
}