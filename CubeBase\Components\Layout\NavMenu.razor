﻿@* <div class="top-row ps-3 navbar navbar-dark"> *@
@*     <div class="container-fluid"> *@
@*         <a class="navbar-brand" href="">CubeBase</a> *@
@*     </div> *@
@* </div> *@

<input type="checkbox" title="Navigation menu" class="navbar-toggler" />

<div class="nav-scrollable" onclick="document.querySelector('.navbar-toggler').click()">
    <nav class="flex-column">
        <div class="nav-item px-3">
            <NavLink class="nav-link" href="home" Match="NavLinkMatch.All">
                <span class="bi bi-house-door-fill-nav-menu" aria-hidden="true"></span> Home
            </NavLink>
        </div>

        <!--div class="nav-item px-3">
            <NavLink class="nav-link" href="Cube">
                <span class="bi bi-list-nested-nav-menu" aria-hidden="true"></span> 医学定义
            </NavLink>
        </!div-->

        <div class="nav-item px-3">
            <NavLink class="nav-link" href="DataDictManager">
                <span class="bi bi-list-nested-nav-menu" aria-hidden="true"></span> 医学字典管理
            </NavLink>
        </div>

        <div class="nav-item px-3">
            <NavLink class="nav-link" href="UserManager">
                <span class="bi bi-list-nested-nav-menu" aria-hidden="true"></span> 用户/权限
            </NavLink>
        </div>

        <div class="nav-item px-3">
            <NavLink class="nav-link" href="OrgManager">
                <span class="bi bi-list-nested-nav-menu" aria-hidden="true"></span> 医院/病区
            </NavLink>
        </div>

        <div class="nav-item px-3">
            <NavLink class="nav-link" href="ProjectManager">
                <span class="bi bi-list-nested-nav-menu" aria-hidden="true"></span> 项目管理
            </NavLink>
        </div>

        <div class="nav-item px-3">
            <NavLink class="nav-link" href="FormsetManager">
                <span class="bi bi-list-nested-nav-menu" aria-hidden="true"></span> 表单集
            </NavLink>
        </div>

        <!--div class="nav-item px-3">
            <NavLink class="nav-link" href="card-mover">
                <span class="bi bi-list-nested-nav-menu" aria-hidden="true"></span> 卡片跨表单移动
            </NavLink>
        </!--div-->

        <div class="nav-item px-3">
            <NavLink class="nav-link" href="LinkerRules">
                <span class="bi bi-list-nested-nav-menu" aria-hidden="true"></span> 配置前置定义
            </NavLink>
        </div>

        <!--div class="nav-item px-3">
            <NavLink class="nav-link" href="Test">
                <span class="bi bi-list-nested-nav-menu" aria-hidden="true"></span> AI调用测试
            </NavLink>
        </!--div-->
    </nav>
</div>