
.llm-match {
    background-color: #e7f5e7;
    padding: 4px 8px;
    border-radius: 4px;
    border-left: 3px solid #4caf50;
    cursor: help;
    margin-bottom: 4px;
}

.llm-potential-match {
    background-color: #fff8e1;
    padding: 4px 8px;
    border-radius: 4px;
    border-left: 3px solid #ff9800;
    cursor: help;
    margin-bottom: 4px;
}

.llm-no-match {
    background-color: #f5f5f5;
    padding: 4px 8px;
    border-radius: 4px;
    border-left: 3px solid #9e9e9e;
    cursor: help;
    margin-bottom: 4px;
}

.llm-error {
    background-color: #ffebee;
    padding: 4px 8px;
    border-radius: 4px;
    border-left: 3px solid #f44336;
    cursor: help;
    margin-bottom: 4px;
}

.llm-pending {
    background-color: #e3f2fd;
    padding: 4px 8px;
    border-radius: 4px;
    border-left: 3px solid #2196f3;
    margin-bottom: 4px;
    font-style: italic;
}

.match-text, .potential-match-text, .no-match-text, .error-text {
    font-weight: 500;
    display: block;
}

.match-type {
    font-size: 0.85em;
    opacity: 0.8;
    display: block;
}

.category-data {
    max-height: 300px;
    overflow: auto;
    background-color: #f5f5f5;
    padding: 8px;
    font-size: 0.85em;
    border-radius: 4px;
    white-space: pre-wrap;
    max-width: 600px;
}

/* 修复MudPopover的显示问题 */
.popover {
    z-index: 1500;
    padding: 12px;
    max-width: 650px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.2);
}