using System;
using System.Collections.Generic;
using System.Threading.Tasks;

using DataAccess.Models;
using Microsoft.Extensions.Logging;

namespace BusinessLogic.Cube;

/// <summary>
/// 医学数据字典服务接口
/// </summary>
public interface IMedDictService
{
    /// <summary>
    /// 导出医学数据字典为文本格式
    /// </summary>
    /// <param name="context">数据库上下文</param>
    /// <returns>包含医学数据字典的文本字符串</returns>
    Task<string> ExportMedicalDataDictionaryAsync(CubeContext context);

    /// <summary>
    /// 导出所有有效节点（Table和Visual）的数据字典为文本格式，不包含列信息
    /// </summary>
    /// <param name="context">数据库上下文</param>
    /// <returns>包含所有节点数据字典的文本字符串</returns>
    Task<string> ExportAllNodesDataDictionaryAsync(CubeContext context);

    /// <summary>
    /// 从指定节点开始导出子树数据字典为文本格式
    /// </summary>
    /// <param name="context">数据库上下文</param>
    /// <param name="startNodeId">起始节点的ID</param>
    /// <returns>包含指定子树数据字典的文本字符串</returns>
    Task<string> ExportSubtreeDataDictionaryAsync(CubeContext context, Guid startNodeId);

    /// <summary>
    /// 获取指定节点的从根节点开始的节点路径
    /// </summary>
    /// <param name="context">数据库上下文</param>
    /// <param name="nodeId">起始节点的ID</param>
    /// <returns>包含从根节点开始的节点路径的字符串 L0 > L1 > L2 > ... > Ln</returns>
    Task<string> GetColumnNodePathAsync(CubeContext context, Guid nodeId);

    /// <summary>
    /// 获取所有有效的 table_definition 数据，使用全局缓存
    /// </summary>
    /// <param name="context">数据库上下文</param>
    /// <returns>所有有效的 table_definition 字典</returns>
    Task<Dictionary<Guid, table_definition>> GetAllTableDefinitionsAsync(CubeContext context);

    /// <summary>
    /// 清除所有缓存（全局数据缓存和路径缓存）
    /// 当 table_definition 数据发生变化时应调用此方法
    /// </summary>
    void ClearAllCaches();

    /// <summary>
    /// 添加一个新的列定义到指定的表中（但不保存更改）
    /// </summary>
    /// <param name="context">数据库上下文</param>
    /// <param name="newColumnData">包含新列信息的对象</param>
    /// <returns>已添加到上下文的新创建的列定义实体</returns>
    /// <exception cref="ArgumentException">当输入验证失败时抛出</exception>
    /// <exception cref="InvalidOperationException">当无法找到有效的父表时抛出</exception>
    Task<column_definition> AddColumnDefinitionAsync(CubeContext context, column_definition newColumnData);

    /// <summary>
    /// 更新指定的列定义（但不保存更改）
    /// </summary>
    /// <param name="context">数据库上下文</param>
    /// <param name="columnData">包含更新列信息的对象</param>
    Task UpdateColumnDefinitionAsync(CubeContext context, column_definition columnData);

    /// <summary>
    /// 删除指定的列定义（但不保存更改）
    /// </summary>
    /// <param name="context">数据库上下文</param>
    /// <param name="columnId">要删除的列的ID</param>
    Task DeleteColumnDefinitionAsync(CubeContext context, Guid columnId);

    /// <summary>
    /// 改变指定列的排序顺序（但不保存更改）
    /// </summary>
    /// <param name="context">数据库上下文</param>
    /// <param name="columnId">要改变排序顺序的列的ID</param>
    /// <param name="direction">改变的方向（"up" 或 "down"）</param>
    Task ChangeColumnSortOrderAsync(CubeContext context, Guid columnId, string direction);

    /// <summary>
    /// 添加一个新的表定义到指定的节点中（但不保存更改）
    /// </summary>
    /// <param name="context">数据库上下文</param>
    /// <param name="newTableData">包含新表信息的对象</param>
    /// <returns>已添加到上下文的新创建的表定义实体</returns>
    /// <exception cref="ArgumentException">当输入验证失败时抛出</exception>
    /// <exception cref="InvalidOperationException">当无法找到有效的父表时抛出</exception>
    Task<table_definition> AddTableDefinitionAsync(CubeContext context, table_definition newTableData);

    /// <summary>
    /// 更新指定的表定义（但不保存更改）
    /// </summary>
    /// <param name="context">数据库上下文</param>
    /// <param name="tableData">包含更新表信息的对象</param>
    Task UpdateTableDefinitionAsync(CubeContext context, table_definition tableData);

    /// <summary>
    /// 删除指定的表定义（但不保存更改）
    /// </summary>
    /// <param name="context">数据库上下文</param>
    /// <param name="tableId">要删除的表的ID</param>
    Task DeleteTableDefinitionAsync(CubeContext context, Guid tableId);

    /// <summary>
    /// 改变指定表的排序顺序（但不保存更改）
    /// </summary>
    /// <param name="context">数据库上下文</param>
    /// <param name="tableId">要改变排序顺序的表的ID</param>
    /// <param name="direction">改变的方向（"up" 或 "down"）</param>
    Task ChangeTableSortOrderAsync(CubeContext context, Guid tableId, string direction);

    /// <summary>
    /// 获取用于构建树的所有有效 table_definition 及其有效的 column_definition。
    /// </summary>
    /// <param name="context">数据库上下文</param>
    /// <returns>包含有效表和列的列表</returns>
    Task<List<table_definition>> GetValidTableTreeAsync(CubeContext context);

    /// <summary>
    /// 获取指定节点的所有后代节点的 ID 集合 (不包含自身)。
    /// </summary>
    /// <param name="context">数据库上下文</param>
    /// <param name="nodeId">起始节点的 ID</param>
    /// <returns>包含所有后代节点 ID 的 HashSet</returns>
    Task<HashSet<Guid>> GetAllDescendantIdsAsync(CubeContext context, Guid nodeId);

    /// <summary>
    /// 分析列定义应该归属于哪个分类
    /// </summary>
    /// <param name="context">数据库上下文</param>
    /// <param name="columnDefinition">列定义实例</param>
    /// <param name="logger">日志记录器</param>
    /// <param name="onPartialResponse">流式输出回调函数，用于接收部分响应</param>
    /// <returns>分析结果</returns>
    Task<string> BelongToAnalysisAsync(CubeContext context, column_definition columnDefinition, ILogger logger, Action<string>? onPartialResponse = null);
}