﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace DataAccess.Models;

/// <summary>
/// 角色数据权限表
/// </summary>
public partial class sys_permission_data
{
    public Guid id { get; set; }

    /// <summary>
    /// 角色ID
    /// </summary>
    public Guid role_id { get; set; }

    /// <summary>
    /// 医院ID
    /// </summary>
    public Guid hospital_id { get; set; }

    /// <summary>
    /// 医院名称
    /// </summary>
    public string hospital_name { get; set; }

    /// <summary>
    /// 科室ID
    /// </summary>
    public Guid? department_id { get; set; }

    /// <summary>
    /// 科室名称
    /// </summary>
    public string department_name { get; set; }

    /// <summary>
    /// 区域ID
    /// </summary>
    public Guid? region_id { get; set; }

    /// <summary>
    /// 区域名称
    /// </summary>
    public string region_name { get; set; }

    /// <summary>
    /// 病区ID
    /// </summary>
    public Guid? ward_id { get; set; }

    /// <summary>
    /// 病区名称
    /// </summary>
    public string ward_name { get; set; }

    /// <summary>
    /// 项目ID
    /// </summary>
    public Guid? form_project_id { get; set; }

    /// <summary>
    /// 项目名称
    /// </summary>
    public string form_project_name { get; set; }

    /// <summary>
    /// 表单集ID
    /// </summary>
    public Guid? form_formset_id { get; set; }

    /// <summary>
    /// 表单集名称
    /// </summary>
    public string form_formset_name { get; set; }

    /// <summary>
    /// 表单ID
    /// </summary>
    public Guid? form_form_id { get; set; }

    /// <summary>
    /// 表单名称
    /// </summary>
    public string form_form_name { get; set; }

    /// <summary>
    /// 表单卡ID
    /// </summary>
    public Guid? form_card_id { get; set; }

    /// <summary>
    /// 表单卡名称
    /// </summary>
    public string form_card_name { get; set; }

    public virtual sys_hospital hospital { get; set; }

    public virtual sys_role role { get; set; }
}