﻿@inherits Cmp_Base
@inject ILogger<Cmp_RecursiveCards> inj_logger
@using System.Diagnostics

<style>
    .card {
    --bs-card-spacer-y: 1 !important;
    --bs-card-spacer-x: 1 !important;
    }
    .card-header {
        /* background-color: #05966940 !important; */
        padding:0;
    }
    .card-body {
        padding:8px!important;
    }

    .recursi-card-select .dropdown-menu {
    overflow-x: auto !important;
    width: 350px !important;
    }
    .card.card-active{
        border:var(--bs-card-border-width) solid #059669 !important;
    }

    .form-label{
        margin-bottom:0;
    }
</style>

<MudItem xl="12">
    <Card Class="@($"ma-0 pa-0 card-{Card.id} {(_isFocusCard ? "card-active" : "")}")">
        <HeaderTemplate>
            <div class="@($"card-header-{Card.id}")" 
            style="width: 100%; height: 100%; background-color: @(_isFocusCard ? "#059669" : "#0df08740"); padding:10px; border-top-left-radius: 5px;"
            @onclick="@(() => ClickCardHeader(Card))">
                <div style="display: flex; align-items: center; justify-content: space-between;">
                    <div style="display: flex; align-items: center;font-family: 'Courier New', monospace;">
                        <h6 style="margin: 0; margin-right: 8px;font-weight: bold;">1. </h6>
                        <input type="text" @bind-value="@Card.name" width="240px" @onblur="HandleCardNameChanged"
                        style="height: 36px; border: 1px solid #666; border-radius: 4px; padding: 0 8px;" />
                        <Checkbox TValue="string" OnStateChanged="@OnStateChanged" DisplayText="复选题"
                                ShowLabel="true" Color="BColor.Success" State="@_checkboxState" IsDisabled="@(Card.Inverseparent.Any())"/>
                        <Button IsOutline="true" Color="BColor.Primary" Size="BSize.ExtraSmall" class="ml-1"
                                OnClick="@(async () => {
                                    await inj_cardService.MoveCardUpAsync(_context, Card);
                                    await OnRefreshThis();
                                })">↑</Button>
                        <Button IsOutline="true" Color="BColor.Primary" Size="BSize.ExtraSmall" class="ml-1"
                                OnClick="@(async () => {
                                    await inj_cardService.MoveCardDownAsync(_context, Card);
                                    await OnRefreshThis();
                                })">↓</Button>
                        <Button Color="BColor.Primary" Size="BSize.ExtraSmall" class="ml-1" @onclick="ToggleCollapse">
                                @(_isCollapsible ? "收起" : "展开")
                        </Button>
                        <Select TValue="string" Color="BColor.Success" Items="@_thisFormOtherCards" OnSelectedItemChanged="OnSelectItemChanged" class="recursi-card-select ml-1" />
                        <div class="d-flex ml-2 align-items-center">
                            <span style="width:105px;font-size:12px">是否隐藏：</span>
                            <Switch OnColor="BColor.Primary" ShowInnerText="true" OnInnerText="隐" OffInnerText="显" @bind-Value="@Card.is_hidden" OnValueChanged="@OnParameterChanged"/>
                        </div>
                       
                    </div>
                    <div style="display: flex; align-items: center;">
                        @if (Card.type != "multiple")
                        {
                        <Button Color="BColor.Primary" Size="BSize.ExtraSmall"
                        class="mr-1" OnClick="@(() => AddSubCard())">添加子卡片</Button>
                        }
                        <Button Color="BColor.Warning" Size="BSize.ExtraSmall"
                        class="mr-2" OnClick="@(() => DeleteThisCard())">删除卡片</Button>
                        <Button Color="BColor.Info" Size="BSize.ExtraSmall"
                        class="mr-2" OnClick="@(() => ExportThisCard())" Disabled="@_isExporting">
                            @if (_isExporting)
                            {
                                <span class="spinner-border spinner-border-sm mr-1" role="status" aria-hidden="true"></span>
                                <span>导出中...</span>
                            }
                            else
                            {
                                <span>导出卡片</span>
                            }
                        </Button>
                        <Button Color="BColor.Danger" Size="BSize.ExtraSmall"
                        class="mr-2" OnClick="@(() => ImportCard())" Disabled="@_isImporting">
                            @if (_isImporting)
                            {
                                <span class="spinner-border spinner-border-sm mr-1" role="status" aria-hidden="true"></span>
                                <span>导入中...</span>
                            }
                            else
                            {
                                <span>导入卡片</span>
                            }
                        </Button>
                    </div>
                </div>
                <span class="ml-2" style="width:190px">ID: @Card.id.ToString()</span>
            </div>
        </HeaderTemplate>
        <BodyTemplate>
            <div style="display: @(_isCollapsible ? "block" : "none")">
                @if (_orderedGroups != null)
                {
                    @foreach (var group in _orderedGroups)
                    {
                        @if (group.Type == "Question")
                        {
                            <MudGrid Spacing="1">
                                @foreach (var question in group.Items.Cast<form_question>())
                                {
                                    <Cmp_Question @key="@question.id" _context="@_context" Question="@question" OnParentRefresh="@OnRefreshThis" />
                                }
                            </MudGrid>
                        }
                        else
                        {
                            <MudGrid Spacing="1">
                                @foreach (var card in group.Items.Cast<form_card>())
                                {
                                    <Cmp_RecursiveCards @key="@card.id" _context="@_context" Card="@card" OnParentRefresh="@OnRefreshThis" />
                                }
                            </MudGrid>
                        }
                    }
                }
            </div>
        </BodyTemplate>
    </Card>
</MudItem>

<!-- 隐藏的文件上传控件 -->
<InputFile id="@($"fileUpload-{Card.id}")" OnChange="HandleFileSelected" style="display:none;" accept=".json" />

@code {
    [Parameter]
    public required CubeContext _context { get; set; }
    [Parameter]
    public required form_card Card { get; set; }
    [Parameter]
    public EventCallback OnParentRefresh { get; set; }  // 给父组件的刷新回调事件

    private List<form_card> _subCards = [];
    private List<form_question> _questions = [];
    private Guid _SubscribeToken;
    private Guid _focusToken;
    private bool _isFocusCard = false;
    private bool _isCollapsible = true;
    private CheckboxState _checkboxState {
        get {
            return Card.type == "multiple" ? CheckboxState.Checked : CheckboxState.UnChecked;
        }
    }

    private IEnumerable<SelectedItem> _thisFormOtherCards
    {
        get
        {
            var list = new List<SelectedItem>();
            list.Add(new SelectedItem("", "请选择要移动到的父卡片"));
            list.Add(new SelectedItem("0", "↑ 移动到根级"));

            // 获取所有可选的卡片（排除自己和子卡片）
            var excludeIds = new HashSet<Guid>();
            CollectChildCardIds(Card, excludeIds);
            if(Card.parent_id != null)
            {
                excludeIds.Add(Card.parent_id.Value);
            }

            // 按照链表顺序构建卡片树
            var rootCards = GetOrderedCards(Card.form.form_card.Where(c => c.parent_id == null));
            foreach (var rootCard in rootCards)
            {
                if (!excludeIds.Contains(rootCard.id))
                {
                    // 添加根级卡片
                    var indent = GetCardIndent(rootCard, IsLastChild);
                    list.Add(new SelectedItem(rootCard.id.ToString(), $"{indent}{rootCard.name}"));
                    
                    // 递归添加子卡片
                    AddChildCards(rootCard, list, excludeIds);
                }
            }
            
            return list;
        }
    }
    private List<(string Type, List<object> Items)> _orderedGroups;
    private Guid _refreshToken;
    private bool _isExporting = false;
    private bool _isImporting = false;

    protected override async Task OnPageInitializedAsync()
    {
        var stopwatch = Stopwatch.StartNew();
        inj_logger.LogWarning("[性能调试] Cmp_RecursiveCards.OnPageInitializedAsync 开始 - CardID: {CardID}, CardName: {CardName}", Card.id, Card.name);
        
        await LoadDataAsync();
        
        stopwatch.Stop();
        inj_logger.LogWarning("[性能调试] Cmp_RecursiveCards.OnPageInitializedAsync 完成 - 耗时: {ElapsedMs}ms", stopwatch.ElapsedMilliseconds);
    }

    protected override void OnAfterRender(bool firstRender)
    {
        if (firstRender)
        {
            if(_SubscribeToken == Guid.Empty)
            {
                _SubscribeToken = inj_messageHub.Subscribe<column_definition>(HandleColumnInsertEvent);
            }
            if(_focusToken == Guid.Empty)
            {
                _focusToken = inj_messageHub.Subscribe<Guid>(HandleFocusEvent);
            }
            if(_refreshToken == Guid.Empty)
            {
                _refreshToken = inj_messageHub.Subscribe<RefreshCardMessage>(HandleRefreshMessage);
            }
        }
    }

    private async Task LoadDataAsync()
    {
        var stopwatch = Stopwatch.StartNew();
        inj_logger.LogWarning("[性能调试] LoadDataAsync 开始 - CardID: {CardID}, CardName: {CardName}", Card.id, Card.name);
        
        var sw1 = Stopwatch.StartNew();
        _subCards = GetOrderedCards(Card.Inverseparent).ToList();
        sw1.Stop();
        inj_logger.LogWarning("[性能调试] GetOrderedCards 耗时: {ElapsedMs}ms, 子卡片数: {SubCardCount}", sw1.ElapsedMilliseconds, _subCards.Count);
        
        var sw2 = Stopwatch.StartNew();
        _questions = GetOrderedQuestions(Card.form_question).ToList();
        sw2.Stop();
        inj_logger.LogWarning("[性能调试] GetOrderedQuestions 耗时: {ElapsedMs}ms, 问题数: {QuestionCount}", sw2.ElapsedMilliseconds, _questions.Count);

        // 预处理排序分组
        var sw3 = Stopwatch.StartNew();
        var sortedItems = new List<(int OrderIndex, string Type, object Item)>();
        var currentIndex = 0;
        
        // 使用链表顺序构建排序列表
        var currentItem = GetFirstItem();
        while (currentItem != null)
        {
            if (currentItem is form_card card)
            {
                sortedItems.Add((currentIndex++, "Card", card));
                currentItem = GetNextItem(card);
            }
            else if (currentItem is form_question question)
            {
                sortedItems.Add((currentIndex++, "Question", question));
                currentItem = GetNextItem(question);
            }
        }

        // 将连续的相同类型项目分组
        _orderedGroups = new List<(string Type, List<object> Items)>();
        string currentType = null;
        List<object> currentGroup = null;

        foreach (var item in sortedItems)
        {
            if (currentType != item.Type)
            {
                if (currentGroup != null)
                {
                    _orderedGroups.Add((currentType, currentGroup));
                }
                currentType = item.Type;
                currentGroup = new List<object>();
            }
            currentGroup?.Add(item.Item);
        }

        if (currentGroup?.Count > 0)
        {
            _orderedGroups.Add((currentType, currentGroup));
        }
        
        sw3.Stop();
        inj_logger.LogWarning("[性能调试] 排序分组处理 耗时: {ElapsedMs}ms, 分组数: {GroupCount}", sw3.ElapsedMilliseconds, _orderedGroups.Count);
        
        stopwatch.Stop();
        inj_logger.LogWarning("[性能调试] LoadDataAsync 完成 - 总耗时: {ElapsedMs}ms", stopwatch.ElapsedMilliseconds);
    }

    private object GetFirstItem()
    {
        var firstCard = Card.Inverseparent.FirstOrDefault(c => c.pre_uid == null);
        var firstQuestion = Card.form_question.FirstOrDefault(q => q.pre_uid == null);
        
        if (firstCard == null && firstQuestion == null) return null;
        if (firstCard == null) return firstQuestion;
        if (firstQuestion == null) return firstCard;
        
        // 如果都存在，返回Preuid为null的那个
        return firstCard;
    }

    private object GetNextItem(object currentItem)
    {
        if (currentItem is form_card card)
        {
            if (card.next_uid == null) return null;

            // 先尝试查找下一个卡片
            var nextCard = Card.Inverseparent.FirstOrDefault(c => c.id == card.next_uid);
            if (nextCard != null) return nextCard;
            
            // 如果没有找到卡片，查找问题
            return Card.form_question.FirstOrDefault(q => q.id == card.next_uid);
        }
        else if (currentItem is form_question question)
        {
            if (question.next_uid == null) return null;
            
            // 尝试查找下一个卡片
            var nextCard = Card.Inverseparent.FirstOrDefault(c => c.id == question.next_uid);
            if (nextCard != null) return nextCard;
            
            // 如果没有找到卡片，查找问题
            return Card.form_question.FirstOrDefault(q => q.id == question.next_uid);
        }
        
        return null;
    }

    private IEnumerable<form_question> GetOrderedQuestions(IEnumerable<form_question> questions)
    {
        var orderedQuestions = new List<form_question>();
        var firstQuestion = questions.FirstOrDefault(q => q.pre_uid == null);
        
        if (firstQuestion == null) return orderedQuestions;
        
        orderedQuestions.Add(firstQuestion);
        var currentQuestion = firstQuestion;
        
        while (currentQuestion.next_uid != null)
        {
            var nextQuestion = questions.FirstOrDefault(q => q.id == currentQuestion.next_uid);
            if (nextQuestion == null) break;
            
            orderedQuestions.Add(nextQuestion);
            currentQuestion = nextQuestion;
        }
        
        return orderedQuestions;
    }

    public async Task OnRefreshThis()
    {
        var stopwatch = Stopwatch.StartNew();
        inj_logger.LogWarning("[性能调试] OnRefreshThis 开始 - CardID: {CardID}, CardName: {CardName}", Card.id, Card.name);
        
        var sw1 = Stopwatch.StartNew();
        await LoadDataAsync();
        sw1.Stop();
        inj_logger.LogWarning("[性能调试] OnRefreshThis-LoadDataAsync 耗时: {ElapsedMs}ms", sw1.ElapsedMilliseconds);
        
        // 数据加载完成后需要更新UI
        var sw2 = Stopwatch.StartNew();
        StateHasChanged();
        sw2.Stop();
        inj_logger.LogWarning("[性能调试] OnRefreshThis-StateHasChanged 耗时: {ElapsedMs}ms", sw2.ElapsedMilliseconds);
        
        // 通知父组件刷新
        if (OnParentRefresh.HasDelegate)
        {
            var sw3 = Stopwatch.StartNew();
            await OnParentRefresh.InvokeAsync();
            sw3.Stop();
            inj_logger.LogWarning("[性能调试] OnRefreshThis-OnParentRefresh 耗时: {ElapsedMs}ms", sw3.ElapsedMilliseconds);
        }
        
        stopwatch.Stop();
        inj_logger.LogWarning("[性能调试] OnRefreshThis 完成 - 总耗时: {ElapsedMs}ms", stopwatch.ElapsedMilliseconds);
    }

    private void ClickCardHeader(form_card card)
    {
        inj_messageHub.Publish<Guid>(card.id); // 发布聚焦更新通知,Card.Id是当前被选中的卡片
    }

    private async Task HandleCardNameChanged()
    {
        await _context.SaveChangesAsync();
    }

    private async void HandleColumnInsertEvent(column_definition column)
    {
        if (column != null && _isFocusCard)
        {
            // 检查是否已存在相同的问题
            if (Card.form_question.Any(q => q.column_definition_id == column.id))
            {
                inj_snackbar.Add($"当前卡片中已存在题目【{column.display_name}】，不能重复添加。", Severity.Error);
                return;
            }
            
            await inj_cardService.AddQuestionToCardAsync(_context, Card, column);
            await LoadDataAsync();
            // 数据加载完成后需要更新UI
            StateHasChanged();
        }
    }

    private void HandleFocusEvent(Guid focusId)
    {
        var wasFocused = _isFocusCard;
        _isFocusCard = (Card.id == focusId); // 接收聚焦通知，如果当前卡片ID与聚焦ID同，则设置为聚焦状态
        
        // 只有聚焦状态真正改变时才更新UI
        if (wasFocused != _isFocusCard)
        {
            StateHasChanged();
        }
    }

    private async Task AddSubCard()
    {
        await inj_cardService.AddCardToParentCardAsync(_context, Card);
        await _context.SaveChangesAsync();
        await OnRefreshThis();
    }

    private async Task DeleteThisCard()
    {
        // 弹出确认对话框
        var confirm = await inj_dialogService.ShowMessageBox(
            $"确定要删除卡片【{Card.name}】吗？", 
            "", 
            yesText: "确定", 
            cancelText: "取消"
        );

        if (confirm == true)
        {
            //_context.Form_Cards.Attach(Card);
            await inj_cardService.DeleteCardAsync(_context, Card);
            await _context.SaveChangesAsync();
            await OnRefreshThis();
        }
    }

    // 复选框状态改变事件
    private async Task OnStateChanged(CheckboxState state, string value)
    {
        Card.type = state == CheckboxState.Checked ? "multiple" : "default";
        await _context.SaveChangesAsync();
        // 类型变化可能影响UI显示，需要刷新
        StateHasChanged();
    }

    private void ToggleCollapse()
    {
        _isCollapsible = !_isCollapsible;
        // _isCollapsible 已绑定到UI，会自动更新，不需要手动调用 StateHasChanged
    }

    private async Task OnSelectItemChanged(SelectedItem value)
    {
        if (value != null && value.Value != "")
        {
            var oldParent = Card.parent;  // 保存原父卡片引用
            form_card? newParent = null;

            if (value.Value != "0") // 不是移动到根级
            {
                newParent = Card.form.form_card.FirstOrDefault(c => c.id.ToString() == value.Value);
            }
            
            // 调用 CardServices 的方法来处理父卡片变更
            await inj_cardService.MoveCardToAnotherContainerAsync(_context, Card, newParent);
            
            // 刷新当前卡片的视图
            await OnRefreshThis();

            // 通过消息总线通知需要刷新的卡片
            if (oldParent != null)
            {
                inj_messageHub.Publish(new RefreshCardMessage { CardId = oldParent.id });
            }
            if (newParent != null)
            {
                inj_messageHub.Publish(new RefreshCardMessage { CardId = newParent.id });
            }
        }
    }

    private async void HandleRefreshMessage(RefreshCardMessage message)
    {
        if (message.CardId == Card.id)
        {
            await LoadDataAsync();
            // 响应刷新消息时需要更新UI
            StateHasChanged();
        }
    }

    public override async ValueTask DisposeAsync()
    {
        var tokens = new[] { _SubscribeToken, _focusToken, _refreshToken };
        foreach (var token in tokens.Where(t => t != Guid.Empty))
        {
            inj_messageHub.Unsubscribe(token);
            inj_logger.LogInformation($"Card {Card.name}: Unsubscribed from {token}");
        }

        await base.DisposeAsync();
    }

    private IEnumerable<form_card> GetOrderedCards(IEnumerable<form_card> cards)
    {
        // 使用字典优化查找
        var cardDict = cards.ToDictionary(c => c.id);
        var orderedCards = new List<form_card>();
        
        var currentCard = cards.FirstOrDefault(c => c.pre_uid == null);
        while (currentCard != null)
        {
            orderedCards.Add(currentCard);
            currentCard = currentCard.next_uid.HasValue ? 
                cardDict.GetValueOrDefault(currentCard.next_uid.Value) : null;
        }
        
        return orderedCards;
    }

    private void CollectChildCardIds(form_card card, HashSet<Guid> ids)
    {
        if (card == null || !ids.Add(card.id)) return;
        
        foreach (var child in card.Inverseparent)
        {
            CollectChildCardIds(child, ids);
        }
    }

    private string GetCardIndent(form_card card, Func<form_card, bool> isLastChildFunc)
    {
        var indent = "";
        var current = card;
        var parentChain = new List<form_card>();
        
        // 从当前卡片向上收集所有父卡片，形成完整的父链
        while (current.parent_id != null)
        {
            current = current.parent;
            parentChain.Insert(0, current);
        }

        // 为每一层父卡片生成对应的缩进
        for (int i = 0; i < parentChain.Count; i++)
        {
            var parent = parentChain[i];
            bool isParentLast = isLastChildFunc(parent);
            
            // 使用竖线和全角空格，确保每层缩进宽度一致
            indent += isParentLast ? "　　　" : "│　　";
        }

        // 为当前卡片添加连接线
        if (parentChain.Count > 0)
        {
            // 移除最后一个缩进的多余空格
            if (indent.EndsWith("　"))
            {
                indent = indent.Substring(0, indent.Length - 2);
            }
            
            // 使用更精确的连接线组合
            indent += isLastChildFunc(card) ? "└──" : "├──";
        }
        
        return indent;
    }

    private bool IsLastChild(form_card card)
    {
        if (card.parent_id == null)
        {
            // 检查是否是根级最后一个卡片
            return !card.form.form_card
                .Where(c => c.parent_id == null)
                .Any(c => c.pre_uid == card.id);
        }
        else
        {
            // 检查是否是父卡片下的最后一个子卡片
            return card.next_uid == null;  // 使用链表关系来判断是否是最后一个
        }
    }

    private void AddChildCards(form_card parentCard, List<SelectedItem> list, HashSet<Guid> excludeIds)
    {
        var childCards = GetOrderedCards(parentCard.Inverseparent);
        foreach (var childCard in childCards)
        {
            if (!excludeIds.Contains(childCard.id))
            {
                var indent = GetCardIndent(childCard, IsLastChild);
                list.Add(new SelectedItem(childCard.id.ToString(), $"{indent}{childCard.name}"));
                
                // 递归处理子卡片
                AddChildCards(childCard, list, excludeIds);
            }
        }
    }

    private async Task OnParameterChanged<T>(T _)
    {
        await _context.SaveChangesAsync();
    }

    private async Task ExportThisCard()
    {
        // 弹出确认对话框
        var confirm = await inj_dialogService.ShowMessageBox(
            $"确定要导出卡片【{Card.name}】吗？", 
            "导出将包含此卡片及其所有子卡片和问题", 
            yesText: "确定", 
            cancelText: "取消"
        );

        if (confirm == true)
        {
            try
            {
                // 设置导出中状态
                _isExporting = true;
                await InvokeAsync(StateHasChanged);

                inj_logger.LogInformation($"开始导出卡片: {Card.name}");
                inj_snackbar.Add($"正在准备导出卡片【{Card.name}】，请稍候...", Severity.Info);

                // 导出卡片
                var result = await inj_cardExportService.ExportCardAsync(_context, Card);
                
                if (result)
                {
                    inj_snackbar.Add($"卡片【{Card.name}】导出成功，请在弹出的对话框中选择保存位置", Severity.Success);
                }
                else
                {
                    inj_snackbar.Add($"卡片【{Card.name}】导出失败", Severity.Error);
                }
            }
            catch (Exception ex)
            {
                inj_logger.LogError(ex, $"导出卡片失败: {Card.id}");
                inj_snackbar.Add($"导出卡片时发生错误: {ex.Message}", Severity.Error);
            }
            finally
            {
                // 重置导出状态
                _isExporting = false;
                await InvokeAsync(StateHasChanged);
            }
        }
    }

    private async Task ImportCard()
    {
        // 弹出确认对话框
        var confirm = await inj_dialogService.ShowMessageBox(
            $"确定要当前位置导入卡片吗？",
            "", 
            yesText: "确定", 
            cancelText: "取消"
        );

        if (confirm == true)
        {
            try
            {
                // 触发隐藏的文件上传控件
                await inj_jsRuntime.InvokeVoidAsync("domInterop.clickElement", $"fileUpload-{Card.id}");
            }
            catch (Exception ex)
            {
                inj_logger.LogError(ex, "触发文件选择对话框失败");
                inj_snackbar.Add("打开文件选择对话框失败，请重试", Severity.Error);
            }
        }
    }

    private async Task HandleFileSelected(InputFileChangeEventArgs e)
    {
        try
        {
            if (e.FileCount == 0) return;

            var file = e.File;
            _isImporting = true;
            await InvokeAsync(StateHasChanged);

            inj_logger.LogInformation($"准备导入文件: {file.Name}, 大小: {file.Size} 字节");
            inj_snackbar.Add($"正在读取文件：{file.Name}", Severity.Info);

            // 读取文件内容
            using var stream = file.OpenReadStream(maxAllowedSize: 1024 * 1024 * 10); // 允许最大10MB
            using var reader = new StreamReader(stream);
            var jsonContent = await reader.ReadToEndAsync();

            if (string.IsNullOrWhiteSpace(jsonContent))
            {
                inj_snackbar.Add("导入失败：文件内容为空", Severity.Error);
                _isImporting = false;
                await InvokeAsync(StateHasChanged);
                return;
            }

            // 调用导入服务
            inj_snackbar.Add($"正在导入卡片...", Severity.Info);
            var importResult = await inj_cardImportService.ImportCardFromJsonAsync(_context, Card.form, Card.id, jsonContent);

            if (importResult.Success)
            {
                inj_snackbar.Add($"导入成功：{importResult.Message}", Severity.Success);
                // 刷新当前卡片
                await OnRefreshThis();
            }
            else
            {
                inj_snackbar.Add($"导入失败：{importResult.Message}", Severity.Error);
            }
        }
        catch (Exception ex)
        {
            inj_logger.LogError(ex, "导入文件过程中发生错误");
            inj_snackbar.Add($"导入过程中发生错误：{ex.Message}", Severity.Error);
        }
        finally
        {
            _isImporting = false;
            await InvokeAsync(StateHasChanged);
        }
    }
}
    