{"ConnectionStrings": {"DefaultConnection": "Host=*************:31785;Database=postgres;Username=postgres;Password=*********;Include Error Detail=true;", "DefaultConnectionLocal": "Host=**********:5432;Database=postgres;Username=postgres;Password=*********"}, "OpenRouter": {"ApiKey": "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", "BaseUrl": "https://openrouter.ai/api/v1"}, "Ollama": {"Uri": "http://***********:11434", "DefaultModel": "qwen3:8b-q8_0"}, "AllowedHosts": "*"}