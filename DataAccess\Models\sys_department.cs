﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace DataAccess.Models;

public partial class sys_department
{
    /// <summary>
    /// 科室ID
    /// </summary>
    public Guid id { get; set; }

    /// <summary>
    /// 医院ID
    /// </summary>
    public Guid hospital_id { get; set; }

    /// <summary>
    /// 科室名称
    /// </summary>
    public string name { get; set; }

    /// <summary>
    /// 科室显示名称
    /// </summary>
    public string display_name { get; set; }

    public virtual ICollection<form_card> form_card { get; set; } = new List<form_card>();

    public virtual ICollection<form_form> form_form { get; set; } = new List<form_form>();

    public virtual ICollection<form_form_set> form_form_set { get; set; } = new List<form_form_set>();

    public virtual ICollection<form_project> form_project { get; set; } = new List<form_project>();

    public virtual ICollection<form_question> form_question { get; set; } = new List<form_question>();

    public virtual sys_hospital hospital { get; set; }

    public virtual ICollection<sys_ward> sys_ward { get; set; } = new List<sys_ward>();
}