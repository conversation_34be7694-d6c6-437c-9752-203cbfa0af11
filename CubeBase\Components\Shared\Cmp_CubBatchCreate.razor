﻿@inherits Cmp_Base
@inject ILogger<Cmp_CubBatchCreate> inj_logger

<div class="form-wrapper">
    <MudText Typo="Typo.h5" Class="pa-2">批量处理 - @_parentTableEntity?.display_name</MudText>
    <MudTextField @bind-Value="_projectName" Label="项目名称" Variant="Variant.Outlined" Class="mb-3" />
    <MudTextField @bind-Value="_formsetName" Label="表单集名称" Variant="Variant.Outlined" Class="mb-3" />    
    
    <div class="scrollable-form">
        <!-- 待处理文本输入区域 -->
        <MudTextField @bind-Value="_inputText" Label="待处理文本" Variant="Variant.Outlined" Lines="5" Class="mb-3" />
        <div class="d-flex mb-3">
            <MudButton Variant="Variant.Filled" Color="Color.Primary" OnClick="ProcessTextAsync" Class="me-2">数据识别</MudButton>
            <MudButton Variant="Variant.Filled" Color="Color.Info" OnClick="StartCategorizationAnalysisAsync">分类分析</MudButton>
        </div>
        <!-- 格式化结果输出 -->
        @if (!string.IsNullOrWhiteSpace(_aiAnswer1))
        {
            <MudPaper Elevation="2" Class="pa-3 mt-3">
                <MudMarkdown Value="@_aiAnswer1" />
            </MudPaper>
        }
        <!-- 分类分析结果输入框 -->
        @if (!string.IsNullOrWhiteSpace(_aiAnswer2))
        {
            <MudPaper Elevation="2" Class="pa-3 mt-3">
                <MudMarkdown Value="@_aiAnswer2" />
            </MudPaper>
        }
        <!-- SQL脚本输出区域 -->
    </div>
</div>

@code {
    [Parameter]
    public required Guid TableId { get; set; }
    [Parameter]
    public EventCallback OnSuccess { get; set; }

    private string _projectName = string.Empty;
    private string _formsetName = string.Empty;
    private string _inputText = string.Empty;
    private string _aiAnswer1 = string.Empty;
    private string _aiAnswer2 = string.Empty;
    private List<table_definition> _tableList = new();
    private table_definition? _parentTableEntity;

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();
    }

    protected override async Task OnParametersSetAsync()
    {
        if (TableId == Guid.Empty)
        {
            var errorMessage = "错误：TableId 参数无效，无法加载数据。请确保从父组件正确传递了 TableId。";
            inj_logger.LogError(errorMessage + " (TableId: {TableId})", TableId);
            _aiAnswer1 = errorMessage; // Display error in the UI
            inj_snackbar.Add("页面参数错误，无法加载！", Severity.Error);
            StateHasChanged();
            return;
        }

        bool initialLoadSuccess = false;
        try
        {
            await LoadDataAsync();
            initialLoadSuccess = true;
        }
        catch (Exception ex)
        {
            inj_logger.LogError(ex, "加载批量创建组件的初始数据时发生错误，TableId: {TableId}", TableId);
            _aiAnswer1 = $"加载页面数据时出错: {ex.Message}";
            inj_snackbar.Add("加载页面基础数据失败！", Severity.Error);
        }
        finally
        {
            // No longer calling StateHasChanged() here as LoadDataAsync will call it if it completes successfully
            // or OnParametersSetAsync will call it if an error occurs during LoadDataAsync or initial TableId check.
            if (initialLoadSuccess)
            {
                //Data loaded successfully, _parentTableEntity should be set.
                //StateHasChanged(); // Already called by LoadDataAsync or error path
            }
            else if (TableId != Guid.Empty)
            {
                //This means LoadDataAsync failed, and error message is in _aiAnswer1
                StateHasChanged(); //Ensure UI updates with error message
            }
        }
    }

    private async Task LoadDataAsync()
    {
        StateHasChanged(); // Update UI to show loading and clear previous answers

        try
        {
            using var context = await ContextFactory.CreateDbContextAsync();

            _tableList = await context.table_definition
                .AsNoTracking()
                .Where(t => t.is_valid)
                .ToListAsync();

            _parentTableEntity = await context.table_definition
                .AsNoTracking()
                .FirstOrDefaultAsync(t => t.id == TableId && t.is_valid)
                ?? throw new Exception($"找不到指定的表 (ID: {TableId})");
        }
        catch
        {
            StateHasChanged(); // Update UI to stop loading
            throw; // Re-throw to be caught by OnParametersSetAsync
        }
        StateHasChanged(); // Update UI after successful load
    }

    private async Task ProcessTextAsync()
    {
        if (TableId == Guid.Empty)
        {
            inj_snackbar.Add("TableId无效，无法处理文本。", Severity.Error);
            return;
        }
        if (string.IsNullOrWhiteSpace(_inputText))
        {
            inj_snackbar.Add("请输入待处理的文本。", Severity.Warning);
            return;
        }

        _aiAnswer1 = "正在分析中，请稍候...";
        await InvokeAsync(StateHasChanged);

        var responseBuilder = new System.Text.StringBuilder();
        bool parsingErrorOccurred = false;

        string systemPrompt = @"
你是一个数据预处理和验证助手。
你的任务是逐行解析用户提供的文本。每一行代表一个表单项目，格式为'表单名称 卡片/复选卡片名称 字段名称 数据类型 备选项1###备选项2...'。
你需要：
1. 解析出每一行的表单名称、卡片/复选卡片名称、字段名称、数据类型和备选项。
2. 验证数据类型是否为以下之一：卡片、复选卡片、单选、多选、整数、小数、文字、日期、布尔、文件。如果不是，请明确指出错误。
3. 如果数据类型是'单选'、'多选'，但没有提供备选项，请给出警告。
4. **重要：如果任何一行文本的格式让你完全无法解析其基本结构（例如，缺少必要的分隔符或关键信息不完整），请立即停止处理，并输出一条特定的错误消息：'错误：输入文本在第 X 行存在严重格式问题，无法继续解析。请修正后重试。'（将 X 替换为实际行号）**

输出格式严格遵守csv格式，示例：
行号,表单名称,卡片/复选卡片名称,项目名称,数据类型,备选项
1,表单名称1,卡片名称1,项目名称1,卡片,备选项1###备选项2
2,表单名称2,复选卡片名称2,项目名称2,复选卡片,备选项1###备选项2
3,表单名称3,卡片名称3,项目名称3,单选,备选项1###备选项2
4,表单名称4,复选卡片名称4,项目名称4,多选,备选项1###备选项2
5,表单名称5,卡片名称5,项目名称5,整数,
6,表单名称6,卡片名称6,项目名称6,小数,
7,表单名称7,卡片名称7,项目名称7,文字,
---
";

        try
        {
            await foreach (var streamResult in BioAI.ThinkStreamAsync(
                prompt: _inputText,
                model: LLModel.OpenRouter.Gemini_25_Flash_05_20,
                systemPrompt: systemPrompt,
                onTokenReceived: async token =>
                {
                    if (parsingErrorOccurred) return;

                    responseBuilder.Append(token.Content);
                    _aiAnswer1 = responseBuilder.ToString();

                    if (_aiAnswer1.Contains("错误：输入文本在第") && _aiAnswer1.Contains("存在严重格式问题，无法继续解析。"))
                    {
                        parsingErrorOccurred = true;
                    }
                    await InvokeAsync(StateHasChanged);
                }))
            {
                // 流式处理完成
            }

            if (parsingErrorOccurred)
            {
                inj_logger.LogWarning("LLM报告输入格式错误，分析已中断。完整响应: {AIResponse}", _aiAnswer1);
                inj_snackbar.Add("输入文本存在格式问题导致分析中断，请检查输出详情并修正。", Severity.Error, options => { options.VisibleStateDuration = 10000; });
            }
            else
            {
                inj_snackbar.Add("文本初步分析完成。", Severity.Success);
            }
        }
        catch (Exception ex)
        {
            inj_logger.LogError(ex, "调用LLM进行文本分析时发生严重错误。");
            _aiAnswer1 = $"分析过程中发生严重错误: {ex.Message} - {ex.StackTrace}";
            inj_snackbar.Add("分析过程中发生严重错误，请查看详情或联系技术支持。", Severity.Error, options => { options.VisibleStateDuration = 10000; });
        }
        finally
        {
            await InvokeAsync(StateHasChanged);
        }
    }

    private async Task StartCategorizationAnalysisAsync()
    {
        if (TableId == Guid.Empty)
        {
            inj_snackbar.Add("TableId无效，无法进行分类分析。", Severity.Error);
            return;
        }
        if (string.IsNullOrWhiteSpace(_aiAnswer1) || _aiAnswer1.StartsWith("错误：") || _aiAnswer1.StartsWith("加载页面数据时出错:"))
        {
            inj_snackbar.Add("请先成功完成数据验证步骤并获得有效的初步分析结果。", Severity.Warning);
            return;
        }

        _aiAnswer2 = "正在进行分类分析，请稍候...";
        await InvokeAsync(StateHasChanged);

        var responseBuilder = new System.Text.StringBuilder();
        bool analysisErrorOccurred = false;

        // Using C# 11+ raw string literal feature for multiline strings.
        // Ensure your project's LangVersion is set appropriately (e.g., to 11.0 or later).
        string systemPrompt = @"
你是一位资深的临床医学和Clinical Informatics、Public Health Informatics专家。
你的任务是分析一系列医学词典条目，并根据一个已有的层级化医学数据字典结构，为它们推荐最合适的归属分类。

# 输入数据说明:
1.  **全节点数据字典结构**: 这是一个基于缩进的层级文本（每级4个空格）。
    *   以 `V:` 开头的行代表 **分类节点 (Visual)**，格式为 `V:unique_name|display_name|full_display_path`。
    *   以 `T:(guid)` 开头的行代表 **表节点 (Table)**，括号中包含其全局唯一ID (GUID)，格式为 `T:(guid)unique_name|display_name|full_display_path`。
    *   `full_display_path` 是从根节点到当前节点的完整中文显示路径。
2.  **待分类的医学词典项目列表**: 这是由前一AI步骤生成的Markdown格式文本，其中包含每个项目的原文、解析的中文名称、数据类型和备选项（如果适用）等信息。你需要理解这些信息以进行分析。

# 任务指令:
对于'待分类的医学词典项目列表'中的 **每一个项目**：
1.  仔细分析其'中文名称'和可能的'数据类型'、'备选项'等信息，理解其临床医学意义。
2.  对照提供的'全节点数据字典结构'，为其判断最合适的归属位置。
3.  **给出建议**：
    *   **规则1：只能归入现有表节点 (`T:`)**：如果该项目适合作为现有某个 **表节点 (`T:`)** 的一部分或扩展。
        *   输出格式：`建议归属到表：根节点 > 二级节点 > 三级节点 > ... > 表节点的display_name (ID: 表节点ID)`
    *   **规则2：新节点只能是表节点 (`T:`)，且父节点必须是分类节点 (`V:`)**：如果该项目代表一个全新的概念，需要创建新的 **表节点 (`T:`)**。这个新的表节点 **必须** 创建在某个已存在的 **分类节点 (`V:`)** 之下。新表节点不能创建在另一个表节点 (`T:`) 之下。
        *   指明父级分类节点，输出格式：`建议在分类：根节点 > 二级节点 > 三级节点 > ... > 父分类节点的display_name (ID: 父分类节点ID) 下创建新表`
        *   给出新表的建议显示名称，输出格式：`新表建议名称：[新表的中文显示名称]`
4.  **提供原因解释**：对于你的每一个建议（无论是归属到现有表还是创建新表），都必须提供详尽的'原因解释'，阐述为何这是最佳方案。解释应基于临床医学和医学信息学原理，清晰明了。

# 期望的输出格式 (针对每个条目):
```markdown
**条目：** [此处为_aiAnswer1中对应条目的原始中文名称，通常在 Markdown 的 `**输入 (行 N):**` 后的原文内容中提取]

**分析结果：**
*   **建议：** [LLM的归属建议，必须包含分类的ID和父节点信息，从根节点开始，依次输出，格式为：根节点 > 二级节点 > 三级节点 > ... > 分类名称 (ID: 节点ID)]
*   **原因解释：** [LLM详细的专业解释]
---
```
请严格遵循上述指令和输出格式。
";

        try
        {
            using var context = await ContextFactory.CreateDbContextAsync();
            string allNodesDictionaryString = await inj_medDictService.ExportAllNodesDataDictionaryAsync(context);

            string userPrompt = $@"
# 全节点数据字典结构:
{allNodesDictionaryString}

# 待分类的医学词典项目列表:
{_aiAnswer1}
";

            await foreach (var streamResult in BioAI.ThinkStreamAsync(
                prompt: userPrompt,
                model: LLModel.OpenRouter.Gemini_25_Flash_05_20,
                systemPrompt: systemPrompt,
                onTokenReceived: async token =>
                {
                    if (analysisErrorOccurred) return;

                    responseBuilder.Append(token.Content);
                    _aiAnswer2 = responseBuilder.ToString();

                    if (_aiAnswer2.Contains("关键错误：无法继续分析"))
                    {
                        analysisErrorOccurred = true;
                        inj_logger.LogWarning("LLM报告分类分析时遇到无法处理的问题。");
                    }
                    await InvokeAsync(StateHasChanged);
                }))
            {
                // 流式处理完成
            }

            if (analysisErrorOccurred)
            {
                inj_snackbar.Add("分类分析过程中LLM报告了错误，请检查输出详情。", Severity.Warning, options => { options.VisibleStateDuration = 10000; });
            }
            else
            {
                inj_snackbar.Add("分类分析完成。", Severity.Success);
                await OnSuccess.InvokeAsync();
            }
        }
        catch (Exception ex)
        {
            analysisErrorOccurred = true;
            inj_logger.LogError(ex, "调用LLM进行分类分析时发生严重错误。");
            _aiAnswer2 = $"分类分析过程中发生严重错误: {ex.Message} - {ex.StackTrace}";
            inj_snackbar.Add("分类分析过程中发生严重错误，请联系技术支持。", Severity.Error, options => { options.VisibleStateDuration = 10000; });
        }
        finally
        {
            await InvokeAsync(StateHasChanged);
        }
    }
}
