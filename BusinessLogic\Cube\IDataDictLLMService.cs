using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using DataAccess.Models;
using Microsoft.Extensions.Logging;

namespace BusinessLogic.Cube;

/// <summary>
/// 数据字典LLM分析服务接口
/// </summary>
public interface IDataDictLLMService
{
    /// <summary>
    /// LLM语义分析结果类
    /// </summary>
    public class LlmSemanticAnalysisResult
    {
        public bool IsProcessed { get; set; } = false; // 标记：此项是否已经过LLM分析
        public bool MatchFound { get; set; } // 如果找到语义和类型兼容的匹配项，则为true
        public bool PotentialMatchTypeMismatch { get; set; } // 如果名称匹配但类型不同，则为true
        public string? MatchedExistingItemId { get; set; } // 匹配到的现有项ID
        public string? MatchedExistingItemUniqueName { get; set; } // 匹配到的现有项唯一名称
        public string? MatchedExistingItemDisplayName { get; set; } // 匹配到的现有项显示名称
        public string? MatchedExistingItemType { get; set; } // 匹配到的现有项类型
        public string Reasoning { get; set; } = string.Empty; // LLM的文本解释/提示
        public string ExistingCategoryDataAsText { get; set; } = string.Empty; // 来自ExportSubtreeDataDictionaryAsync的完整文本
        public string ErrorMessage { get; set; } = string.Empty; // 存储此项LLM处理过程中的任何错误
        public string SuggestedEnglishName { get; set; } = string.Empty; // 新增：建议的英文字段名
        public string SuggestedChineseName { get; set; } = string.Empty; // 新增：建议的中文字段名
    }

    /// <summary>
    /// 表单数据项类
    /// </summary>
    public class ParsedDataItem
    {
        public string Id { get; set; } = string.Empty;
        public string DisplayName { get; set; } = string.Empty;
        public string ItemType { get; set; } = string.Empty;
        public List<string> Attributes { get; set; } = new List<string>();
        public string MatchResult { get; set; } = string.Empty; // 匹配结果属性，用于显示在第五列
        public string MatchType { get; set; } = string.Empty; // 匹配类型：MATCH_T, SUGGEST_NEW_T, REQUIRE_T
        public string CategoryId { get; set; } = string.Empty; // 数据字典分类ID
        public LlmSemanticAnalysisResult? LlmAnalysis { get; set; } // LLM分析结果
        public bool IsSelected { get; set; } = false; // 复选框选中状态
    }

    /// <summary>
    /// 使用LLM分析表单字段是否在数据字典中已存在
    /// </summary>
    /// <param name="itemsToAnalyze">需要分析的表单字段列表</param>
    /// <param name="logger">日志记录器</param>
    /// <returns>分析后的表单字段列表</returns>
    Task<List<ParsedDataItem>> RunLlmFieldAnalysisAsync(List<ParsedDataItem> itemsToAnalyze, List<ParsedDataItem> allItems, ILogger logger);

    /// <summary>
    /// 清理显示名称，移除空格和连接符号
    /// </summary>
    /// <param name="displayName">显示名称</param>
    /// <returns>清理后的显示名称</returns>
    string CleanDisplayName(string displayName);

    /// <summary>
    /// 解析CSV格式的LLM分析结果
    /// </summary>
    /// <param name="csvText">CSV格式文本</param>
    /// <param name="logger">日志记录器</param>
    /// <returns>解析后的LLM分析结果列表</returns>
    List<LlmSingleFieldAnalysisResponse> ParseCsvResults(string csvText, ILogger logger);

    /// <summary>
    /// LLM分析单个字段结果的响应类，用于JSON反序列化
    /// </summary>
    public class LlmSingleFieldAnalysisResponse
    {
        public string parsed_data_item_id { get; set; } = string.Empty;
        public string new_field_name { get; set; } = string.Empty;
        public string new_field_type { get; set; } = string.Empty;
        public bool match_found { get; set; }
        public bool potential_match_type_mismatch { get; set; }
        public string? matched_existing_item_id { get; set; } // 新增：匹配到的字段ID
        public string? matched_existing_item_unique_name { get; set; }
        public string? matched_existing_item_display_name { get; set; }
        public string? matched_existing_item_type { get; set; }
        public string reasoning { get; set; } = string.Empty;
        public string suggested_english_name { get; set; } = string.Empty; // 新增：建议的英文字段名
        public string suggested_chinese_name { get; set; } = string.Empty; // 新增：建议的中文字段名
    }
}


