// 文件操作JavaScript互操作
window.fileDownloadInterop = {
    downloadFromContent: function (content, fileName, contentType) {
        var a = document.createElement("a");
        var file = new Blob([content], { type: contentType });
        a.href = URL.createObjectURL(file);
        a.download = fileName;
        a.click();
        return true;
    },
    
    downloadWithPrompt: async function (content, defaultFileName, contentType) {
        try {
            // 创建Blob对象
            const blob = new Blob([content], { type: contentType });
            
            // 检查是否支持现代文件系统API
            if (window.showSaveFilePicker) {
                try {
                    const options = {
                        suggestedName: defaultFileName,
                        types: [{
                            description: '导出文件',
                            accept: { [contentType]: ['.json'] }
                        }]
                    };
                    
                    // 显示保存文件对话框
                    const fileHandle = await window.showSaveFilePicker(options);
                    
                    // 获取可写流
                    const writable = await fileHandle.createWritable();
                    
                    // 写入内容
                    await writable.write(blob);
                    
                    // 关闭流
                    await writable.close();
                    
                    console.log("使用现代文件系统API保存成功");
                    return true;
                } catch (e) {
                    console.error("文件系统API保存失败，回退到传统方法:", e);
                    // 如果用户取消或API失败，回退到传统方法
                }
            }
            
            // 回退方法：使用prompt获取文件名
            const userFileName = prompt("请输入保存的文件名:", defaultFileName);
            
            // 如果用户取消，则返回false
            if (userFileName === null) return false;
            
            // 使用用户输入的文件名或默认文件名
            const fileName = userFileName || defaultFileName;
            
            // 创建下载链接
            var a = document.createElement("a");
            a.href = URL.createObjectURL(blob);
            a.download = fileName;
            
            // 触发下载
            a.click();
            
            // 清理
            setTimeout(function() {
                URL.revokeObjectURL(a.href);
            }, 100);
            
            return true;
        } catch (e) {
            console.error("文件下载失败:", e);
            return false;
        }
    }
};

// 获取DOM元素并点击
window.domInterop = {
    clickElement: function (elementId) {
        var element = document.getElementById(elementId);
        if (element) {
            element.click();
            return true;
        }
        return false;
    }
}; 