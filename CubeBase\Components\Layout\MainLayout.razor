﻿@inherits LayoutComponentBase

@* Required *@
<MudThemeProvider />
<MudPopoverProvider />

@* Needed for dialogs *@
<MudDialogProvider />

@* Needed for snackbars *@
<MudSnackbarProvider />


<MudLayout>
    <MudAppBar style="background-color:#2f9688"> <MudText Typo="Typo.h6">CubeBase</MudText></MudAppBar>

    <MudDrawer Width="160px" Elevation="1" ClipMode="DrawerClipMode.Always" Open="true"
               Variant="DrawerVariant.Mini" Style="background:linear-gradient(180deg, #2f9688 0%, #bcebdc 70%);">

    <NavMenu />
    </MudDrawer>

    <MudMainContent>
        @Body
    </MudMainContent>
</MudLayout>




@* <div class="page"> *@
@*     <div class="sidebar"> *@
@*         <NavMenu /> *@
@*     </div> *@

@*     <main> *@
@*         <div class="top-row px-4"> *@
@*             <a href="https://learn.microsoft.com/aspnet/core/" target="_blank">About</a> *@
@*         </div> *@

@*         <article class="content px-0"> *@
@*             @Body *@
@*         </article> *@
@*     </main> *@
@* </div> *@

@* <div id="blazor-error-ui"> *@
@*     An unhandled error has occurred. *@
@*     <a href="" class="reload">Reload</a> *@
@*     <a class="dismiss">🗙</a> *@
@* </div> *@
