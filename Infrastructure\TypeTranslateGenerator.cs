﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Infrastructure
{
    public class TypeTranslateGenerator
    {
        /// <summary>将ItemType转换为数据类型</summary>
        public static string ConvertQuestionDataType(string itemType) => itemType?.Trim().ToLower() switch
        {
            "整数" or "小数" => "数值",
            "文本" or "长文本" => "文本",
            "文件" => "文件",
            "日期" => "日期",
            "单选" or "多选" => "选择",
            _ => "文本" // 默认为文本类型
        };
    }
}
