﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    <UserSecretsId>************************************</UserSecretsId>
  </PropertyGroup>

  <PropertyGroup>
    <EmitCompilerGeneratedFiles>true</EmitCompilerGeneratedFiles>
    <CompilerGeneratedFilesOutputPath>DesiredPathForGeneratedStuff</CompilerGeneratedFilesOutputPath>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="DesiredPathForGeneratedStuff\**" />
    <Content Remove="DesiredPathForGeneratedStuff\**" />
    <EmbeddedResource Remove="DesiredPathForGeneratedStuff\**" />
    <None Remove="DesiredPathForGeneratedStuff\**" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="AutoMapper" Version="13.0.1" />
    <PackageReference Include="BitzArt.Blazor.Auth.Server" Version="1.0.2" />
    <PackageReference Include="BootstrapBlazor" Version="8.11.0" />
    <PackageReference Include="BootstrapBlazor.BootstrapIcon" Version="8.0.3" />
    <PackageReference Include="BootstrapBlazor.CherryMarkdown" Version="8.2.0" />
    <PackageReference Include="BootstrapBlazor.FontAwesome" Version="8.2.0" />
    <PackageReference Include="BootstrapBlazor.Markdown" Version="8.2.1" />
    <PackageReference Include="BootstrapBlazor.MaterialDesign" Version="8.1.0" />
    <PackageReference Include="Easy.MessageHub" Version="5.1.0" />
    <PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="8.0.10" />
    <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.21.0" />
    <PackageReference Include="MudBlazor" Version="8.6.0" />
    <PackageReference Include="MudBlazor.Markdown" Version="8.6.0" />
    <PackageReference Include="NLog.Web.AspNetCore" Version="5.3.14" />
    <PackageReference Include="Npgsql" Version="8.0.6" />
    <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="8.0.11" />
    <PackageReference Include="Microsoft.AspNetCore.Diagnostics.EntityFrameworkCore" Version="8.0.10" />
    <PackageReference Include="System.Text.Json" Version="8.0.5" />
    <PackageReference Include="Tizzani.MudBlazor.HtmlEditor" Version="2.2.1" />
    <PackageReference Include="Bio.AI" Version="0.1.526.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\BusinessLogic\BusinessLogic.csproj" />
    <ProjectReference Include="..\DataAccess\DataAccess.csproj" />
    <ProjectReference Include="..\Infrastructure\Infrastructure.csproj" />
  </ItemGroup>

  <ItemGroup>
    <None Update="TempFiles\list1.txt">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="TempFiles\list2.txt">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
  </ItemGroup>

</Project>
