﻿@page "/Home"
@using System.Text.Json
@inject IUserService UserService
@inject NavigationManager NavigationManager

<PageTitle>Home</PageTitle>

<h1>Hello, world!</h1>


<div>
    <Button Type="primary" OnClick="SignOutAsync">Logout</Button>
</div>

@code {

    private async Task SignOutAsync()
    {
        await UserService.SignOutAsync();
        NavigationManager.NavigateTo("/", true);
    }
}

<AuthorizeView>
    @* <div style="width:60rem;word-wrap:break-word;"> *@
    @*     <pre> *@
    @*     @JsonSerializer.Serialize(@context.User.Claims.Select(x => new *@
    @*             { x.Type, x.Value, x.Issuer, x.Subject?.Name, x.ValueType }), *@
    @*                          new JsonSerializerOptions { WriteIndented = true }) *@
    @*     </pre> *@
    @* </div> *@
</AuthorizeView>